/**
 * R1系统 - 系统监控模块
 * 实现日志记录、性能监控、错误追踪等功能
 */

class SystemMonitor extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'SystemMonitor');

    // 硬件监控配置
    this.hardwareConfig = {
      enabled: true,              // 是否启用硬件监控
      fallbackToSimulation: true, // 硬件失败时是否降级到模拟
      updateInterval: 5000,       // 更新间隔(ms)
      timeout: 3000              // 硬件请求超时(ms)
    };

    // 系统监控特有属性
    this.logs = [];
    this.performanceMetrics = {
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      avgResponseTime: 0,
      lastUpdate: Date.now()
    };
    this.maxLogs = 1000;
    this.logLevels = {
      ERROR: { name: 'ERROR', color: '#ff4757', icon: '❌' },
      WARN: { name: 'WARN', color: '#ffa502', icon: '⚠️' },
      INFO: { name: 'INFO', color: '#3742fa', icon: 'ℹ️' },
      SUCCESS: { name: 'SUCCESS', color: '#2ed573', icon: '✅' },
      DEBUG: { name: 'DEBUG', color: '#747d8c', icon: '🔍' }
    };
  }

  async setupEventListeners() {
    // 监听所有系统事件进行日志记录
    this.eventBus.on('system.error', (data) => {
      this.addLog('ERROR', '系统错误', data.error || data.message);
    });

    this.eventBus.on('module.error', (data) => {
      this.addLog('ERROR', `模块错误: ${data.moduleName}`, data.error);
    });

    this.eventBus.on('module.success', (data) => {
      this.addLog('SUCCESS', `模块操作: ${data.moduleName}`, data.message);
    });

    this.eventBus.on('esp32.connected', () => {
      this.addLog('SUCCESS', 'ESP32连接', 'ESP32设备连接成功');
    });

    this.eventBus.on('esp32.disconnected', () => {
      this.addLog('WARN', 'ESP32断开', 'ESP32设备连接断开');
    });

    this.eventBus.on('esp32.error', (data) => {
      this.addLog('ERROR', 'ESP32错误', data.error);
    });

    this.eventBus.on('esp32.request.success', (data) => {
      this.performanceMetrics.requestCount++;
      this.performanceMetrics.successCount++;
      this.updatePerformanceMetrics();
    });

    this.eventBus.on('esp32.request.error', (data) => {
      this.performanceMetrics.requestCount++;
      this.performanceMetrics.errorCount++;
      this.addLog('ERROR', 'API请求失败', `${data.endpoint}: ${data.error}`);
      this.updatePerformanceMetrics();
    });

    this.eventBus.on('signal.learned', (data) => {
      this.addLog('SUCCESS', '信号学习', `信号学习成功: ${data.signal?.name}`);
    });

    this.eventBus.on('signal.sent', (data) => {
      if (data.success) {
        this.addLog('INFO', '信号发射', `信号发射成功: ${data.signal?.name}`);
      } else {
        this.addLog('ERROR', '信号发射失败', `${data.signal?.name}: ${data.error?.message}`);
      }
    });

    // ==================== 控制模块事件监听 - 核心执行状态 ====================
    // 控制模块是所有发射任务的统一执行者，应该重点监控

    this.eventBus.on('control.emit.started', (data) => {
      console.log('🔍 SystemMonitor: 收到 control.emit.started 事件，原始数据:', data);

      // ✅ 根源修复：现在BaseModule.emitEvent不再包装数据，直接使用
      const task = data.task || data;

      // 更智能的任务类型识别
      let taskType = task?.type || data?.type;
      if (!taskType) {
        // 根据任务名称推断类型
        if (task?.name?.includes('定时') || task?.name?.includes('timer')) {
          taskType = 'timer';
        } else if (task?.name?.includes('all') || task?.name?.includes('全部')) {
          taskType = 'all';
        } else if (task?.name?.includes('selected') || task?.name?.includes('选中')) {
          taskType = 'selected';
        } else {
          taskType = 'unknown';
        }
      }

      // 更智能的任务名称获取
      let taskName = task?.name || data?.name;
      if (!taskName) {
        taskName = taskType === 'timer' ? '定时任务' :
                  taskType === 'all' ? '全部信号任务' :
                  taskType === 'selected' ? '选中信号任务' : '未知任务';
      }

      // 信号数量获取
      const signalsCount = task?.signals?.length ||
                          data?.signals?.length ||
                          task?.signalCount ||
                          data?.signalCount || 0;

      // ✅ 修复循环/单次模式检测 - 检查控制模块的实际状态
      // 从调试报告看，控制模块在任务执行时isLoopMode=true，但事件数据中可能没有传递
      // 需要从控制模块的当前状态或任务配置中获取
      let isLoopMode = false;

      // 方法1：从任务数据中获取
      if (task?.isLoopMode !== undefined) {
        isLoopMode = task.isLoopMode;
      }
      // 方法2：从事件数据中获取
      else if (data?.isLoopMode !== undefined) {
        isLoopMode = data.isLoopMode;
      }
      // 方法3：通过事件系统查询控制模块当前状态
      else {
        // 发送查询请求给控制模块获取当前循环模式状态
        this.emitEvent('control.state.request', {
          callback: (controlState) => {
            if (controlState?.isLoopMode !== undefined) {
              isLoopMode = controlState.isLoopMode;
            }
          }
        });
      }

      const modeText = isLoopMode ? '循环' : '单次';

      console.log('🔍 SystemMonitor: 解析后的任务信息:', {
        taskType,
        taskName,
        taskId: task?.id || data?.id,
        signalsCount,
        isLoopMode,
        modeText,
        rawEventData: data
      });

      try {
        this.addLog('INFO', '任务执行', `开始执行: ${taskName} (${taskType}) - ${signalsCount}个信号 [${modeText}]`);
      } catch (error) {
        console.error('SystemMonitor: addLog调用失败:', error);
      }
    });

    // 移除进度事件监听 - 减少冗余日志
    // 只在任务开始和结束时记录，不记录每个信号的进度
    // this.eventBus.on('control.emit.progress', ...)

    this.eventBus.on('control.emit.completed', (data) => {
      const task = data.task || data;

      // 增强的任务名称获取
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        const taskType = task?.type || data?.type;
        taskName = taskType === 'timer' ? '定时任务' :
                  taskType === 'all' ? '全部信号任务' :
                  taskType === 'selected' ? '选中信号任务' : '任务';
      }

      const duration = data.duration ? `耗时${Math.round(data.duration/1000)}秒` : '';
      const totalSignals = task?.signals?.length || data?.signals?.length || task?.signalCount || 0;
      const emitCount = task?.emitCount || data?.emitCount || 0;

      // ✅ 修复循环/单次模式检测 - 与任务开始事件保持一致
      let isLoopMode = false;

      if (task?.isLoopMode !== undefined) {
        isLoopMode = task.isLoopMode;
      } else if (data?.isLoopMode !== undefined) {
        isLoopMode = data.isLoopMode;
      }

      const modeText = isLoopMode ? '循环' : '单次';

      // 记录详细的完成统计 - 重点关注任务整体执行情况
      try {
        this.addLog('SUCCESS', '任务完成', `${taskName}执行完成 - ${totalSignals}个信号，发射${emitCount}次 [${modeText}] ${duration}`);
      } catch (error) {
        console.error('SystemMonitor: addLog（任务完成）调用失败:', error);
      }
    });

    this.eventBus.on('control.emit.paused', (data) => {
      const task = data.task || data;
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        const taskType = task?.type || data?.type;
        taskName = taskType === 'timer' ? '定时任务' :
                  taskType === 'all' ? '全部信号任务' :
                  taskType === 'selected' ? '选中信号任务' : '任务';
      }
      this.addLog('WARN', '任务暂停', `任务已暂停: ${taskName}`);
    });

    this.eventBus.on('control.emit.resumed', (data) => {
      const task = data.task || data;
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        const taskType = task?.type || data?.type;
        taskName = taskType === 'timer' ? '定时任务' :
                  taskType === 'all' ? '全部信号任务' :
                  taskType === 'selected' ? '选中信号任务' : '任务';
      }
      this.addLog('INFO', '任务恢复', `任务已恢复: ${taskName}`);
    });

    this.eventBus.on('control.emit.stopped', (data) => {
      const task = data.task || data;
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        const taskType = task?.type || data?.type;
        taskName = taskType === 'timer' ? '定时任务' :
                  taskType === 'all' ? '全部信号任务' :
                  taskType === 'selected' ? '选中信号任务' : '任务';
      }
      this.addLog('WARN', '任务停止', `任务已停止: ${taskName}`);
    });

    // 只监听信号发射失败事件 - 减少冗余日志，专注于错误监控
    this.eventBus.on('control.signal.emit.failed', (data) => {
      console.log('🔍 SystemMonitor: 收到 control.signal.emit.failed 事件:', data);
      const signal = data.signal || data;

      // 增强的信号名称获取
      let signalName = signal?.name || signal?.signalCode || data.signalName;
      if (!signalName || signalName === 'undefined') {
        signalName = '未知信号';
      }

      // 增强的错误信息获取
      let errorMsg = data.error || data.message || '未知错误';
      if (errorMsg && errorMsg.length > 50) {
        errorMsg = errorMsg.substring(0, 50) + '...';
      }

      this.addLog('ERROR', '信号发射', `发射失败: ${signalName} - ${errorMsg}`);
    });

    // ==================== 定时模块事件监听 - 任务调度状态 ====================

    this.eventBus.on('timer.task.activated', (data) => {
      const task = data.task || data;

      // 增强的任务名称获取
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        taskName = '定时任务';
      }

      // 增强的时间处理
      let nextTime = 'Invalid Date';
      const nextExecutionTime = data.nextExecutionTime;
      if (nextExecutionTime && !isNaN(new Date(nextExecutionTime))) {
        nextTime = new Date(nextExecutionTime).toLocaleString();
      }

      this.addLog('INFO', '定时调度', `任务已激活: ${taskName} (下次执行: ${nextTime})`);
    });

    this.eventBus.on('timer.task.deactivated', (data) => {
      const taskId = data.taskId || '未知ID';
      this.addLog('INFO', '定时调度', `任务已停用: ID ${taskId}`);
    });

    this.eventBus.on('timer.task.queue.updated', (data) => {
      const activeTasksCount = data.activeTasksCount ?? 'undefined';
      const nextTask = data.nextTask;
      const nextTaskName = nextTask?.name || '';
      const nextTaskInfo = nextTaskName ? ` 下个任务: ${nextTaskName}` : '';
      this.addLog('DEBUG', '定时队列', `激活任务数: ${activeTasksCount}${nextTaskInfo}`);
    });

    this.eventBus.on('timer.task.execution.request', (data) => {
      const task = data.task || data;
      let taskName = task?.name || data?.name;
      if (!taskName || taskName === 'undefined') {
        taskName = '定时任务';
      }
      this.addLog('INFO', '定时触发', `定时器触发任务: ${taskName} → 发送给控制模块`);
    });

    // ==================== 信号管理模块事件监听 - 数据管理状态 ====================

    this.eventBus.on('signal.learning.started', (data) => {
      this.addLog('INFO', '信号学习', '开始学习新信号...');
    });

    this.eventBus.on('signal.learning.stopped', (data) => {
      this.addLog('INFO', '信号学习', '信号学习已停止');
    });

    this.eventBus.on('signal.detected', (data) => {
      this.addLog('SUCCESS', '信号学习', `检测到信号: ${data.signalData?.name || '新信号'}`);
    });
  }

  async setupUI() {
    console.log('🔍 SystemMonitor: 开始设置UI...');
    this.initSidebarDisplay();

  }

  async loadModuleData() {
    // 加载历史日志（如果有的话）
    this.loadStoredLogs();
    this.addLog('INFO', '系统监控', '系统监控模块已启动');
  }



  /**
   * 初始化系统监控区域 - 可点击过滤的日志监控
   */
  initSidebarDisplay() {
    const container = $('#systemMonitorArea');
    if (!container) return;

    // 初始化当前过滤器
    this.currentFilter = 'all';

    container.innerHTML = `
      <div class="system-monitor-content">
        <!-- 日志统计和过滤器 -->
        <div class="log-statistics">
          <div class="log-stats-bar">
            <div class="log-stat-item all active" data-filter="all">
              <div class="stat-info">
                <div class="stat-label">全部</div>
                <div class="stat-count" id="allLogCount">0</div>
              </div>
            </div>
            <div class="log-stat-item system" data-filter="system">
              <div class="stat-info">
                <div class="stat-label">系统</div>
                <div class="stat-count" id="systemLogCount">0</div>
              </div>
            </div>
            <div class="log-stat-item error" data-filter="error">
              <div class="stat-info">
                <div class="stat-label">错误</div>
                <div class="stat-count" id="errorLogCount">0</div>
              </div>
            </div>
            <div class="log-stat-item warning" data-filter="warning">
              <div class="stat-info">
                <div class="stat-label">警告</div>
                <div class="stat-count" id="warningLogCount">0</div>
              </div>
            </div>
            <div class="log-stat-item success" data-filter="success">
              <div class="stat-info">
                <div class="stat-label">成功</div>
                <div class="stat-count" id="successLogCount">0</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 日志显示区域 -->
        <div class="logs-display">
          <div class="logs-list" id="filteredLogsList">
            <!-- 过滤后的日志将在这里显示 -->
          </div>
        </div>

        <!-- 系统资源监控 -->
        <div class="system-runtime-info">
          <div class="runtime-info-grid">
            <!-- 标签行 -->
            <div class="runtime-labels-row">
              <span class="info-label">CPU</span>
              <span class="info-label">内存</span>
              <span class="info-label">磁盘</span>
            </div>
            <!-- 数值行 -->
            <div class="runtime-values-row">
              <span class="info-value" id="cpuUsageDisplay">0%</span>
              <span class="info-value" id="memoryUsageDisplay">0%</span>
              <span class="info-value" id="diskUsageDisplay">0%</span>
            </div>
          </div>
        </div>
      </div>
    `;

    // 绑定过滤器点击事件
    this.bindFilterEvents();
  }

  /**
   * 绑定过滤器事件
   */
  bindFilterEvents() {
    console.log('🔍 SystemMonitor: 绑定过滤器事件...');

    // 🔧 修复：监控模块在侧边栏，应该查找systemMonitorArea
    const systemMonitorArea = $('#systemMonitorArea');
    console.log('🔍 SystemMonitor: 查找systemMonitorArea结果:', systemMonitorArea);

    if (systemMonitorArea) {
      systemMonitorArea.addEventListener('click', (e) => {
        console.log('🔍 SystemMonitor: 侧边栏点击事件', {
          target: e.target,
          className: e.target.className,
          hasLogStatItem: e.target.classList.contains('log-stat-item')
        });

        // 🔧 修复：使用closest查找log-stat-item，处理点击子元素的情况
        const logStatItem = e.target.closest('.log-stat-item');
        if (logStatItem) {
          const filter = logStatItem.getAttribute('data-filter');
          console.log('🔍 SystemMonitor: 过滤器点击:', filter);
          this.setActiveFilter(filter);
          this.updateFilteredLogs();
        }
      });

    } else {
      console.error('❌ SystemMonitor: 找不到systemMonitorArea容器');
    }
  }

  /**
   * 设置活动过滤器
   */
  setActiveFilter(filter) {
    this.currentFilter = filter;

    // 更新活动状态
    const filterItems = document.querySelectorAll('.log-stat-item');
    filterItems.forEach(item => {
      if (item.getAttribute('data-filter') === filter) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }







  /**
   * 添加日志条目
   */
  addLog(level, category, message, data = null) {
    const logEntry = {
      id: R1Utils.generateId('log'),
      timestamp: Date.now(),
      level: level,
      category: category,
      message: message,
      data: data
    };

    // 添加到日志数组
    this.logs.unshift(logEntry);

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // 更新UI
    this.updateMonitorDisplays();

    // 保存到本地存储
    this.saveLogsToStorage();

    return logEntry;
  }

  /**
   * 更新监控显示
   */
  updateMonitorDisplays() {
    this.updateLogStatistics();
    this.updateFilteredLogs();
    this.updateSystemRuntimeInfo();
  }



  /**
   * 更新日志统计
   */
  updateLogStatistics() {
    const allCount = this.logs.length;
    const systemCount = this.logs.filter(log => log.category === 'system' || log.level === 'INFO').length;
    const errorCount = this.logs.filter(log => log.level === 'ERROR').length;
    const warningCount = this.logs.filter(log => log.level === 'WARN').length;
    const successCount = this.logs.filter(log => log.level === 'SUCCESS').length;

    const elements = {
      allLogCount: allCount,
      systemLogCount: systemCount,
      errorLogCount: errorCount,
      warningLogCount: warningCount,
      successLogCount: successCount
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });
  }

  /**
   * 更新过滤后的日志显示
   */
  updateFilteredLogs() {
    const container = $('#filteredLogsList');
    if (!container) return;

    let filteredLogs = [];

    // 根据当前过滤器过滤日志
    switch (this.currentFilter) {
      case 'all':
        filteredLogs = this.logs; // 显示所有日志，允许滚动
        break;
      case 'system':
        filteredLogs = this.logs.filter(log =>
          log.category === 'system' || log.level === 'INFO'
        );
        break;
      case 'error':
        filteredLogs = this.logs.filter(log => log.level === 'ERROR');
        break;
      case 'warning':
        filteredLogs = this.logs.filter(log => log.level === 'WARN');
        break;
      case 'success':
        filteredLogs = this.logs.filter(log => log.level === 'SUCCESS');
        break;
      default:
        filteredLogs = this.logs;
    }

    if (filteredLogs.length === 0) {
      container.innerHTML = '<div class="no-logs">暂无日志</div>';
      return;
    }

    container.innerHTML = filteredLogs.map(log => {
      const levelInfo = this.logLevels[log.level] || this.logLevels.INFO;
      const timeStr = R1Utils.formatTime(log.timestamp, 'HH:mm:ss');

      return `
        <div class="log-item ${log.level.toLowerCase()}">
          <span class="log-time">${timeStr}</span>
          <span class="log-icon">${levelInfo.icon}</span>
          <span class="log-text">${log.message.length > 40 ? log.message.substring(0, 40) + '...' : log.message}</span>
        </div>
      `;
    }).join('');
  }



  /**
   * 更新系统资源监控信息 - CPU、内存、磁盘使用率
   */
  async updateSystemRuntimeInfo() {
    try {
      // 尝试获取硬件真实数据，失败则使用模拟数据
      const stats = await this.getSystemStats();

      // 更新CPU使用率
      this.updateResourceUsage('cpu', stats.cpuUsage);

      // 更新内存使用率
      this.updateResourceUsage('memory', stats.memoryUsage);

      // 更新磁盘使用率
      this.updateResourceUsage('disk', stats.diskUsage);

    } catch (error) {
      console.warn('SystemMonitor: 获取系统统计失败，使用模拟数据:', error);
      // 降级到模拟数据
      this.updateWithSimulatedData();
    }
  }

  /**
   * 获取系统统计数据 - 硬件优先，模拟降级
   */
  async getSystemStats() {
    // 通过事件系统检查ESP32连接状态 - 符合架构标准
    return new Promise((resolve) => {
      this.emitEvent('esp32.status.request', {
        callback: (statusData) => {
          if (statusData && statusData.isConnected) {
            // ESP32已连接，获取硬件数据
            this.getHardwareStats().then(resolve).catch(() => {
              // 硬件获取失败，降级到模拟数据
              resolve(this.getSimulatedStats());
            });
          } else {
            // ESP32未连接，使用模拟数据
            resolve(this.getSimulatedStats());
          }
        },
        timeout: 1000 // 1秒超时
      });

      // 超时降级到模拟数据
      setTimeout(() => {
        resolve(this.getSimulatedStats());
      }, 1000);
    });
  }

  /**
   * 从ESP32获取硬件统计数据
   */
  async getHardwareStats() {
    try {
      // 通过事件系统请求ESP32硬件数据
      return new Promise((resolve, reject) => {
        this.emitEvent('esp32.request.system-stats', {
          callback: (data) => {
            if (data && data.success) {
              resolve({
                cpuUsage: data.cpu_usage || 0,
                memoryUsage: data.memory_usage || 0,
                diskUsage: data.flash_usage || 0,
                temperature: data.temperature || 0,
                wifiSignal: data.wifi_rssi || 0
              });
            } else {
              reject(new Error('ESP32数据获取失败'));
            }
          },
          timeout: 3000 // 3秒超时
        });
      });
    } catch (error) {
      throw new Error(`硬件数据获取失败: ${error.message}`);
    }
  }

  /**
   * 获取模拟统计数据
   */
  getSimulatedStats() {
    // CPU使用率（模拟数据，基于系统活动）
    const cpuUsage = this.calculateCPUUsage();

    // 内存使用情况（浏览器真实数据）
    let memoryUsage = 0;
    if (performance.memory) {
      const memoryUsed = performance.memory.usedJSHeapSize;
      const memoryLimit = performance.memory.jsHeapSizeLimit;
      memoryUsage = Math.round((memoryUsed / memoryLimit) * 100);
    }

    // 磁盘使用率（模拟数据，基于本地存储）
    const diskUsage = this.calculateDiskUsage();

    return {
      cpuUsage,
      memoryUsage,
      diskUsage
    };
  }

  /**
   * 使用模拟数据更新显示
   */
  updateWithSimulatedData() {
    const stats = this.getSimulatedStats();

    this.updateResourceUsage('cpu', stats.cpuUsage);
    this.updateResourceUsage('memory', stats.memoryUsage);
    this.updateResourceUsage('disk', stats.diskUsage);
  }

  /**
   * 计算CPU使用率（模拟）
   */
  calculateCPUUsage() {
    // 基于系统活动模拟CPU使用率
    const baseUsage = 5; // 基础使用率5%
    const recentLogs = this.logs.filter(log => Date.now() - log.timestamp < 5000).length; // 5秒内的日志数量
    const activityBonus = Math.min(recentLogs * 2, 30); // 每条日志增加2%，最多30%

    // 添加一些随机波动
    const randomVariation = (Math.random() - 0.5) * 10; // ±5%的随机波动

    return Math.max(0, Math.min(100, baseUsage + activityBonus + randomVariation));
  }

  /**
   * 计算磁盘使用率（模拟）
   */
  calculateDiskUsage() {
    try {
      // 基于本地存储使用情况模拟磁盘使用率
      let storageUsed = 0;
      const storageLimit = 5 * 1024 * 1024; // 假设5MB限制

      // 计算localStorage使用量
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          storageUsed += localStorage[key].length;
        }
      }

      // 基础磁盘使用率（模拟系统文件）
      const baseDiskUsage = 25; // 基础25%
      const storagePercentage = Math.round((storageUsed / storageLimit) * 100);

      return Math.min(100, baseDiskUsage + storagePercentage);
    } catch (error) {
      // 如果无法计算，返回模拟值
      return 30 + Math.random() * 20; // 30-50%之间
    }
  }

  /**
   * 更新资源使用率显示
   */
  updateResourceUsage(type, percentage) {
    const textElement = $(`#${type}UsageDisplay`);

    if (textElement) {
      // 更新文字显示
      textElement.textContent = `${Math.round(percentage)}%`;

      // 根据使用率设置颜色
      textElement.className = 'info-value';
      if (percentage >= 80) {
        textElement.classList.add('danger');
      } else if (percentage >= 60) {
        textElement.classList.add('warning');
      } else {
        textElement.classList.add('success');
      }
    }
  }

  /**
   * 更新性能统计
   */
  updatePerformanceStats() {
    const elements = {
      apiRequestCount: this.performanceMetrics.requestCount,
      avgResponseTime: `${this.performanceMetrics.avgResponseTime.toFixed(0)}ms`
    };

    // 计算成功率
    const successRate = this.performanceMetrics.requestCount > 0
      ? Math.round((this.performanceMetrics.successCount / this.performanceMetrics.requestCount) * 100)
      : 100;
    elements.apiSuccessRate = `${successRate}%`;

    Object.entries(elements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });
  }

  /**
   * 更新运行时统计
   */
  updateRuntimeStats() {
    // 获取内存使用情况
    let memoryUsage = 0;
    if (performance.memory) {
      const memoryUsed = performance.memory.usedJSHeapSize;
      const memoryLimit = performance.memory.jsHeapSizeLimit;
      memoryUsage = Math.round((memoryUsed / memoryLimit) * 100);
    }

    // 通过事件系统获取活动模块数量 - 符合架构标准
    let activeModules = 0;
    this.emitEvent('system.modules.count.request', {
      callback: (data) => {
        activeModules = data?.count || 0;
      }
    });

    // 通过事件系统获取系统运行时间 - 符合架构标准
    let uptime = 0;
    this.emitEvent('system.uptime.request', {
      callback: (data) => {
        uptime = data?.uptime || 0;
      }
    });
    const uptimeStr = this.formatUptime(uptime);

    const elements = {
      memoryUsageMonitor: `${memoryUsage}%`,
      activeModulesCount: activeModules,
      systemUptimeMonitor: uptimeStr
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });
  }

  /**
   * 更新错误追踪
   */
  updateErrorTracking() {
    // 统计近期错误（最近5分钟）
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    const recentErrors = this.logs.filter(log =>
      log.level === 'ERROR' && log.timestamp > fiveMinutesAgo
    );

    const recentErrorCount = $('#recentErrorCount');
    const lastErrorTime = $('#lastErrorTime');
    const lastErrorMessage = $('#lastErrorMessage');

    if (recentErrorCount) {
      recentErrorCount.textContent = recentErrors.length;
    }

    if (recentErrors.length > 0) {
      const lastError = recentErrors[0]; // 最新的错误
      if (lastErrorTime) {
        lastErrorTime.textContent = R1Utils.formatTime(lastError.timestamp, 'HH:mm:ss');
      }
      if (lastErrorMessage) {
        const message = lastError.message.length > 20 ?
          lastError.message.substring(0, 20) + '...' :
          lastError.message;
        lastErrorMessage.textContent = message;
      }
    } else {
      if (lastErrorTime) lastErrorTime.textContent = '无错误';
      if (lastErrorMessage) lastErrorMessage.textContent = '系统运行正常';
    }
  }

  /**
   * 格式化运行时间
   */
  formatUptime(uptime) {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * 更新侧边栏指标
   */
  updateSidebarMetrics() {
    // 更新性能指标
    const elements = {
      sidebarTotalRequests: this.performanceMetrics.requestCount,
      sidebarLogCount: this.logs.length
    };

    // 计算成功率
    const successRate = this.performanceMetrics.requestCount > 0
      ? Math.round((this.performanceMetrics.successCount / this.performanceMetrics.requestCount) * 100)
      : 100;
    elements.sidebarSuccessRate = `${successRate}%`;
    elements.sidebarAvgResponseTime = `${this.performanceMetrics.avgResponseTime.toFixed(0)}ms`;

    Object.entries(elements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });

    // 统计各级别日志数量
    const counts = {
      ERROR: 0,
      WARN: 0,
      SUCCESS: 0
    };

    this.logs.forEach(log => {
      if (counts.hasOwnProperty(log.level)) {
        counts[log.level]++;
      }
    });

    // 更新日志统计
    const logStatElements = {
      sidebarErrorCount: counts.ERROR,
      sidebarWarningCount: counts.WARN,
      sidebarSuccessCount: counts.SUCCESS
    };

    Object.entries(logStatElements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });

    // 更新健康指示器
    this.updateHealthIndicators();
  }

  /**
   * 更新健康指示器
   */
  updateHealthIndicators() {
    // API健康状态
    const apiHealthDot = $('#apiHealthDot');
    const apiHealthText = $('#apiHealthText');
    if (apiHealthDot && apiHealthText) {
      const successRate = this.performanceMetrics.requestCount > 0
        ? (this.performanceMetrics.successCount / this.performanceMetrics.requestCount) * 100
        : 100;

      if (successRate >= 95) {
        apiHealthDot.className = 'health-dot success';
        apiHealthText.textContent = '正常';
      } else if (successRate >= 80) {
        apiHealthDot.className = 'health-dot warning';
        apiHealthText.textContent = '警告';
      } else {
        apiHealthDot.className = 'health-dot error';
        apiHealthText.textContent = '异常';
      }
    }

    // 错误率健康状态
    const errorHealthDot = $('#errorHealthDot');
    const errorHealthText = $('#errorHealthText');
    if (errorHealthDot && errorHealthText) {
      const recentErrorCount = this.logs.filter(log =>
        log.level === 'ERROR' && (Date.now() - log.timestamp) < 300000 // 5分钟内
      ).length;

      const errorRate = this.logs.length > 0 ? Math.round((recentErrorCount / Math.min(this.logs.length, 20)) * 100) : 0;

      if (errorRate === 0) {
        errorHealthDot.className = 'health-dot success';
        errorHealthText.textContent = '0%';
      } else if (errorRate < 10) {
        errorHealthDot.className = 'health-dot warning';
        errorHealthText.textContent = `${errorRate}%`;
      } else {
        errorHealthDot.className = 'health-dot error';
        errorHealthText.textContent = `${errorRate}%`;
      }
    }
  }



  /**
   * 获取过滤后的日志
   */
  getFilteredLogs() {
    let filtered = [...this.logs];

    // 按级别过滤
    const levelFilter = $('#logLevelFilter')?.value;
    if (levelFilter) {
      filtered = filtered.filter(log => log.level === levelFilter);
    }

    // 按搜索关键词过滤
    const searchKeyword = $('#logSearchInput')?.value?.toLowerCase();
    if (searchKeyword) {
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchKeyword) ||
        log.category.toLowerCase().includes(searchKeyword)
      );
    }

    return filtered;
  }

  /**
   * 过滤日志
   */
  filterLogs() {
    this.updateLogDisplay();
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = [];
    this.updateLogDisplay();
    this.updateSidebarLogs();
    this.updateMetrics();
    this.saveLogsToStorage();
    this.handleSuccess('日志已清空', '日志管理');
  }

  /**
   * 导出日志
   */
  exportLogs() {
    try {
      const filteredLogs = this.getFilteredLogs();
      const logData = filteredLogs.map(log => ({
        时间: R1Utils.formatTime(log.timestamp, 'YYYY-MM-DD HH:mm:ss'),
        级别: log.level,
        类别: log.category,
        消息: log.message
      }));

      const csvContent = this.convertToCSV(logData);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `R1系统日志_${R1Utils.formatTime(Date.now(), 'YYYY-MM-DD_HH-mm-ss')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      this.handleSuccess('日志导出成功', '日志管理');
    } catch (error) {
      this.handleError(error, '日志导出');
    }
  }

  /**
   * 转换为CSV格式
   */
  convertToCSV(data) {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    return '\uFEFF' + csvContent; // 添加BOM以支持中文
  }

  /**
   * 刷新日志
   */
  refreshLogs() {
    this.updateLogDisplay();
    this.updateSidebarLogs();
    this.updateMetrics();
    this.handleSuccess('日志已刷新', '日志管理');
  }

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics() {
    this.performanceMetrics.lastUpdate = Date.now();

    // 计算成功率
    const successRate = this.performanceMetrics.requestCount > 0
      ? Math.round((this.performanceMetrics.successCount / this.performanceMetrics.requestCount) * 100)
      : 100;

    // 更新UI
    const elements = {
      totalRequests: this.performanceMetrics.requestCount,
      successRate: `${successRate}%`,
      avgResponseTime: `${this.performanceMetrics.avgResponseTime.toFixed(0)}ms`
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = $(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });

    // 更新健康指示器
    this.updateHealthIndicators();
  }

  /**
   * 更新指标显示
   */
  updateMetrics() {
    // 更新日志计数
    const logCountElement = $('#logCount');
    if (logCountElement) {
      logCountElement.textContent = this.logs.length;
    }

    // 统计各级别日志数量
    const counts = {
      ERROR: 0,
      WARN: 0,
      SUCCESS: 0
    };

    this.logs.forEach(log => {
      if (counts.hasOwnProperty(log.level)) {
        counts[log.level]++;
      }
    });


  }

  /**
   * 更新健康指示器
   */
  updateHealthIndicators() {
    // API健康状态
    const apiHealthDot = $('#apiHealthDot');
    if (apiHealthDot) {
      const successRate = this.performanceMetrics.requestCount > 0
        ? (this.performanceMetrics.successCount / this.performanceMetrics.requestCount) * 100
        : 100;

      if (successRate >= 95) {
        apiHealthDot.className = 'health-dot success';
      } else if (successRate >= 80) {
        apiHealthDot.className = 'health-dot warning';
      } else {
        apiHealthDot.className = 'health-dot danger';
      }
    }

    // 错误率健康状态
    const errorHealthDot = $('#errorHealthDot');
    if (errorHealthDot) {
      const errorCount = this.logs.filter(log => log.level === 'ERROR').length;
      const recentErrorCount = this.logs.filter(log =>
        log.level === 'ERROR' && (Date.now() - log.timestamp) < 300000 // 5分钟内
      ).length;

      if (recentErrorCount === 0) {
        errorHealthDot.className = 'health-dot success';
      } else if (recentErrorCount < 5) {
        errorHealthDot.className = 'health-dot warning';
      } else {
        errorHealthDot.className = 'health-dot danger';
      }
    }
  }

  /**
   * 保存日志到本地存储
   */
  saveLogsToStorage() {
    try {
      const recentLogs = this.logs.slice(0, 100); // 只保存最新100条
      R1Utils.storage.set('systemLogs', recentLogs);
    } catch (error) {
      console.warn('保存日志到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载日志
   */
  loadStoredLogs() {
    try {
      const storedLogs = R1Utils.storage.get('systemLogs');
      if (storedLogs && Array.isArray(storedLogs)) {
        this.logs = storedLogs;
      }
    } catch (error) {
      console.warn('从本地存储加载日志失败:', error);
    }
  }

  /**
   * 刷新模块数据
   */
  async refresh() {
    this.updateMonitorDisplays();
    this.updatePerformanceMetrics();
    this.handleSuccess('系统监控已刷新', '数据刷新');
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      // 清理定时器
      if (this.updateInterval) {
        clearInterval(this.updateInterval);
        this.updateInterval = null;
      }

      // 清理事件监听器
      this.eventBus.off('system.monitor.update.request');
      this.eventBus.off('system.monitor.data.request');
      this.eventBus.off('system.monitor.performance.request');

      // 清理数据
      this.monitorData = {};
      this.performanceData = {};

      console.log('SystemMonitor: 模块已销毁');
    } catch (error) {
      console.error('SystemMonitor: 销毁模块失败:', error);
    }
  }
}

window.SystemMonitor = SystemMonitor;

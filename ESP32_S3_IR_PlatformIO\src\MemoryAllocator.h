#ifndef MEMORY_ALLOCATOR_H
#define MEMORY_ALLOCATOR_H

#include <Arduino.h>
#include "hardware-config.h"
#include "system-config.h"

/**
 * 纯RAM内存分配器类 (PSRAM已禁用)
 * 
 * 专为ESP32-S3 v0.2版本设计，完全禁用PSRAM功能
 * 只使用普通RAM进行内存分配和管理
 * 
 * 核心功能：
 * - 纯RAM内存分配策略
 * - 内存使用统计
 * - 内存泄漏检测
 * - 自动内存清理
 * - 内存碎片化管理
 */
class MemoryAllocator {
public:
    // ==================== 内存分配接口 ====================
    
    /**
     * 智能内存分配 (仅使用RAM)
     * @param size 分配大小
     * @return void* 分配的内存指针，失败返回nullptr
     */
    static void* smartAlloc(size_t size);
    
    /**
     * 智能内存释放
     * @param ptr 内存指针
     */
    static void smartFree(void* ptr);
    
    /**
     * 智能内存重新分配
     * @param ptr 原内存指针
     * @param newSize 新大小
     * @return void* 新内存指针
     */
    static void* smartRealloc(void* ptr, size_t newSize);
    
    /**
     * 分配并清零内存
     * @param count 元素数量
     * @param size 单个元素大小
     * @return void* 分配的内存指针
     */
    static void* smartCalloc(size_t count, size_t size);
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化内存分配器
     * @return bool 初始化是否成功
     */
    static bool initialize();
    
    /**
     * 关闭内存分配器
     */
    static void shutdown();
    
    // ==================== 内存统计接口 ====================
    
    /**
     * 获取总分配次数
     * @return size_t 分配次数
     */
    static size_t getTotalAllocations();
    
    /**
     * 获取总释放次数
     * @return size_t 释放次数
     */
    static size_t getTotalDeallocations();
    
    /**
     * 获取当前已分配内存大小
     * @return size_t 已分配内存大小
     */
    static size_t getCurrentAllocatedSize();
    
    /**
     * 获取峰值内存使用量
     * @return size_t 峰值内存使用量
     */
    static size_t getPeakMemoryUsage();
    
    /**
     * 获取堆内存使用量
     * @return size_t 堆内存使用量
     */
    static size_t getHeapUsage();
    
    // ==================== 内存健康检查 ====================
    
    /**
     * 检查内存泄漏
     * @return bool 是否存在内存泄漏
     */
    static bool checkMemoryLeaks();
    
    /**
     * 获取内存碎片化程度
     * @return float 碎片化程度 (0.0-1.0)
     */
    static float getFragmentationLevel();
    
    /**
     * 检查内存完整性
     * @return bool 内存是否完整
     */
    static bool checkMemoryIntegrity();
    
    // ==================== 内存清理接口 ====================
    
    /**
     * 执行内存清理
     */
    static void performCleanup();
    
    /**
     * 内存碎片整理
     */
    static void defragmentMemory();
    
    /**
     * 强制垃圾回收
     */
    static void forceGarbageCollection();
    
    // ==================== 内存池管理 ====================
    
    /**
     * 从内存池分配
     * @param size 分配大小
     * @return void* 分配的内存指针
     */
    static void* allocFromPool(size_t size);
    
    /**
     * 释放到内存池
     * @param ptr 内存指针
     * @param size 内存大小
     */
    static void freeToPool(void* ptr, size_t size);
    
    /**
     * 清理内存池
     */
    static void cleanupMemoryPools();
    
    // ==================== 调试接口 ====================
    
    /**
     * 打印内存统计信息
     */
    static void printMemoryStats();
    
    /**
     * 打印内存使用详情
     */
    static void printMemoryDetails();
    
    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    static void enableDebugMode(bool enable);

private:
    // ==================== 内存分配策略 ====================
    
    /**
     * 获取最佳分配策略
     * @param size 分配大小
     * @return int 分配策略 (0=Heap)
     */
    static int getBestAllocationStrategy(size_t size);
    
    /**
     * 检查内存是否紧张
     * @return bool 内存是否紧张
     */
    static bool isMemoryUnderPressure();
    
    // ==================== 内存池结构 ====================
    struct MemoryPool {
        void* memory;
        size_t size;
        size_t used;
        bool available;
    };
    
    // ==================== 调试信息结构 ====================
    #ifdef DEBUG_MODE
    struct AllocationInfo {
        void* ptr;
        size_t size;
        unsigned long timestamp;
        const char* file;
        int line;
    };
    #endif
    
    // ==================== 静态成员变量 ====================
    static size_t s_totalAllocations;
    static size_t s_totalDeallocations;
    static size_t s_currentAllocatedSize;
    static size_t s_peakMemoryUsage;
    static size_t s_heapUsage;
    
    // 内存池
    static MemoryPool* s_memoryPools;
    static size_t s_poolCount;
    
    #ifdef DEBUG_MODE
    static bool s_debugMode;
    static AllocationInfo* s_allocations;
    static size_t s_allocationCount;
    static size_t s_maxAllocations;
    #endif
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化内存池
     * @return bool 初始化是否成功
     */
    static bool initializeMemoryPools();
    
    /**
     * 记录分配信息
     * @param ptr 内存指针
     * @param size 分配大小
     */
    static void recordAllocation(void* ptr, size_t size);
    
    /**
     * 记录释放信息
     * @param ptr 内存指针
     */
    static void recordDeallocation(void* ptr);
    
    /**
     * 更新统计信息
     * @param size 内存大小
     * @param isAllocation 是否为分配操作
     */
    static void updateStats(size_t size, bool isAllocation);
};

// ==================== 内存分配阈值配置 ====================
#define SMALL_ALLOCATION_THRESHOLD 256      // 小内存分配阈值
#define LARGE_ALLOCATION_THRESHOLD 4096     // 大内存分配阈值
#define MIN_FREE_HEAP 30720                 // 最小空闲堆内存 (30KB)

// ==================== 调试宏 ====================
#ifdef DEBUG_MODE
    #define DEBUG_ALLOC(ptr, size) \
        Serial.printf("ALLOC: %p, size: %d\n", ptr, size)
    #define DEBUG_FREE(ptr) \
        Serial.printf("FREE: %p\n", ptr)
#else
    #define DEBUG_ALLOC(ptr, size)
    #define DEBUG_FREE(ptr)
#endif

// ==================== 便利宏 ====================
#define SMART_ALLOC(size) MemoryAllocator::smartAlloc(size)
#define SMART_FREE(ptr) MemoryAllocator::smartFree(ptr)
#define SMART_CALLOC(count, size) MemoryAllocator::smartCalloc(count, size)
#define SMART_REALLOC(ptr, size) MemoryAllocator::smartRealloc(ptr, size)

#endif // MEMORY_ALLOCATOR_H

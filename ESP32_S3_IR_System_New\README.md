# ESP32-S3 红外控制系统 - 全新架构版本

## 🎯 项目概述

这是一个**完全重新设计的ESP32-S3红外控制系统**，采用**零全局内存分配的清洁架构**，彻底解决了PSRAM初始化冲突问题，同时**100%兼容原前端系统**。

## ✨ 核心特点

### 🏗️ 架构优势
- **零全局内存分配设计** - 完全避免PSRAM初始化冲突
- **清洁架构模式** - 组件化、可维护、可扩展
- **延迟初始化** - 所有组件在PSRAM可用后创建
- **智能内存管理** - PSRAM优化分配策略

### 🔄 100% API兼容性
- **所有HTTP API端点完全相同**
- **WebSocket消息格式保持一致**
- **前端无需任何修改**
- **数据结构完全兼容**

### ⚡ 性能优化
- **PSRAM优化内存管理**
- **异步处理架构**
- **高效消息队列**
- **智能资源池化**

## 🔧 硬件要求

### 必需硬件
- **ESP32-S3** 开发板（带OPI PSRAM）
- **8MB OPI PSRAM** （必需）
- **16MB Flash** （推荐）
- **IR发射器** - 连接到GPIO 4
- **IR接收器** - 连接到GPIO 5

### 推荐开发板
- ESP32-S3-WROOM-1-N16R8
- ESP32-S3-DevKitM-1
- 其他带OPI PSRAM的ESP32-S3开发板

## 📋 Arduino IDE 配置

### 开发板设置
```
Board: ESP32S3 Dev Module
Upload Speed: 921600
USB Mode: Hardware CDC and JTAG
Flash Mode: QIO 80MHz ⚠️ 重要！
Flash Size: 16MB
PSRAM: OPI PSRAM ⚠️ 重要！
Partition Scheme: 16M Flash (3MB APP/9.9MB FATFS)
```

### 关键配置说明
- **Flash Mode必须设置为QIO** - 这是OPI PSRAM工作的前提
- **PSRAM必须设置为OPI PSRAM** - 不能选择QSPI PSRAM
- **build_opt.h文件已包含** - 无需手动配置编译选项

## 🚀 快速开始

### 1. 下载和安装
```bash
# 克隆项目
git clone [项目地址]
cd ESP32_S3_IR_System_New

# 或直接下载ZIP文件解压
```

### 2. 配置WiFi
编辑 `SystemManager.h` 文件，修改WiFi设置：
```cpp
const char* WIFI_SSID = "你的WiFi名称";
const char* WIFI_PASSWORD = "你的WiFi密码";
```

### 3. 安装依赖库
在Arduino IDE中安装以下库：
- **ESPAsyncWebServer** by lacamera
- **AsyncTCP** by dvarrel
- **ArduinoJson** by Benoit Blanchon
- **IRremoteESP8266** by David Conran

### 4. 编译上传
1. 打开 `ESP32_S3_IR_System_New.ino`
2. 选择正确的开发板和配置
3. 编译并上传到ESP32-S3
4. 打开串口监视器（115200波特率）

### 5. 验证系统
查看串口输出，确认以下信息：
```
✅ PSRAM DETECTION SUCCESSFUL!
📊 PSRAM Size: 8388608 bytes (8.00 MB)
✅ All system components initialized successfully
🚀 System ready for operation
```

### 6. 访问Web界面
- 连接成功后，串口会显示IP地址
- 在浏览器中访问 `http://[ESP32_IP]`
- 开始使用红外控制系统

## 📁 项目结构

```
ESP32_S3_IR_System_New/
├── ESP32_S3_IR_System_New.ino    # 主程序入口
├── build_opt.h                   # PSRAM编译配置
├── README.md                     # 项目说明
│
├── 后端组件/
│   ├── SystemManager.h/.cpp      # 系统管理器
│   ├── DataManager.h/.cpp        # 数据管理器
│   ├── IRController.h/.cpp       # 红外控制器
│   ├── TaskManager.h/.cpp        # 任务管理器
│   ├── WSManager.h/.cpp          # WebSocket管理器
│   └── WebServerManager.h/.cpp   # Web服务器管理器
│
└── 前端文件/
    └── data/
        ├── index.html            # 主页面
        ├── css/                  # 样式文件
        └── js/                   # JavaScript文件
```

## 🔍 故障排除

### PSRAM检测失败
```
❌ PSRAM NOT DETECTED!
```
**解决方案：**
1. 确认开发板有OPI PSRAM
2. 检查Flash Mode是否设置为QIO
3. 检查PSRAM设置是否为OPI PSRAM
4. 重新选择开发板型号

### Flash Mode错误
```
❌ CRITICAL: Flash Mode is not QIO!
```
**解决方案：**
1. 在Arduino IDE中设置Flash Mode为QIO 80MHz
2. 重新编译上传

### WiFi连接失败
```
❌ WiFi connection failed!
```
**解决方案：**
1. 检查WiFi SSID和密码是否正确
2. 确认WiFi网络可用
3. 系统会自动启动AP模式作为备用

### 编译错误
**解决方案：**
1. 确认所有依赖库已安装
2. 检查Arduino IDE版本（推荐2.0+）
3. 清理编译缓存后重试

## 🌐 API文档

### HTTP API端点
```
GET  /api/system/status          # 获取系统状态
GET  /api/system/info           # 获取系统信息
POST /api/system/restart        # 重启系统

GET  /api/signals               # 获取信号列表
POST /api/signals/send          # 发射信号
POST /api/signals/learn         # 学习信号
POST /api/signals/learn/stop    # 停止学习
GET  /api/signals/learn/status  # 学习状态
POST /api/signals/batch         # 批量发射
POST /api/signals/delete        # 删除信号

GET  /api/tasks                 # 获取任务列表
POST /api/tasks                 # 创建任务
POST /api/tasks/control         # 控制任务
GET  /api/tasks/status          # 任务状态
POST /api/tasks/delete          # 删除任务

GET  /api/timers                # 获取定时器列表
POST /api/timers                # 创建定时器
POST /api/timers/toggle         # 切换定时器
POST /api/timers/update         # 更新定时器
POST /api/timers/delete         # 删除定时器

GET  /api/data/export           # 导出数据
POST /api/data/import           # 导入数据
POST /api/data/backup           # 备份数据
POST /api/data/clear            # 清空数据

GET  /api/config                # 获取配置
POST /api/config                # 更新配置
POST /api/config/reset          # 重置配置
```

### WebSocket消息
```
signal_sent         # 信号发射通知
learning_started    # 学习开始通知
learning_completed  # 学习完成通知
task_started        # 任务开始通知
task_completed      # 任务完成通知
system_status       # 系统状态更新
```

## 🔧 开发说明

### 添加新功能
1. 在相应的Manager类中添加方法
2. 在WebServerManager中添加API端点
3. 更新前端JavaScript（如需要）

### 内存管理原则
1. 避免全局对象构造
2. 使用智能指针管理动态内存
3. 优先使用PSRAM分配
4. 及时释放不需要的资源

### 调试技巧
1. 使用串口监视器查看详细日志
2. 检查内存使用情况
3. 监控WebSocket连接状态
4. 使用浏览器开发者工具调试前端

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果遇到问题，请：
1. 查看故障排除部分
2. 检查串口输出日志
3. 提交详细的Issue报告

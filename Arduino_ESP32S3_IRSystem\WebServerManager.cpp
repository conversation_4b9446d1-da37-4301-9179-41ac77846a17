#include "WebServerManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "TaskManager.h"
#include "WSManager.h"
#include "NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
WebServerManager::WebServerManager()
    : m_server(nullptr)
    , m_initialized(false)
    , m_running(false)
    , m_port(80)
    , m_debugMode(false)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_taskManager(nullptr)
    , m_wsManager(nullptr)
    , m_networkSecurity(nullptr)
    , m_totalRequests(0)
    , m_successfulRequests(0)
    , m_failedRequests(0)
    , m_startTime(0)
{
    Serial.println("🌐 WebServerManager created");
}

WebServerManager::~WebServerManager() {
    shutdown();
    if (m_server) {
        delete m_server;
        m_server = nullptr;
    }
    Serial.println("🌐 WebServerManager destroyed");
}

// ==================== 系统生命周期 ====================
bool WebServerManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️ WebServerManager already initialized");
        return true;
    }
    
    Serial.println("🌐 Initializing WebServerManager...");
    
    if (!initializeServer()) {
        Serial.println("❌ Server initialization failed");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ WebServerManager initialization completed");
    return true;
}

void WebServerManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🌐 Shutting down WebServerManager...");
    
    stop();
    cleanupServer();
    
    m_initialized = false;
    Serial.println("✅ WebServerManager shutdown completed");
}

bool WebServerManager::isHealthy() const {
    return m_initialized && m_server && m_running;
}

void WebServerManager::setDependencies(DataManager* dataManager, IRController* irController, 
                                      TaskManager* taskManager, WSManager* wsManager, 
                                      NetworkSecurity* networkSecurity) {
    m_dataManager = dataManager;
    m_irController = irController;
    m_taskManager = taskManager;
    m_wsManager = wsManager;
    m_networkSecurity = networkSecurity;
    
    Serial.println("🌐 WebServerManager dependencies set");
}

// ==================== 服务器控制 ====================
bool WebServerManager::start(uint16_t port) {
    if (!m_initialized || !m_server) {
        Serial.println("❌ WebServerManager not initialized");
        return false;
    }
    
    if (m_running) {
        Serial.println("⚠️ Server already running");
        return true;
    }
    
    m_port = port;
    
    // 设置路由
    setupRoutes();
    setupStaticRoutes();
    setupAPIRoutes();
    
    // 启动服务器
    m_server->begin();
    m_running = true;
    m_startTime = millis();
    
    Serial.printf("✅ Web server started on port %d\n", m_port);
    return true;
}

void WebServerManager::stop() {
    if (m_running && m_server) {
        m_server->end();
        m_running = false;
        Serial.println("🌐 Web server stopped");
    }
}

bool WebServerManager::restart() {
    Serial.println("🌐 Restarting web server...");
    
    uint16_t currentPort = m_port;
    stop();
    delay(1000);
    
    return start(currentPort);
}

bool WebServerManager::isRunning() const {
    return m_running;
}

uint16_t WebServerManager::getPort() const {
    return m_port;
}

// ==================== 路由管理 ====================
void WebServerManager::setupRoutes() {
    if (!m_server) return;
    
    // 根路径
    m_server->on("/", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleRoot(request);
    });
    
    // 404处理
    m_server->onNotFound([this](AsyncWebServerRequest* request) {
        handleNotFound(request);
    });
    
    // CORS预检
    m_server->on("/", HTTP_OPTIONS, [this](AsyncWebServerRequest* request) {
        handleCORS(request);
    });
}

void WebServerManager::setupStaticRoutes() {
    if (!m_server) return;
    
    // 静态文件服务
    m_server->serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // CSS文件
    m_server->serveStatic("/css/", SPIFFS, "/css/");
    
    // JavaScript文件
    m_server->serveStatic("/js/", SPIFFS, "/js/");
    
    // 图片文件
    m_server->serveStatic("/images/", SPIFFS, "/images/");
}

void WebServerManager::setupAPIRoutes() {
    if (!m_server) return;

    // ==================== 系统相关API ====================
    m_server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleSystemStatus(request);
    });

    m_server->on("/api/system/error-log", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSystemErrorLog(request);
    });

    m_server->on("/api/batch", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleBatchRequest(request);
    });

    // ==================== 信号相关API ====================
    // 获取所有信号
    m_server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleSignalsAPI(request);
    });

    // 删除单个信号
    m_server->on("/api/signals/delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalDelete(request);
    });

    // 批量删除信号
    m_server->on("/api/signals/batch-delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalBatchDelete(request);
    });

    // 更新信号
    m_server->on("/api/signals/update", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalUpdate(request);
    });

    // 发送信号
    m_server->on("/api/signals/send", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalSend(request);
    });

    // 开始学习信号
    m_server->on("/api/signals/learn/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalLearnStart(request);
    });

    // 停止学习信号
    m_server->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalLearnStop(request);
    });

    // 保存学习的信号
    m_server->on("/api/signals/learn/save", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalLearnSave(request);
    });

    // 导入信号文件
    m_server->on("/api/signals/import", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalImport(request);
    });

    // 导入文本信号
    m_server->on("/api/signals/import/text", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalImportText(request);
    });

    // 执行文件导入
    m_server->on("/api/signals/import/execute", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalImportExecute(request);
    });

    // 执行文本导入
    m_server->on("/api/signals/import/text/execute", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSignalImportTextExecute(request);
    });

    // ==================== 控制相关API ====================
    // 任务历史记录
    m_server->on("/api/control/history", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleControlHistoryGet(request);
    });

    m_server->on("/api/control/history", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleControlHistoryPost(request);
    });

    // ==================== 任务相关API ====================
    m_server->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleTasksAPI(request);
    });

    m_server->on("/api/tasks", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleTasksAPI(request);
    });

    // ==================== 配置API ====================
    m_server->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleConfigAPI(request);
    });

    m_server->on("/api/config", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleConfigAPI(request);
    });
}

// ==================== 请求处理 ====================
void WebServerManager::handleRoot(AsyncWebServerRequest* request) {
    logRequest(request, 200);
    m_totalRequests++;
    m_successfulRequests++;
    
    request->send(SPIFFS, "/index.html", "text/html");
}

void WebServerManager::handleNotFound(AsyncWebServerRequest* request) {
    logRequest(request, 404);
    m_totalRequests++;
    m_failedRequests++;
    
    DynamicJsonDocument response(256);
    response["error"] = "Not Found";
    response["path"] = request->url();
    response["method"] = request->methodToString();
    
    sendJSONResponse(request, response, 404);
}

void WebServerManager::handleCORS(AsyncWebServerRequest* request) {
    AsyncWebServerResponse* response = request->beginResponse(200);
    setCORSHeaders(response);
    request->send(response);
}

// ==================== API处理器 ====================
void WebServerManager::handleSystemStatus(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    DynamicJsonDocument status(512);
    status["timestamp"] = millis();
    status["uptime"] = millis() - m_startTime;
    status["free_heap"] = ESP.getFreeHeap();
    status["total_requests"] = m_totalRequests;
    status["server_running"] = m_running;
    
    if (m_dataManager) {
        status["signal_count"] = m_dataManager->getSignalCount();
        status["task_count"] = m_dataManager->getTaskCount();
    }
    
    if (m_taskManager) {
        status["is_executing"] = m_taskManager->isExecuting();
    }
    
    sendJSONResponse(request, status);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalsAPI(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }
    
    if (request->method() == HTTP_GET) {
        // 获取所有信号
        DynamicJsonDocument signals = m_dataManager->getAllSignals();
        sendJSONResponse(request, signals);
    } else if (request->method() == HTTP_POST) {
        // 创建新信号（需要解析请求体）
        DynamicJsonDocument response(256);
        response["message"] = "Signal creation not implemented in this simplified version";
        sendJSONResponse(request, response);
    }
    
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleTasksAPI(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    if (!m_taskManager) {
        sendErrorResponse(request, "TaskManager not available", 503);
        m_failedRequests++;
        return;
    }
    
    if (request->method() == HTTP_GET) {
        // 获取任务状态
        DynamicJsonDocument status = m_taskManager->getAllTasksStatus();
        sendJSONResponse(request, status);
    } else if (request->method() == HTTP_POST) {
        // 创建或执行任务
        DynamicJsonDocument response(256);
        response["message"] = "Task execution not implemented in this simplified version";
        sendJSONResponse(request, response);
    }
    
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleTimersAPI(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    DynamicJsonDocument response(256);
    response["message"] = "Timer API not implemented in this simplified version";
    sendJSONResponse(request, response);
    
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleIRControlAPI(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        m_failedRequests++;
        return;
    }
    
    DynamicJsonDocument response(256);
    response["message"] = "IR control not implemented in this simplified version";
    sendJSONResponse(request, response);
    
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleConfigAPI(AsyncWebServerRequest* request) {
    m_totalRequests++;
    
    DynamicJsonDocument response(256);
    response["message"] = "Config API not implemented in this simplified version";
    sendJSONResponse(request, response);

    m_successfulRequests++;
    logRequest(request, 200);
}

// ==================== 响应辅助方法 ====================
void WebServerManager::sendJSONResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& json, int code) {
    String jsonString;
    serializeJson(json, jsonString);

    AsyncWebServerResponse* response = request->beginResponse(code, "application/json", jsonString);
    setCORSHeaders(response);
    request->send(response);
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, const String& message, int code) {
    DynamicJsonDocument error(256);
    error["error"] = message;
    error["code"] = code;
    error["timestamp"] = millis();

    sendJSONResponse(request, error, code);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest* request, const String& message, const DynamicJsonDocument& data) {
    DynamicJsonDocument response(512);
    response["success"] = true;
    response["message"] = message;
    response["timestamp"] = millis();

    if (!data.isNull()) {
        response["data"] = data;
    }

    sendJSONResponse(request, response);
}

// ==================== 统计和监控 ====================
DynamicJsonDocument WebServerManager::getServerStatistics() const {
    DynamicJsonDocument stats(512);

    stats["total_requests"] = m_totalRequests;
    stats["successful_requests"] = m_successfulRequests;
    stats["failed_requests"] = m_failedRequests;
    stats["uptime"] = millis() - m_startTime;
    stats["server_running"] = m_running;
    stats["port"] = m_port;

    if (m_totalRequests > 0) {
        stats["success_rate"] = (float)m_successfulRequests / m_totalRequests * 100;
    } else {
        stats["success_rate"] = 0.0f;
    }

    return stats;
}

void WebServerManager::logRequest(AsyncWebServerRequest* request, int responseCode) {
    if (m_debugMode) {
        String clientIP = request->client()->remoteIP().toString();
        Serial.printf("🌐 %s %s %s -> %d\n",
                     request->methodToString(),
                     request->url().c_str(),
                     clientIP.c_str(),
                     responseCode);
    }
}

int WebServerManager::getActiveConnections() const {
    return m_running ? 1 : 0;
}

// ==================== 调试功能 ====================
void WebServerManager::enableDebugMode(bool enable) {
    m_debugMode = enable;
    Serial.printf("🌐 Debug mode %s\n", enable ? "enabled" : "disabled");
}

String WebServerManager::getDebugInfo() const {
    String info = "WebServerManager Debug Info:\n";
    info += "  Initialized: " + String(m_initialized ? "Yes" : "No") + "\n";
    info += "  Running: " + String(m_running ? "Yes" : "No") + "\n";
    info += "  Port: " + String(m_port) + "\n";
    info += "  Total Requests: " + String(m_totalRequests) + "\n";

    return info;
}

// ==================== 私有方法实现 ====================
bool WebServerManager::initializeServer() {
    if (m_server) {
        return true; // 已经初始化
    }

    m_server = new AsyncWebServer(m_port);
    if (!m_server) {
        Serial.println("❌ Failed to create server instance");
        return false;
    }

    Serial.printf("🌐 Server instance created on port %d\n", m_port);
    return true;
}

void WebServerManager::cleanupServer() {
    if (m_server) {
        delete m_server;
        m_server = nullptr;
    }
}

void WebServerManager::setCORSHeaders(AsyncWebServerResponse* response) {
    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-API-Key");
}

bool WebServerManager::validatePermission(AsyncWebServerRequest* request) {
    return true; // 简化实现
}

bool WebServerManager::validateAPIKey(AsyncWebServerRequest* request) {
    return true; // 简化实现
}

bool WebServerManager::checkRateLimit(AsyncWebServerRequest* request) {
    return false; // 简化实现：不限制
}

// ==================== 扩展API处理器实现 ====================
void WebServerManager::handleSystemErrorLog(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：记录错误日志
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Error log recorded";
    response["timestamp"] = millis();

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleBatchRequest(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：批量请求处理
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Batch request processed";
    response["results"] = JsonArray();

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalDelete(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 解析请求参数
    DynamicJsonDocument params = parseRequestParams(request);
    if (!params.containsKey("signalId")) {
        sendErrorResponse(request, "Missing signalId parameter", 400);
        m_failedRequests++;
        return;
    }

    String signalId = params["signalId"];
    bool success = m_dataManager->deleteSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Signal deleted successfully" : "Failed to delete signal";

    sendJSONResponse(request, response);
    if (success) m_successfulRequests++; else m_failedRequests++;
    logRequest(request, success ? 200 : 400);
}

void WebServerManager::handleSignalBatchDelete(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 简化实现：批量删除信号
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signals deleted successfully";
    response["deleted_count"] = 0;

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalUpdate(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 简化实现：更新信号
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signal updated successfully";

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalSend(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_irController || !m_taskManager) {
        sendErrorResponse(request, "IRController or TaskManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 解析请求参数
    DynamicJsonDocument params = parseRequestParams(request);
    if (!params.containsKey("signalId")) {
        sendErrorResponse(request, "Missing signalId parameter", 400);
        m_failedRequests++;
        return;
    }

    String signalId = params["signalId"];
    bool success = m_taskManager->executeSingleSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Signal sent successfully" : "Failed to send signal";

    sendJSONResponse(request, response);
    if (success) m_successfulRequests++; else m_failedRequests++;
    logRequest(request, success ? 200 : 400);
}

void WebServerManager::handleSignalLearnStart(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        m_failedRequests++;
        return;
    }

    // 解析超时参数
    DynamicJsonDocument params = parseRequestParams(request);
    unsigned long timeout = params["timeout"] | 10000;

    bool success = m_irController->startLearning(timeout);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Learning started" : "Failed to start learning";
    response["timeout"] = timeout;

    sendJSONResponse(request, response);
    if (success) m_successfulRequests++; else m_failedRequests++;
    logRequest(request, success ? 200 : 400);
}

void WebServerManager::handleSignalLearnStop(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        m_failedRequests++;
        return;
    }

    bool success = m_irController->stopLearning();

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Learning stopped" : "Failed to stop learning";

    sendJSONResponse(request, response);
    if (success) m_successfulRequests++; else m_failedRequests++;
    logRequest(request, success ? 200 : 400);
}

void WebServerManager::handleSignalLearnSave(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager || !m_irController) {
        sendErrorResponse(request, "DataManager or IRController not available", 503);
        m_failedRequests++;
        return;
    }

    // 简化实现：保存学习的信号
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signal saved successfully";
    response["signal_id"] = "signal_" + String(millis());

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalImport(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：信号导入
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signal import processed";
    response["parsed_signals"] = JsonArray();

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalImportText(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：文本信号导入
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Text import processed";
    response["parsed_signals"] = JsonArray();

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalImportExecute(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 简化实现：执行信号导入
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signals imported successfully";
    response["imported_count"] = 0;

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleSignalImportTextExecute(AsyncWebServerRequest* request) {
    m_totalRequests++;

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        m_failedRequests++;
        return;
    }

    // 简化实现：执行文本信号导入
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Text signals imported successfully";
    response["imported_count"] = 0;

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleControlHistoryGet(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：获取控制历史
    DynamicJsonDocument response(512);
    response["success"] = true;
    response["data"] = JsonArray();
    response["total_count"] = 0;

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

void WebServerManager::handleControlHistoryPost(AsyncWebServerRequest* request) {
    m_totalRequests++;

    // 简化实现：保存控制历史
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Control history saved successfully";

    sendJSONResponse(request, response);
    m_successfulRequests++;
    logRequest(request, 200);
}

// ==================== 辅助方法实现 ====================
DynamicJsonDocument WebServerManager::parseRequestParams(AsyncWebServerRequest* request) {
    DynamicJsonDocument params(512);

    // 解析URL参数
    int paramCount = request->params();
    for (int i = 0; i < paramCount; i++) {
        AsyncWebParameter* param = request->getParam(i);
        params[param->name()] = param->value();
    }

    return params;
}

/*
 * Task Manager Implementation - ESP32-S3 IR Control System
 * Fully compatible with ArduinoJson 6.21.3 API
 * Handles task creation, execution, and management with priority queuing
 */

#include "task_manager.h"
#include "data_manager.h"
#include "ir_controller.h"
#include "websocket_manager.h"
#include <algorithm>

TaskManager::TaskManager() {
    dataManager = nullptr;
    irController = nullptr;
    wsManager = nullptr;
    
    currentTask = nullptr;
    m_isProcessing = false;
    
    totalTasksCreated = 0;
    totalTasksCompleted = 0;
    totalTasksFailed = 0;
    
    maxConcurrentTasks = DEFAULT_MAX_CONCURRENT_TASKS;
    taskTimeoutMs = DEFAULT_TASK_TIMEOUT;
    
    // Clear callbacks
    onTaskStarted = nullptr;
    onTaskCompleted = nullptr;
    onTaskFailed = nullptr;
}

TaskManager::~TaskManager() {
    clearAllTasks();
}

bool TaskManager::initialize() {
    Serial.println("Initializing Task Manager...");
    
    Serial.printf("✅ Task Manager initialized - Max concurrent: %d, Timeout: %lu ms\n", 
                 maxConcurrentTasks, taskTimeoutMs);
    
    return true;
}

// ==================== Module Reference Setters ====================

void TaskManager::setDataManager(DataManager* dm) {
    dataManager = dm;
    Serial.println("🔗 TaskManager: DataManager reference set");
}

void TaskManager::setIRController(IRController* ir) {
    irController = ir;
    Serial.println("🔗 TaskManager: IRController reference set");
}

void TaskManager::setWebSocketManager(WebSocketManager* wsm) {
    wsManager = wsm;
    Serial.println("🔗 TaskManager: WebSocketManager reference set");
}

void TaskManager::handleLoop() {
    // Process task queue if not currently processing
    if (!m_isProcessing && !taskQueue.empty()) {
        // Sort queue by priority
        sortTaskQueue();
        
        // Get next task
        Task nextTask = taskQueue.front();
        taskQueue.erase(taskQueue.begin());
        
        // Execute task
        m_isProcessing = true;
        currentTask = &nextTask;
        
        Serial.printf("📋 Starting task: %s (Type: %d, Priority: %d)\n", 
                     nextTask.name.c_str(), static_cast<int>(nextTask.type), 
                     static_cast<int>(nextTask.priority));
        
        nextTask.status = TaskStatus::RUNNING;
        nextTask.startedAt = millis();
        
        // Add to tasks collection
        tasks.push_back(nextTask);
        int taskIndex = tasks.size() - 1;
        currentTask = &tasks[taskIndex];
        
        // Trigger callback
        if (onTaskStarted) {
            onTaskStarted(nextTask.id);
        }
        
        // Execute the task
        bool success = executeTask(*currentTask);
        
        // Complete the task
        completeTask(*currentTask, success);
        
        currentTask = nullptr;
        m_isProcessing = false;
    }
    
    // Clean up old completed tasks
    if (tasks.size() > MAX_TASK_HISTORY) {
        // Remove oldest completed tasks
        for (auto it = tasks.begin(); it != tasks.end();) {
            if (it->status == TaskStatus::COMPLETED || it->status == TaskStatus::FAILED) {
                it = tasks.erase(it);
                if (tasks.size() <= MAX_TASK_HISTORY) break;
            } else {
                ++it;
            }
        }
    }
}

// ==================== Task Creation ====================

String TaskManager::createTask(const String& name, TaskType type, const DynamicJsonDocument& parameters, 
                              TaskPriority priority) {
    Task newTask;
    newTask.id = generateTaskId();
    newTask.name = name;
    newTask.type = type;
    newTask.status = TaskStatus::PENDING;
    newTask.priority = priority;
    newTask.parameters = parameters;
    newTask.createdAt = millis();
    
    if (!validateTask(newTask)) {
        Serial.println("ERROR: Task validation failed");
        return "";
    }
    
    taskQueue.push_back(newTask);
    totalTasksCreated++;
    
    Serial.printf("📋 Task created: %s (ID: %s)\n", name.c_str(), newTask.id.c_str());
    return newTask.id;
}

String TaskManager::createSignalSendTask(const String& signalId, TaskPriority priority) {
    DynamicJsonDocument params(128);
    params["signal_id"] = signalId;
    
    return createTask("Send Signal: " + signalId, TaskType::SIGNAL_SEND, params, priority);
}

String TaskManager::createBatchSendTask(const std::vector<String>& signalIds, int delayMs, 
                                       TaskPriority priority) {
    DynamicJsonDocument params(512);
    JsonArray signalsArray = params.createNestedArray("signal_ids");
    
    for (const String& signalId : signalIds) {
        signalsArray.add(signalId);
    }
    
    params["delay_ms"] = delayMs;
    
    String taskName = "Batch Send (" + String(signalIds.size()) + " signals)";
    return createTask(taskName, TaskType::BATCH_SEND, params, priority);
}

String TaskManager::createTimerTask(const String& timerId, TaskPriority priority) {
    DynamicJsonDocument params(128);
    params["timer_id"] = timerId;
    
    return createTask("Execute Timer: " + timerId, TaskType::TIMER_EXECUTE, params, priority);
}

String TaskManager::createSystemCommandTask(const String& command, TaskPriority priority) {
    DynamicJsonDocument params(128);
    params["command"] = command;
    
    return createTask("System Command: " + command, TaskType::SYSTEM_COMMAND, params, priority);
}

// ==================== Task Control ====================

bool TaskManager::startTask(const String& taskId) {
    Task* task = getTask(taskId);
    if (!task) {
        Serial.printf("ERROR: Task %s not found\n", taskId.c_str());
        return false;
    }
    
    if (task->status != TaskStatus::PENDING && task->status != TaskStatus::PAUSED) {
        Serial.printf("ERROR: Task %s cannot be started (Status: %d)\n", 
                     taskId.c_str(), static_cast<int>(task->status));
        return false;
    }
    
    // Add to queue if not already there
    bool inQueue = false;
    for (const auto& queuedTask : taskQueue) {
        if (queuedTask.id == taskId) {
            inQueue = true;
            break;
        }
    }
    
    if (!inQueue) {
        task->status = TaskStatus::PENDING;
        taskQueue.push_back(*task);
    }
    
    Serial.printf("📋 Task queued for execution: %s\n", taskId.c_str());
    return true;
}

bool TaskManager::pauseTask(const String& taskId) {
    Task* task = getTask(taskId);
    if (!task) {
        Serial.printf("ERROR: Task %s not found\n", taskId.c_str());
        return false;
    }
    
    if (task->status == TaskStatus::RUNNING) {
        task->status = TaskStatus::PAUSED;
        Serial.printf("📋 Task paused: %s\n", taskId.c_str());
        return true;
    }
    
    Serial.printf("ERROR: Task %s cannot be paused (Status: %d)\n", 
                 taskId.c_str(), static_cast<int>(task->status));
    return false;
}

bool TaskManager::resumeTask(const String& taskId) {
    Task* task = getTask(taskId);
    if (!task) {
        Serial.printf("ERROR: Task %s not found\n", taskId.c_str());
        return false;
    }
    
    if (task->status == TaskStatus::PAUSED) {
        task->status = TaskStatus::PENDING;
        
        // Add back to queue
        taskQueue.push_back(*task);
        
        Serial.printf("📋 Task resumed: %s\n", taskId.c_str());
        return true;
    }
    
    Serial.printf("ERROR: Task %s cannot be resumed (Status: %d)\n", 
                 taskId.c_str(), static_cast<int>(task->status));
    return false;
}

bool TaskManager::cancelTask(const String& taskId) {
    Task* task = getTask(taskId);
    if (!task) {
        Serial.printf("ERROR: Task %s not found\n", taskId.c_str());
        return false;
    }
    
    // Remove from queue if present
    for (auto it = taskQueue.begin(); it != taskQueue.end(); ++it) {
        if (it->id == taskId) {
            taskQueue.erase(it);
            break;
        }
    }
    
    task->status = TaskStatus::CANCELLED;
    task->completedAt = millis();
    task->result = "Task cancelled by user";
    
    Serial.printf("📋 Task cancelled: %s\n", taskId.c_str());
    return true;
}

bool TaskManager::deleteTask(const String& taskId) {
    // First cancel if running
    cancelTask(taskId);
    
    // Remove from tasks collection
    for (auto it = tasks.begin(); it != tasks.end(); ++it) {
        if (it->id == taskId) {
            tasks.erase(it);
            Serial.printf("📋 Task deleted: %s\n", taskId.c_str());
            return true;
        }
    }
    
    Serial.printf("ERROR: Task %s not found for deletion\n", taskId.c_str());
    return false;
}

// ==================== Task Queries ====================

Task* TaskManager::getTask(const String& taskId) {
    for (auto& task : tasks) {
        if (task.id == taskId) {
            return &task;
        }
    }
    return nullptr;
}

std::vector<Task> TaskManager::getAllTasks() {
    return tasks;
}

std::vector<Task> TaskManager::getTasksByStatus(TaskStatus status) {
    std::vector<Task> filteredTasks;
    
    for (const auto& task : tasks) {
        if (task.status == status) {
            filteredTasks.push_back(task);
        }
    }
    
    return filteredTasks;
}

std::vector<Task> TaskManager::getTasksByPriority(TaskPriority priority) {
    std::vector<Task> filteredTasks;
    
    for (const auto& task : tasks) {
        if (task.priority == priority) {
            filteredTasks.push_back(task);
        }
    }
    
    return filteredTasks;
}

DynamicJsonDocument TaskManager::getTasksJSON() {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");
    
    for (const auto& task : tasks) {
        tasksArray.add(task.toJson());
    }
    
    doc["count"] = tasks.size();
    doc["queue_size"] = taskQueue.size();
    doc["is_processing"] = m_isProcessing;
    doc["timestamp"] = millis();
    
    return doc;
}

// ==================== Queue Management ====================

void TaskManager::clearQueue() {
    taskQueue.clear();
    Serial.println("📋 Task queue cleared");
}

void TaskManager::clearCompletedTasks() {
    for (auto it = tasks.begin(); it != tasks.end();) {
        if (it->status == TaskStatus::COMPLETED || it->status == TaskStatus::FAILED) {
            it = tasks.erase(it);
        } else {
            ++it;
        }
    }
    Serial.println("📋 Completed tasks cleared");
}

void TaskManager::clearAllTasks() {
    clearQueue();
    tasks.clear();
    currentTask = nullptr;
    m_isProcessing = false;
    Serial.println("📋 All tasks cleared");
}

// ==================== Status Queries ====================

int TaskManager::getActiveTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == TaskStatus::RUNNING || task.status == TaskStatus::PENDING) {
            count++;
        }
    }
    return count;
}

int TaskManager::getPendingTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == TaskStatus::PENDING) {
            count++;
        }
    }
    return count + taskQueue.size();
}

int TaskManager::getCompletedTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == TaskStatus::COMPLETED) {
            count++;
        }
    }
    return count;
}

int TaskManager::getFailedTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == TaskStatus::FAILED) {
            count++;
        }
    }
    return count;
}

// ==================== Statistics ====================

DynamicJsonDocument TaskManager::getStatistics() const {
    DynamicJsonDocument stats(512);

    stats["total_created"] = totalTasksCreated;
    stats["total_completed"] = totalTasksCompleted;
    stats["total_failed"] = totalTasksFailed;
    stats["active_count"] = getActiveTaskCount();
    stats["pending_count"] = getPendingTaskCount();
    stats["completed_count"] = getCompletedTaskCount();
    stats["failed_count"] = getFailedTaskCount();
    stats["queue_size"] = taskQueue.size();
    stats["is_processing"] = m_isProcessing;
    stats["max_concurrent"] = maxConcurrentTasks;
    stats["timeout_ms"] = taskTimeoutMs;

    if (currentTask) {
        stats["current_task"] = currentTask->id;
    }

    return stats;
}

// ==================== Configuration ====================

void TaskManager::setMaxConcurrentTasks(int maxTasks) {
    maxConcurrentTasks = maxTasks;
    Serial.printf("🔧 Max concurrent tasks set to: %d\n", maxTasks);
}

void TaskManager::setTaskTimeout(unsigned long timeoutMs) {
    taskTimeoutMs = timeoutMs;
    Serial.printf("🔧 Task timeout set to: %lu ms\n", timeoutMs);
}

// ==================== Event Callback Setters ====================

void TaskManager::setOnTaskCompleted(std::function<void(const String&, bool)> callback) {
    onTaskCompleted = callback;
    Serial.println("📋 Task completed callback set");
}

// ==================== Private Methods ====================

String TaskManager::generateTaskId() {
    return "task_" + String(millis()) + "_" + String(random(1000, 9999));
}

bool TaskManager::validateTask(const Task& task) {
    if (task.name.isEmpty()) {
        Serial.println("ERROR: Task name cannot be empty");
        return false;
    }

    if (!isValidTaskType(task.type)) {
        Serial.println("ERROR: Invalid task type");
        return false;
    }

    if (!isValidTaskPriority(task.priority)) {
        Serial.println("ERROR: Invalid task priority");
        return false;
    }

    return true;
}

void TaskManager::sortTaskQueue() {
    // Sort by priority (higher priority first)
    std::sort(taskQueue.begin(), taskQueue.end(), [](const Task& a, const Task& b) {
        return static_cast<int>(a.priority) > static_cast<int>(b.priority);
    });
}

bool TaskManager::executeTask(Task& task) {
    Serial.printf("📋 Executing task: %s (Type: %d)\n", task.name.c_str(), static_cast<int>(task.type));

    bool success = false;

    try {
        switch (task.type) {
            case TaskType::SIGNAL_SEND:
                success = executeSignalSendTask(task);
                break;

            case TaskType::BATCH_SEND:
                success = executeBatchSendTask(task);
                break;

            case TaskType::TIMER_EXECUTE:
                success = executeTimerTask(task);
                break;

            case TaskType::SYSTEM_COMMAND:
                success = executeSystemCommandTask(task);
                break;

            case TaskType::CUSTOM:
                success = executeCustomTask(task);
                break;

            default:
                Serial.printf("ERROR: Unknown task type: %d\n", static_cast<int>(task.type));
                task.error = "Unknown task type";
                success = false;
                break;
        }
    } catch (...) {
        Serial.printf("ERROR: Exception during task execution: %s\n", task.id.c_str());
        task.error = "Exception during execution";
        success = false;
    }

    return success;
}

void TaskManager::completeTask(Task& task, bool success) {
    task.completedAt = millis();

    if (success) {
        task.status = TaskStatus::COMPLETED;
        totalTasksCompleted++;

        Serial.printf("✅ Task completed successfully: %s\n", task.id.c_str());

        if (onTaskCompleted) {
            onTaskCompleted(task.id, true);
        }
    } else {
        task.status = TaskStatus::FAILED;
        totalTasksFailed++;

        Serial.printf("❌ Task failed: %s - %s\n", task.id.c_str(), task.error.c_str());

        if (onTaskFailed) {
            onTaskFailed(task.id, task.error);
        }

        if (onTaskCompleted) {
            onTaskCompleted(task.id, false);
        }
    }
}

void TaskManager::failTask(Task& task, const String& error) {
    task.error = error;
    task.status = TaskStatus::FAILED;
    task.completedAt = millis();
    totalTasksFailed++;

    Serial.printf("❌ Task failed: %s - %s\n", task.id.c_str(), error.c_str());

    if (onTaskFailed) {
        onTaskFailed(task.id, error);
    }
}

// ==================== Task Execution Methods ====================

bool TaskManager::executeSignalSendTask(const Task& task) {
    if (!irController) {
        Serial.println("ERROR: IR Controller not available");
        return false;
    }

    if (!task.parameters.containsKey("signal_id")) {
        Serial.println("ERROR: Signal ID not specified in task parameters");
        return false;
    }

    String signalId = task.parameters["signal_id"];

    Serial.printf("📡 Sending signal: %s\n", signalId.c_str());
    return irController->sendSignal(signalId);
}

bool TaskManager::executeBatchSendTask(const Task& task) {
    if (!irController) {
        Serial.println("ERROR: IR Controller not available");
        return false;
    }

    if (!task.parameters.containsKey("signal_ids")) {
        Serial.println("ERROR: Signal IDs not specified in task parameters");
        return false;
    }

    JsonArrayConst signalIdsArray = task.parameters["signal_ids"];
    int delayMs = task.parameters["delay_ms"] | 500;

    std::vector<String> signalIds;
    for (JsonVariantConst signalId : signalIdsArray) {
        signalIds.push_back(signalId.as<String>());
    }

    if (signalIds.empty()) {
        Serial.println("ERROR: No signal IDs provided for batch send");
        return false;
    }

    Serial.printf("📡 Batch sending %d signals with %d ms delay\n", signalIds.size(), delayMs);
    return irController->batchSendSignals(signalIds, delayMs);
}

bool TaskManager::executeTimerTask(const Task& task) {
    if (!dataManager) {
        Serial.println("ERROR: Data Manager not available");
        return false;
    }

    if (!task.parameters.containsKey("timer_id")) {
        Serial.println("ERROR: Timer ID not specified in task parameters");
        return false;
    }

    String timerId = task.parameters["timer_id"];

    // Get timer data
    TimerData* timer = dataManager->getTimer(timerId);
    if (!timer) {
        Serial.printf("ERROR: Timer %s not found\n", timerId.c_str());
        return false;
    }

    if (!timer->isActive) {
        Serial.printf("ERROR: Timer %s is not active\n", timerId.c_str());
        return false;
    }

    // Execute the timer (send associated signal)
    if (!irController) {
        Serial.println("ERROR: IR Controller not available for timer execution");
        return false;
    }

    Serial.printf("⏰ Executing timer: %s (Signal: %s)\n", timerId.c_str(), timer->signalId.c_str());

    bool success = irController->sendSignal(timer->signalId);

    if (success) {
        // Update timer execution info
        dataManager->executeTimer(timerId);
    }

    return success;
}

bool TaskManager::executeSystemCommandTask(const Task& task) {
    if (!task.parameters.containsKey("command")) {
        Serial.println("ERROR: Command not specified in task parameters");
        return false;
    }

    String command = task.parameters["command"];

    Serial.printf("🔧 Executing system command: %s\n", command.c_str());

    // Handle system commands
    if (command == "restart") {
        Serial.println("🔄 System restart requested");
        delay(1000);
        ESP.restart();
        return true;
    } else if (command == "clear_data") {
        if (dataManager) {
            dataManager->clearAllData();
            Serial.println("🗑️  All data cleared");
            return true;
        }
        return false;
    } else if (command == "backup_data") {
        if (dataManager) {
            return dataManager->backupData();
        }
        return false;
    } else if (command == "test_ir") {
        if (irController) {
            return irController->testHardware();
        }
        return false;
    } else {
        Serial.printf("ERROR: Unknown system command: %s\n", command.c_str());
        return false;
    }
}

bool TaskManager::executeCustomTask(const Task& task) {
    Serial.printf("🔧 Executing custom task: %s\n", task.name.c_str());

    // Custom task implementation can be added here
    // For now, just return success

    return true;
}

// ==================== Helper Methods ====================

int TaskManager::findTaskIndex(const String& taskId) {
    for (size_t i = 0; i < tasks.size(); i++) {
        if (tasks[i].id == taskId) {
            return i;
        }
    }
    return -1;
}

bool TaskManager::isValidTaskType(TaskType type) {
    return (type == TaskType::SIGNAL_SEND ||
            type == TaskType::BATCH_SEND ||
            type == TaskType::TIMER_EXECUTE ||
            type == TaskType::SYSTEM_COMMAND ||
            type == TaskType::CUSTOM);
}

bool TaskManager::isValidTaskStatus(TaskStatus status) {
    return (status == TaskStatus::PENDING ||
            status == TaskStatus::RUNNING ||
            status == TaskStatus::COMPLETED ||
            status == TaskStatus::FAILED ||
            status == TaskStatus::CANCELLED ||
            status == TaskStatus::PAUSED);
}

bool TaskManager::isValidTaskPriority(TaskPriority priority) {
    return (priority == TaskPriority::TASK_LOW ||
            priority == TaskPriority::NORMAL ||
            priority == TaskPriority::TASK_HIGH ||
            priority == TaskPriority::URGENT);
}

// ==================== Task Structure Implementation ====================

DynamicJsonDocument Task::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = static_cast<int>(type);
    doc["status"] = static_cast<int>(status);
    doc["priority"] = static_cast<int>(priority);
    doc["parameters"] = parameters;
    doc["created_at"] = createdAt;
    doc["started_at"] = startedAt;
    doc["completed_at"] = completedAt;
    doc["result"] = result;
    doc["error"] = error;

    return doc;
}

bool Task::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = static_cast<TaskType>(doc["type"] | static_cast<int>(TaskType::CUSTOM));
    status = static_cast<TaskStatus>(doc["status"] | static_cast<int>(TaskStatus::PENDING));
    priority = static_cast<TaskPriority>(doc["priority"] | static_cast<int>(TaskPriority::NORMAL));

    if (doc.containsKey("parameters")) {
        parameters = doc["parameters"];
    }

    createdAt = doc["created_at"] | 0;
    startedAt = doc["started_at"] | 0;
    completedAt = doc["completed_at"] | 0;
    result = doc["result"] | "";
    error = doc["error"] | "";

    return true;
}

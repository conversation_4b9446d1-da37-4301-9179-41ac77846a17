#include "DataManager.h"

// File path constants
const char* DataManager::SIGNALS_FILE = "/data/signals.json";
const char* DataManager::TIMERS_FILE = "/data/timers.json";
const char* DataManager::TASKS_FILE = "/data/tasks.json";
const char* DataManager::CONFIG_FILE = "/data/config.json";

DataManager::DataManager() : m_initialized(false) {
    // Constructor does NO memory allocation
    Serial.println("DataManager created (no memory allocated)");
}

DataManager::~DataManager() {
    // Smart pointers automatically clean up
    Serial.println("DataManager destroyed");
}

bool DataManager::initialize() {
    Serial.println("📋 Initializing DataManager...");
    
    if (m_initialized) {
        Serial.println("DataManager already initialized");
        return true;
    }
    
    // Create storage containers after PSRAM is available
    if (!initializeStorage()) {
        Serial.println("❌ Failed to initialize storage");
        return false;
    }
    
    // Create data directories
    if (!createDataDirectories()) {
        Serial.println("❌ Failed to create data directories");
        return false;
    }
    
    // Load existing data
    if (!loadAllData()) {
        Serial.println("⚠️  Warning: Failed to load some data files");
        // Continue anyway - files might not exist yet
    }
    
    m_initialized = true;
    Serial.printf("✅ DataManager initialized - Signals: %d, Timers: %d, Tasks: %d\n", 
                 getSignalCount(), getTimerCount(), getTaskCount());
    
    return true;
}

bool DataManager::initializeStorage() {
    Serial.println("🗄️  Creating storage containers...");
    
    try {
        m_signals = std::make_unique<std::vector<SignalData>>();
        m_timers = std::make_unique<std::vector<TimerData>>();
        m_tasks = std::make_unique<std::vector<TaskData>>();
        m_config = std::make_unique<std::map<String, String>>();
        m_preferences = std::make_unique<Preferences>();
        
        if (!m_signals || !m_timers || !m_tasks || !m_config || !m_preferences) {
            Serial.println("❌ Failed to create storage containers");
            return false;
        }
        
        Serial.println("✅ Storage containers created successfully");
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception creating storage: %s\n", e.what());
        return false;
    }
}

bool DataManager::createDataDirectories() {
    if (!SPIFFS.exists("/data")) {
        if (!SPIFFS.mkdir("/data")) {
            Serial.println("❌ Failed to create /data directory");
            return false;
        }
        Serial.println("✅ Created /data directory");
    }
    return true;
}

// ==================== Signal Management ====================

bool DataManager::addSignal(const SignalData& signal) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    if (!validateSignalData(signal)) {
        return false;
    }
    
    // Check for duplicate ID
    if (findSignalIndex(signal.id) >= 0) {
        Serial.println("❌ Signal ID already exists");
        return false;
    }
    
    SignalData newSignal = signal;
    if (newSignal.id.isEmpty()) {
        newSignal.id = generateUniqueId("signal");
    }
    newSignal.timestamp = millis();
    
    m_signals->push_back(newSignal);
    saveSignalsToFile();
    
    Serial.printf("✅ Signal added: %s\n", newSignal.name.c_str());
    return true;
}

bool DataManager::removeSignal(const String& id) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.println("❌ Signal not found");
        return false;
    }
    
    m_signals->erase(m_signals->begin() + index);
    saveSignalsToFile();
    
    Serial.printf("✅ Signal removed: %s\n", id.c_str());
    return true;
}

bool DataManager::updateSignal(const String& id, const SignalData& signal) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.println("❌ Signal not found");
        return false;
    }
    
    if (!validateSignalData(signal)) {
        return false;
    }
    
    (*m_signals)[index] = signal;
    (*m_signals)[index].id = id; // Preserve original ID
    saveSignalsToFile();
    
    Serial.printf("✅ Signal updated: %s\n", id.c_str());
    return true;
}

SignalData DataManager::getSignal(const String& id) const {
    if (!m_initialized || !m_signals) {
        return SignalData();
    }
    
    int index = findSignalIndex(id);
    if (index >= 0) {
        return (*m_signals)[index];
    }
    
    return SignalData();
}

DynamicJsonDocument DataManager::getSignalsJSON() const {
    DynamicJsonDocument doc(4096);
    JsonArray signalsArray = doc.createNestedArray("signals");
    
    if (m_initialized && m_signals) {
        for (const auto& signal : *m_signals) {
            if (signal.isActive) {
                signalsArray.add(signal.toJson());
            }
        }
    }
    
    doc["count"] = signalsArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

int DataManager::getSignalCount() const {
    return (m_initialized && m_signals) ? m_signals->size() : 0;
}

void DataManager::clearAllSignals() {
    if (m_initialized && m_signals) {
        m_signals->clear();
        saveSignalsToFile();
        Serial.println("✅ All signals cleared");
    }
}

// ==================== Timer Management ====================

bool DataManager::addTimer(const TimerData& timer) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    if (!validateTimerData(timer)) {
        return false;
    }
    
    TimerData newTimer = timer;
    if (newTimer.id.isEmpty()) {
        newTimer.id = generateUniqueId("timer");
    }
    newTimer.timestamp = millis();
    
    m_timers->push_back(newTimer);
    saveTimersToFile();
    
    Serial.printf("✅ Timer added: %s\n", newTimer.name.c_str());
    return true;
}

bool DataManager::removeTimer(const String& id) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    int index = findTimerIndex(id);
    if (index < 0) {
        return false;
    }
    
    m_timers->erase(m_timers->begin() + index);
    saveTimersToFile();
    
    return true;
}

bool DataManager::updateTimer(const String& id, const TimerData& timer) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    int index = findTimerIndex(id);
    if (index < 0) {
        return false;
    }
    
    (*m_timers)[index] = timer;
    (*m_timers)[index].id = id;
    saveTimersToFile();
    
    return true;
}

TimerData DataManager::getTimer(const String& id) const {
    if (!m_initialized || !m_timers) {
        return TimerData();
    }
    
    int index = findTimerIndex(id);
    if (index >= 0) {
        return (*m_timers)[index];
    }
    
    return TimerData();
}

DynamicJsonDocument DataManager::getTimersJSON() const {
    DynamicJsonDocument doc(2048);
    JsonArray timersArray = doc.createNestedArray("timers");
    
    if (m_initialized && m_timers) {
        for (const auto& timer : *m_timers) {
            if (timer.isActive) {
                timersArray.add(timer.toJson());
            }
        }
    }
    
    doc["count"] = timersArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

int DataManager::getTimerCount() const {
    return (m_initialized && m_timers) ? m_timers->size() : 0;
}

void DataManager::clearAllTimers() {
    if (m_initialized && m_timers) {
        m_timers->clear();
        saveTimersToFile();
        Serial.println("✅ All timers cleared");
    }
}

// ==================== Task Management ====================

bool DataManager::addTask(const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    if (!validateTaskData(task)) {
        return false;
    }

    TaskData newTask = task;
    if (newTask.id.isEmpty()) {
        newTask.id = generateUniqueId("task");
    }
    newTask.timestamp = millis();

    m_tasks->push_back(newTask);
    saveTasksToFile();

    Serial.printf("✅ Task added: %s\n", newTask.name.c_str());
    return true;
}

bool DataManager::removeTask(const String& id) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    m_tasks->erase(m_tasks->begin() + index);
    saveTasksToFile();

    return true;
}

bool DataManager::updateTask(const String& id, const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    (*m_tasks)[index] = task;
    (*m_tasks)[index].id = id;
    saveTasksToFile();

    return true;
}

TaskData DataManager::getTask(const String& id) const {
    if (!m_initialized || !m_tasks) {
        return TaskData();
    }

    int index = findTaskIndex(id);
    if (index >= 0) {
        return (*m_tasks)[index];
    }

    return TaskData();
}

DynamicJsonDocument DataManager::getTasksJSON() const {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    if (m_initialized && m_tasks) {
        for (const auto& task : *m_tasks) {
            if (task.isActive) {
                tasksArray.add(task.toJson());
            }
        }
    }

    doc["count"] = tasksArray.size();
    doc["timestamp"] = millis();

    return doc;
}

int DataManager::getTaskCount() const {
    return (m_initialized && m_tasks) ? m_tasks->size() : 0;
}

void DataManager::clearAllTasks() {
    if (m_initialized && m_tasks) {
        m_tasks->clear();
        saveTasksToFile();
        Serial.println("✅ All tasks cleared");
    }
}

// ==================== Configuration Management ====================

bool DataManager::setConfig(const String& key, const String& value) {
    if (!m_initialized || !m_config) {
        return false;
    }

    (*m_config)[key] = value;
    saveConfigToFile();

    return true;
}

String DataManager::getConfig(const String& key, const String& defaultValue) const {
    if (!m_initialized || !m_config) {
        return defaultValue;
    }

    auto it = m_config->find(key);
    if (it != m_config->end()) {
        return it->second;
    }

    return defaultValue;
}

bool DataManager::hasConfig(const String& key) const {
    if (!m_initialized || !m_config) {
        return false;
    }

    return m_config->find(key) != m_config->end();
}

DynamicJsonDocument DataManager::getConfigJSON() const {
    DynamicJsonDocument doc(1024);

    if (m_initialized && m_config) {
        for (const auto& pair : *m_config) {
            doc[pair.first] = pair.second;
        }
    }

    doc["timestamp"] = millis();

    return doc;
}

bool DataManager::setConfigJSON(const DynamicJsonDocument& config) {
    if (!m_initialized || !m_config) {
        return false;
    }

    m_config->clear();

    for (JsonPair pair : config.as<JsonObject>()) {
        if (strcmp(pair.key().c_str(), "timestamp") != 0) {
            (*m_config)[pair.key().c_str()] = pair.value().as<String>();
        }
    }

    saveConfigToFile();
    return true;
}

// ==================== Data Persistence ====================

bool DataManager::saveAllData() {
    bool success = true;

    if (!saveSignalsToFile()) {
        Serial.println("❌ Failed to save signals");
        success = false;
    }

    if (!saveTimersToFile()) {
        Serial.println("❌ Failed to save timers");
        success = false;
    }

    if (!saveTasksToFile()) {
        Serial.println("❌ Failed to save tasks");
        success = false;
    }

    if (!saveConfigToFile()) {
        Serial.println("❌ Failed to save config");
        success = false;
    }

    if (success) {
        Serial.println("✅ All data saved successfully");
    }

    return success;
}

bool DataManager::loadAllData() {
    bool success = true;

    if (!loadSignalsFromFile()) {
        Serial.println("⚠️  Failed to load signals");
        success = false;
    }

    if (!loadTimersFromFile()) {
        Serial.println("⚠️  Failed to load timers");
        success = false;
    }

    if (!loadTasksFromFile()) {
        Serial.println("⚠️  Failed to load tasks");
        success = false;
    }

    if (!loadConfigFromFile()) {
        Serial.println("⚠️  Failed to load config");
        success = false;
    }

    return success;
}

void DataManager::clearAllData() {
    clearAllSignals();
    clearAllTimers();
    clearAllTasks();

    if (m_config) {
        m_config->clear();
        saveConfigToFile();
    }

    Serial.println("✅ All data cleared");
}

// ==================== System Information ====================

size_t DataManager::getUsedSpace() const {
    return SPIFFS.usedBytes();
}

size_t DataManager::getFreeSpace() const {
    return SPIFFS.totalBytes() - SPIFFS.usedBytes();
}

DynamicJsonDocument DataManager::getSystemStatus() const {
    DynamicJsonDocument doc(512);

    doc["initialized"] = m_initialized;
    doc["signals_count"] = getSignalCount();
    doc["timers_count"] = getTimerCount();
    doc["tasks_count"] = getTaskCount();
    doc["config_count"] = m_config ? m_config->size() : 0;

    doc["storage"]["total_bytes"] = SPIFFS.totalBytes();
    doc["storage"]["used_bytes"] = SPIFFS.usedBytes();
    doc["storage"]["free_bytes"] = getFreeSpace();
    doc["storage"]["usage_percent"] = (float)SPIFFS.usedBytes() / SPIFFS.totalBytes() * 100;

    doc["memory"]["free_heap"] = ESP.getFreeHeap();
    doc["memory"]["free_psram"] = psramFound() ? ESP.getFreePsram() : 0;

    doc["timestamp"] = millis();

    return doc;
}

// ==================== Helper Methods ====================

String DataManager::generateUniqueId(const String& prefix) const {
    return prefix + "_" + String(millis()) + "_" + String(random(1000, 9999));
}

int DataManager::findSignalIndex(const String& id) const {
    if (!m_signals) return -1;

    for (size_t i = 0; i < m_signals->size(); i++) {
        if ((*m_signals)[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTimerIndex(const String& id) const {
    if (!m_timers) return -1;

    for (size_t i = 0; i < m_timers->size(); i++) {
        if ((*m_timers)[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTaskIndex(const String& id) const {
    if (!m_tasks) return -1;

    for (size_t i = 0; i < m_tasks->size(); i++) {
        if ((*m_tasks)[i].id == id) {
            return i;
        }
    }
    return -1;
}

// ==================== Validation Methods ====================

bool DataManager::validateSignalData(const SignalData& signal) const {
    if (signal.name.isEmpty()) {
        Serial.println("❌ Signal name cannot be empty");
        return false;
    }

    if (signal.type.isEmpty()) {
        Serial.println("❌ Signal type cannot be empty");
        return false;
    }

    if (signal.data.isEmpty() && signal.rawData.isEmpty()) {
        Serial.println("❌ Signal must have either data or raw data");
        return false;
    }

    return true;
}

bool DataManager::validateTimerData(const TimerData& timer) const {
    if (timer.name.isEmpty()) {
        Serial.println("❌ Timer name cannot be empty");
        return false;
    }

    if (timer.signalId.isEmpty()) {
        Serial.println("❌ Timer signal ID cannot be empty");
        return false;
    }

    if (timer.schedule.isEmpty()) {
        Serial.println("❌ Timer schedule cannot be empty");
        return false;
    }

    return true;
}

bool DataManager::validateTaskData(const TaskData& task) const {
    if (task.name.isEmpty()) {
        Serial.println("❌ Task name cannot be empty");
        return false;
    }

    if (task.type.isEmpty()) {
        Serial.println("❌ Task type cannot be empty");
        return false;
    }

    return true;
}

// ==================== File Operations ====================

bool DataManager::loadSignalsFromFile() {
    if (!SPIFFS.exists(SIGNALS_FILE)) {
        Serial.println("Signals file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(SIGNALS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open signals file");
        return false;
    }

    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse signals file: %s\n", error.c_str());
        return false;
    }

    if (m_signals) {
        m_signals->clear();

        if (doc.containsKey("signals")) {
            JsonArrayConst signalsArray = doc["signals"];
            for (JsonVariantConst signalVariant : signalsArray) {
                SignalData signal;
                DynamicJsonDocument signalDoc(512);
                signalDoc.set(signalVariant);
                if (signal.fromJson(signalDoc)) {
                    m_signals->push_back(signal);
                }
            }
        }

        Serial.printf("✅ Loaded %d signals from file\n", m_signals->size());
    }

    return true;
}

bool DataManager::saveSignalsToFile() {
    if (!m_signals) {
        return false;
    }

    DynamicJsonDocument doc(4096);
    JsonArray signalsArray = doc.createNestedArray("signals");

    for (const auto& signal : *m_signals) {
        signalsArray.add(signal.toJson());
    }

    doc["count"] = m_signals->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(SIGNALS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open signals file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTimersFromFile() {
    if (!SPIFFS.exists(TIMERS_FILE)) {
        Serial.println("Timers file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TIMERS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open timers file");
        return false;
    }

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse timers file: %s\n", error.c_str());
        return false;
    }

    if (m_timers) {
        m_timers->clear();

        if (doc.containsKey("timers")) {
            JsonArrayConst timersArray = doc["timers"];
            for (JsonVariantConst timerVariant : timersArray) {
                TimerData timer;
                DynamicJsonDocument timerDoc(256);
                timerDoc.set(timerVariant);
                if (timer.fromJson(timerDoc)) {
                    m_timers->push_back(timer);
                }
            }
        }

        Serial.printf("✅ Loaded %d timers from file\n", m_timers->size());
    }

    return true;
}

bool DataManager::saveTimersToFile() {
    if (!m_timers) {
        return false;
    }

    DynamicJsonDocument doc(2048);
    JsonArray timersArray = doc.createNestedArray("timers");

    for (const auto& timer : *m_timers) {
        timersArray.add(timer.toJson());
    }

    doc["count"] = m_timers->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TIMERS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open timers file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTasksFromFile() {
    if (!SPIFFS.exists(TASKS_FILE)) {
        Serial.println("Tasks file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TASKS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open tasks file");
        return false;
    }

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse tasks file: %s\n", error.c_str());
        return false;
    }

    if (m_tasks) {
        m_tasks->clear();

        if (doc.containsKey("tasks")) {
            JsonArrayConst tasksArray = doc["tasks"];
            for (JsonVariantConst taskVariant : tasksArray) {
                TaskData task;
                DynamicJsonDocument taskDoc(512);
                taskDoc.set(taskVariant);
                if (task.fromJson(taskDoc)) {
                    m_tasks->push_back(task);
                }
            }
        }

        Serial.printf("✅ Loaded %d tasks from file\n", m_tasks->size());
    }

    return true;
}

bool DataManager::saveTasksToFile() {
    if (!m_tasks) {
        return false;
    }

    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    for (const auto& task : *m_tasks) {
        tasksArray.add(task.toJson());
    }

    doc["count"] = m_tasks->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TASKS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open tasks file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadConfigFromFile() {
    if (!SPIFFS.exists(CONFIG_FILE)) {
        Serial.println("Config file not found, using default configuration");

        // Set default configuration
        if (m_config) {
            (*m_config)["system_name"] = "ESP32-S3 IR Control System";
            (*m_config)["version"] = "2.0.0";
            (*m_config)["ir_frequency"] = "38000";
            (*m_config)["learning_timeout"] = "30000";
            saveConfigToFile();
        }

        return true;
    }

    File file = SPIFFS.open(CONFIG_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open config file");
        return false;
    }

    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse config file: %s\n", error.c_str());
        return false;
    }

    if (m_config) {
        m_config->clear();

        for (JsonPair pair : doc.as<JsonObject>()) {
            if (strcmp(pair.key().c_str(), "timestamp") != 0) {
                (*m_config)[pair.key().c_str()] = pair.value().as<String>();
            }
        }

        Serial.printf("✅ Loaded %d config items from file\n", m_config->size());
    }

    return true;
}

bool DataManager::saveConfigToFile() {
    if (!m_config) {
        return false;
    }

    DynamicJsonDocument doc(1024);

    for (const auto& pair : *m_config) {
        doc[pair.first] = pair.second;
    }

    doc["timestamp"] = millis();

    File file = SPIFFS.open(CONFIG_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open config file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

// ==================== Data Structure Implementations ====================

DynamicJsonDocument SignalData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["protocol"] = protocol;
    doc["data"] = data;
    doc["raw_data"] = rawData;
    doc["frequency"] = frequency;
    doc["timestamp"] = timestamp;
    doc["is_active"] = isActive;
    doc["use_count"] = useCount;
    doc["description"] = description;

    return doc;
}

bool SignalData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "other";
    protocol = doc["protocol"] | "";
    data = doc["data"] | "";
    rawData = doc["raw_data"] | "";
    frequency = doc["frequency"] | 38000;
    timestamp = doc["timestamp"] | 0;
    isActive = doc["is_active"] | true;
    useCount = doc["use_count"] | 0;
    description = doc["description"] | "";

    return true;
}

DynamicJsonDocument TimerData::toJson() const {
    DynamicJsonDocument doc(256);

    doc["id"] = id;
    doc["name"] = name;
    doc["signal_id"] = signalId;
    doc["schedule"] = schedule;
    doc["is_active"] = isActive;
    doc["timestamp"] = timestamp;
    doc["last_executed"] = lastExecuted;
    doc["execution_count"] = executionCount;

    return doc;
}

bool TimerData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    signalId = doc["signal_id"] | "";
    schedule = doc["schedule"] | "";
    isActive = doc["is_active"] | true;
    timestamp = doc["timestamp"] | 0;
    lastExecuted = doc["last_executed"] | 0;
    executionCount = doc["execution_count"] | 0;

    return true;
}

DynamicJsonDocument TaskData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["config"] = config;
    doc["is_active"] = isActive;
    doc["timestamp"] = timestamp;
    doc["last_executed"] = lastExecuted;
    doc["execution_count"] = executionCount;

    return doc;
}

bool TaskData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "";
    config = doc["config"] | "";
    isActive = doc["is_active"] | true;
    timestamp = doc["timestamp"] | 0;
    lastExecuted = doc["last_executed"] | 0;
    executionCount = doc["execution_count"] | 0;

    return true;
}

// ==================== Task Management ====================

bool DataManager::addTask(const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    if (!validateTaskData(task)) {
        return false;
    }

    TaskData newTask = task;
    if (newTask.id.isEmpty()) {
        newTask.id = generateUniqueId("task");
    }
    newTask.timestamp = millis();

    m_tasks->push_back(newTask);
    saveTasksToFile();

    Serial.printf("✅ Task added: %s\n", newTask.name.c_str());
    return true;
}

bool DataManager::removeTask(const String& id) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    m_tasks->erase(m_tasks->begin() + index);
    saveTasksToFile();

    return true;
}

bool DataManager::updateTask(const String& id, const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    (*m_tasks)[index] = task;
    (*m_tasks)[index].id = id;
    saveTasksToFile();

    return true;
}

TaskData DataManager::getTask(const String& id) const {
    if (!m_initialized || !m_tasks) {
        return TaskData();
    }

    int index = findTaskIndex(id);
    if (index >= 0) {
        return (*m_tasks)[index];
    }

    return TaskData();
}

DynamicJsonDocument DataManager::getTasksJSON() const {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    if (m_initialized && m_tasks) {
        for (const auto& task : *m_tasks) {
            if (task.isActive) {
                tasksArray.add(task.toJson());
            }
        }
    }

    doc["count"] = tasksArray.size();
    doc["timestamp"] = millis();

    return doc;
}

int DataManager::getTaskCount() const {
    return (m_initialized && m_tasks) ? m_tasks->size() : 0;
}

void DataManager::clearAllTasks() {
    if (m_initialized && m_tasks) {
        m_tasks->clear();
        saveTasksToFile();
        Serial.println("✅ All tasks cleared");
    }
}

// ==================== Task Management ====================

bool DataManager::addTask(const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    if (!validateTaskData(task)) {
        return false;
    }

    TaskData newTask = task;
    if (newTask.id.isEmpty()) {
        newTask.id = generateUniqueId("task");
    }
    newTask.timestamp = millis();

    m_tasks->push_back(newTask);
    saveTasksToFile();

    Serial.printf("✅ Task added: %s\n", newTask.name.c_str());
    return true;
}

bool DataManager::removeTask(const String& id) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    m_tasks->erase(m_tasks->begin() + index);
    saveTasksToFile();

    return true;
}

bool DataManager::updateTask(const String& id, const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    (*m_tasks)[index] = task;
    (*m_tasks)[index].id = id;
    saveTasksToFile();

    return true;
}

TaskData DataManager::getTask(const String& id) const {
    if (!m_initialized || !m_tasks) {
        return TaskData();
    }

    int index = findTaskIndex(id);
    if (index >= 0) {
        return (*m_tasks)[index];
    }

    return TaskData();
}

DynamicJsonDocument DataManager::getTasksJSON() const {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    if (m_initialized && m_tasks) {
        for (const auto& task : *m_tasks) {
            if (task.isActive) {
                tasksArray.add(task.toJson());
            }
        }
    }

    doc["count"] = tasksArray.size();
    doc["timestamp"] = millis();

    return doc;
}

int DataManager::getTaskCount() const {
    return (m_initialized && m_tasks) ? m_tasks->size() : 0;
}

void DataManager::clearAllTasks() {
    if (m_initialized && m_tasks) {
        m_tasks->clear();
        saveTasksToFile();
        Serial.println("✅ All tasks cleared");
    }
}

// ==================== Configuration Management ====================

bool DataManager::setConfig(const String& key, const String& value) {
    if (!m_initialized || !m_config) {
        return false;
    }

    (*m_config)[key] = value;
    saveConfigToFile();

    return true;
}

String DataManager::getConfig(const String& key, const String& defaultValue) const {
    if (!m_initialized || !m_config) {
        return defaultValue;
    }

    auto it = m_config->find(key);
    if (it != m_config->end()) {
        return it->second;
    }

    return defaultValue;
}

bool DataManager::hasConfig(const String& key) const {
    if (!m_initialized || !m_config) {
        return false;
    }

    return m_config->find(key) != m_config->end();
}

DynamicJsonDocument DataManager::getConfigJSON() const {
    DynamicJsonDocument doc(1024);

    if (m_initialized && m_config) {
        for (const auto& pair : *m_config) {
            doc[pair.first] = pair.second;
        }
    }

    doc["timestamp"] = millis();

    return doc;
}

bool DataManager::setConfigJSON(const DynamicJsonDocument& config) {
    if (!m_initialized || !m_config) {
        return false;
    }

    m_config->clear();

    for (JsonPair pair : config.as<JsonObject>()) {
        if (strcmp(pair.key().c_str(), "timestamp") != 0) {
            (*m_config)[pair.key().c_str()] = pair.value().as<String>();
        }
    }

    saveConfigToFile();
    return true;
}

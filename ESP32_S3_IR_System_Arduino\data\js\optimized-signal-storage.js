/**
 * 优化的信号存储系统 - 高性能信号管理
 */
class OptimizedSignalStorage {
  constructor() {
    this.signals = new Map();
    this.activeSignals = new Set(); // 常用信号快速访问
    this.signalsByType = new Map(); // 按类型索引
    this.signalsByProtocol = new Map(); // 按协议索引
    this.searchIndex = new Map(); // 搜索索引
    this.maxActive = 100; // 活跃信号数量限制
    this.accessCount = new Map(); // 访问计数
    this.performance = {
      hits: 0,
      misses: 0,
      evictions: 0,
      indexUpdates: 0
    };
  }

  /**
   * 添加信号
   */
  addSignal(signal) {
    this.signals.set(signal.id, signal);
    this.updateIndexes(signal);
    this.updateSearchIndex(signal);
    
    // 智能缓存管理
    if (this.activeSignals.size >= this.maxActive) {
      this.evictLeastUsed();
    }
    this.activeSignals.add(signal.id);
    this.accessCount.set(signal.id, 0);
  }

  /**
   * 获取信号
   */
  getSignal(id) {
    if (this.signals.has(id)) {
      this.performance.hits++;
      this.markAccessed(id);
      return this.signals.get(id);
    }
    this.performance.misses++;
    return null;
  }

  /**
   * 按类型获取信号
   */
  getSignalsByType(type) {
    const signalIds = this.signalsByType.get(type) || [];
    return signalIds.map(id => this.signals.get(id)).filter(Boolean);
  }

  /**
   * 按协议获取信号
   */
  getSignalsByProtocol(protocol) {
    const signalIds = this.signalsByProtocol.get(protocol) || [];
    return signalIds.map(id => this.signals.get(id)).filter(Boolean);
  }

  /**
   * 快速搜索信号
   */
  searchSignals(query) {
    if (!query || query.length < 2) {
      return Array.from(this.signals.values());
    }

    const lowerQuery = query.toLowerCase();
    const results = new Set();

    // 搜索索引
    for (const [term, signalIds] of this.searchIndex) {
      if (term.includes(lowerQuery)) {
        signalIds.forEach(id => results.add(id));
      }
    }

    return Array.from(results).map(id => this.signals.get(id)).filter(Boolean);
  }

  /**
   * 获取所有信号（优化版本）
   */
  getAllSignals() {
    return Array.from(this.signals.values());
  }

  /**
   * 获取活跃信号
   */
  getActiveSignals() {
    return Array.from(this.activeSignals).map(id => this.signals.get(id)).filter(Boolean);
  }

  /**
   * 更新索引
   */
  updateIndexes(signal) {
    this.performance.indexUpdates++;

    // 类型索引
    if (!this.signalsByType.has(signal.type)) {
      this.signalsByType.set(signal.type, []);
    }
    this.signalsByType.get(signal.type).push(signal.id);

    // 协议索引
    if (signal.protocol) {
      if (!this.signalsByProtocol.has(signal.protocol)) {
        this.signalsByProtocol.set(signal.protocol, []);
      }
      this.signalsByProtocol.get(signal.protocol).push(signal.id);
    }
  }

  /**
   * 更新搜索索引
   */
  updateSearchIndex(signal) {
    const terms = [
      signal.name?.toLowerCase(),
      signal.type?.toLowerCase(),
      signal.protocol?.toLowerCase(),
      signal.signalCode?.toLowerCase()
    ].filter(Boolean);

    for (const term of terms) {
      if (!this.searchIndex.has(term)) {
        this.searchIndex.set(term, new Set());
      }
      this.searchIndex.get(term).add(signal.id);
    }
  }

  /**
   * 标记访问
   */
  markAccessed(id) {
    const count = this.accessCount.get(id) || 0;
    this.accessCount.set(id, count + 1);
    this.activeSignals.add(id);
  }

  /**
   * 淘汰最少使用的信号
   */
  evictLeastUsed() {
    if (this.activeSignals.size <= this.maxActive) return;

    // 找到访问次数最少的信号
    let leastUsedId = null;
    let minAccess = Infinity;

    for (const id of this.activeSignals) {
      const accessCount = this.accessCount.get(id) || 0;
      if (accessCount < minAccess) {
        minAccess = accessCount;
        leastUsedId = id;
      }
    }

    if (leastUsedId) {
      this.activeSignals.delete(leastUsedId);
      this.performance.evictions++;
    }
  }

  /**
   * 删除信号
   */
  removeSignal(id) {
    const signal = this.signals.get(id);
    if (!signal) return false;

    // 从主存储删除
    this.signals.delete(id);
    this.activeSignals.delete(id);
    this.accessCount.delete(id);

    // 从索引删除
    this.removeFromIndexes(signal);
    this.removeFromSearchIndex(signal);

    return true;
  }

  /**
   * 从索引中删除
   */
  removeFromIndexes(signal) {
    // 类型索引
    const typeSignals = this.signalsByType.get(signal.type);
    if (typeSignals) {
      const index = typeSignals.indexOf(signal.id);
      if (index > -1) {
        typeSignals.splice(index, 1);
      }
    }

    // 协议索引
    if (signal.protocol) {
      const protocolSignals = this.signalsByProtocol.get(signal.protocol);
      if (protocolSignals) {
        const index = protocolSignals.indexOf(signal.id);
        if (index > -1) {
          protocolSignals.splice(index, 1);
        }
      }
    }
  }

  /**
   * 从搜索索引删除
   */
  removeFromSearchIndex(signal) {
    const terms = [
      signal.name?.toLowerCase(),
      signal.type?.toLowerCase(),
      signal.protocol?.toLowerCase(),
      signal.signalCode?.toLowerCase()
    ].filter(Boolean);

    for (const term of terms) {
      const termSignals = this.searchIndex.get(term);
      if (termSignals) {
        termSignals.delete(signal.id);
        if (termSignals.size === 0) {
          this.searchIndex.delete(term);
        }
      }
    }
  }

  /**
   * 清空所有数据
   */
  clear() {
    this.signals.clear();
    this.activeSignals.clear();
    this.signalsByType.clear();
    this.signalsByProtocol.clear();
    this.searchIndex.clear();
    this.accessCount.clear();
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalSignals: this.signals.size,
      activeSignals: this.activeSignals.size,
      typeIndexes: this.signalsByType.size,
      protocolIndexes: this.signalsByProtocol.size,
      searchTerms: this.searchIndex.size,
      performance: this.performance,
      hitRate: this.performance.hits + this.performance.misses > 0 
        ? (this.performance.hits / (this.performance.hits + this.performance.misses) * 100).toFixed(1) + '%'
        : '0%'
    };
  }

  /**
   * 优化索引（定期维护）
   */
  optimizeIndexes() {
    // 清理空的索引项
    for (const [type, signalIds] of this.signalsByType) {
      if (signalIds.length === 0) {
        this.signalsByType.delete(type);
      }
    }

    for (const [protocol, signalIds] of this.signalsByProtocol) {
      if (signalIds.length === 0) {
        this.signalsByProtocol.delete(protocol);
      }
    }

    for (const [term, signalIds] of this.searchIndex) {
      if (signalIds.size === 0) {
        this.searchIndex.delete(term);
      }
    }
  }
}

// 导出类
window.OptimizedSignalStorage = OptimizedSignalStorage;

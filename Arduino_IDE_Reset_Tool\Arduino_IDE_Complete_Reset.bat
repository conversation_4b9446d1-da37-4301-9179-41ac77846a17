@echo off
echo ========================================
echo Arduino IDE Complete Reset Tool
echo ========================================
echo.
echo This tool will:
echo 1. Close all Arduino IDE processes
echo 2. Clear all cache and temp files
echo 3. Reset all configurations
echo 4. Clear ESP32 package cache
echo 5. Reset library cache
echo.
echo WARNING: This will delete all Arduino IDE settings and cache
echo.
set /p confirm="Confirm reset? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled
    pause
    exit /b
)

echo.
echo Starting reset process...
echo.

REM 1. Close Arduino IDE processes
echo 1. Closing Arduino IDE processes...
taskkill /f /im arduino.exe 2>nul
taskkill /f /im arduino-ide.exe 2>nul
taskkill /f /im ArduinoIDE.exe 2>nul
timeout /t 2 >nul

REM 2. Clear Arduino IDE cache and config
echo 2. Clearing Arduino IDE cache...
if exist "%USERPROFILE%\AppData\Local\arduino" (
    rmdir /s /q "%USERPROFILE%\AppData\Local\arduino" 2>nul
    echo    Deleted local Arduino cache
)

if exist "%USERPROFILE%\AppData\Local\Arduino15" (
    rmdir /s /q "%USERPROFILE%\AppData\Local\Arduino15" 2>nul
    echo    Deleted Arduino15 config
)

if exist "%USERPROFILE%\AppData\Roaming\Arduino" (
    rmdir /s /q "%USERPROFILE%\AppData\Roaming\Arduino" 2>nul
    echo    Deleted Arduino roaming config
)

if exist "%USERPROFILE%\AppData\Roaming\arduino-ide" (
    rmdir /s /q "%USERPROFILE%\AppData\Roaming\arduino-ide" 2>nul
    echo    Deleted Arduino IDE config
)

REM 3. Clear temp files
echo 3. Clearing temp files...
if exist "%TEMP%\arduino" (
    rmdir /s /q "%TEMP%\arduino" 2>nul
    echo    Deleted temp Arduino files
)

for /f "delims=" %%i in ('dir /b "%TEMP%\arduino_*" 2^>nul') do (
    rmdir /s /q "%TEMP%\%%i" 2>nul
    echo    Deleted temp file: %%i
)

REM 4. Clear compile cache
echo 4. Clearing compile cache...
for /f "delims=" %%i in ('dir /b "%USERPROFILE%\AppData\Local\Temp\arduino_*" 2^>nul') do (
    rmdir /s /q "%USERPROFILE%\AppData\Local\Temp\%%i" 2>nul
    echo    Deleted compile cache: %%i
)

REM 5. Clear ESP32 cache
echo 5. Clearing ESP32 cache...
if exist "%USERPROFILE%\.espressif" (
    rmdir /s /q "%USERPROFILE%\.espressif" 2>nul
    echo    Deleted ESP32 toolchain cache
)

if exist "%USERPROFILE%\.platformio" (
    rmdir /s /q "%USERPROFILE%\.platformio" 2>nul
    echo    Deleted PlatformIO cache
)

REM 6. Clear registry
echo 6. Clearing registry...
reg delete "HKCU\Software\Arduino" /f 2>nul
reg delete "HKCU\Software\arduino-ide" /f 2>nul
echo    Cleared registry entries

echo.
echo Arduino IDE Reset Complete!
echo.
echo Next steps:
echo 1. Restart Arduino IDE
echo 2. Reinstall ESP32 package
echo 3. Reconfigure board settings
echo.
echo Reset tool completed
pause

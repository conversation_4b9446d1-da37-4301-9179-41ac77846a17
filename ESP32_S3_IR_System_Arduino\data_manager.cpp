/*
 * Data Manager Implementation - ESP32-S3 IR Control System
 * Fully compatible with ArduinoJson 6.21.3 API
 * Handles signal, timer, and task data with SPIFFS storage
 */

#include "data_manager.h"

// File path constants
const char* DataManager::SIGNALS_FILE = "/data/signals.json";
const char* DataManager::TIMERS_FILE = "/data/timers.json";
const char* DataManager::TASKS_FILE = "/data/tasks.json";
const char* DataManager::CONFIG_FILE = "/data/config.json";

DataManager::DataManager() {
    prefs = new Preferences();
    // Initialize vector pointers to nullptr - will be created in initialize()
    signals = nullptr;
    timers = nullptr;
    tasks = nullptr;
    configCache = nullptr;
}

DataManager::~DataManager() {
    if (prefs) {
        delete prefs;
    }
    if (signals) {
        delete signals;
    }
    if (timers) {
        delete timers;
    }
    if (tasks) {
        delete tasks;
    }
    if (configCache) {
        delete configCache;
    }
}

bool DataManager::initialize() {
    Serial.println("Initializing Data Manager...");

    // Create vector objects after PSRAM is available
    signals = new std::vector<SignalData>();
    timers = new std::vector<TimerData>();
    tasks = new std::vector<TaskData>();
    configCache = new std::vector<ConfigItem>();

    // Check file system
    if (!checkFileSystem()) {
        Serial.println("ERROR: File system check failed");
        return false;
    }

    // Create data directories
    if (!createDataDirectories()) {
        Serial.println("ERROR: Failed to create data directories");
        return false;
    }
    
    // Load all data
    loadSignalsFromFile();
    loadTimersFromFile();
    loadTasksFromFile();
    loadSystemConfig();
    
    Serial.printf("Data Manager initialized - Signals: %d, Timers: %d, Tasks: %d\n",
                 signals->size(), timers->size(), tasks->size());
    
    return true;
}

// ==================== Signal Management ====================

DynamicJsonDocument DataManager::getSignalsJSON() {
    DynamicJsonDocument doc(4096);
    JsonArray signalsArray = doc.createNestedArray("signals");
    
    if (signals) {
        for (const auto& signal : *signals) {
            if (signal.isActive) {
                signalsArray.add(signal.toJson());
            }
        }
    }
    
    doc["count"] = signalsArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

DynamicJsonDocument DataManager::getAllSignalsJSON() {
    DynamicJsonDocument doc(4096);
    JsonArray signalsArray = doc.createNestedArray("signals");
    
    for (const auto& signal : signals) {
        signalsArray.add(signal.toJson());
    }
    
    doc["count"] = signalsArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

SignalData* DataManager::getSignal(const String& id) {
    int index = findSignalIndex(id);
    if (index >= 0) {
        return &signals[index];
    }
    return nullptr;
}

String DataManager::createSignal(const DynamicJsonDocument& signalData) {
    SignalData newSignal;
    
    if (!newSignal.fromJson(signalData)) {
        Serial.println("ERROR: Invalid signal data format");
        return "";
    }
    
    // Generate unique ID if not provided
    if (newSignal.id.isEmpty()) {
        newSignal.id = generateUniqueId("signal");
    }
    
    // Check if signal already exists
    if (signalExists(newSignal.id)) {
        Serial.printf("ERROR: Signal with ID %s already exists\n", newSignal.id.c_str());
        return "";
    }
    
    // Validate signal data
    if (!validateSignalData(newSignal)) {
        Serial.println("ERROR: Signal data validation failed");
        return "";
    }
    
    // Set timestamp
    newSignal.timestamp = millis();
    
    // Add to collection
    if (signals) {
        signals->push_back(newSignal);
    }
    
    // Save to file
    saveSignalsToFile();
    
    Serial.printf("Signal created: %s (%s)\n", newSignal.name.c_str(), newSignal.id.c_str());
    return newSignal.id;
}

bool DataManager::updateSignal(const String& id, const DynamicJsonDocument& signalData) {
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Signal %s not found\n", id.c_str());
        return false;
    }
    
    SignalData updatedSignal;
    if (!updatedSignal.fromJson(signalData)) {
        Serial.println("ERROR: Invalid signal data format");
        return false;
    }
    
    // Preserve original ID and timestamp
    updatedSignal.id = id;
    updatedSignal.timestamp = signals[index].timestamp;
    
    // Validate updated data
    if (!validateSignalData(updatedSignal)) {
        Serial.println("ERROR: Signal data validation failed");
        return false;
    }
    
    // Update signal
    signals[index] = updatedSignal;
    
    // Save to file
    saveSignalsToFile();
    
    Serial.printf("Signal updated: %s\n", id.c_str());
    return true;
}

bool DataManager::deleteSignal(const String& id) {
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Signal %s not found\n", id.c_str());
        return false;
    }
    
    // Remove from collection
    if (signals && index < signals->size()) {
        signals->erase(signals->begin() + index);
    }
    
    // Save to file
    saveSignalsToFile();
    
    Serial.printf("Signal deleted: %s\n", id.c_str());
    return true;
}

bool DataManager::signalExists(const String& id) {
    return findSignalIndex(id) >= 0;
}

// ==================== Timer Management ====================

DynamicJsonDocument DataManager::getTimersJSON() {
    DynamicJsonDocument doc(2048);
    JsonArray timersArray = doc.createNestedArray("timers");
    
    for (const auto& timer : timers) {
        if (timer.isActive) {
            timersArray.add(timer.toJson());
        }
    }
    
    doc["count"] = timersArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

DynamicJsonDocument DataManager::getAllTimersJSON() {
    DynamicJsonDocument doc(2048);
    JsonArray timersArray = doc.createNestedArray("timers");
    
    for (const auto& timer : timers) {
        timersArray.add(timer.toJson());
    }
    
    doc["count"] = timersArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

TimerData* DataManager::getTimer(const String& id) {
    int index = findTimerIndex(id);
    if (index >= 0) {
        return &timers[index];
    }
    return nullptr;
}

String DataManager::createTimer(const String& name, const String& signalId, const String& schedule) {
    DynamicJsonDocument timerData(256);
    timerData["name"] = name;
    timerData["signal_id"] = signalId;
    timerData["schedule"] = schedule;
    timerData["is_active"] = true;
    
    return createTimer(timerData);
}

String DataManager::createTimer(const DynamicJsonDocument& timerData) {
    TimerData newTimer;
    
    if (!newTimer.fromJson(timerData)) {
        Serial.println("ERROR: Invalid timer data format");
        return "";
    }
    
    // Generate unique ID if not provided
    if (newTimer.id.isEmpty()) {
        newTimer.id = generateUniqueId("timer");
    }
    
    // Check if timer already exists
    if (timerExists(newTimer.id)) {
        Serial.printf("ERROR: Timer with ID %s already exists\n", newTimer.id.c_str());
        return "";
    }
    
    // Validate timer data
    if (!validateTimerData(newTimer)) {
        Serial.println("ERROR: Timer data validation failed");
        return "";
    }
    
    // Set timestamp
    newTimer.timestamp = millis();
    
    // Add to collection
    if (timers) {
        timers->push_back(newTimer);
    }
    
    // Save to file
    saveTimersToFile();
    
    Serial.printf("Timer created: %s (%s)\n", newTimer.name.c_str(), newTimer.id.c_str());
    return newTimer.id;
}

bool DataManager::updateTimer(const String& id, const DynamicJsonDocument& timerData) {
    int index = findTimerIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Timer %s not found\n", id.c_str());
        return false;
    }
    
    TimerData updatedTimer;
    if (!updatedTimer.fromJson(timerData)) {
        Serial.println("ERROR: Invalid timer data format");
        return false;
    }
    
    // Preserve original ID and timestamp
    updatedTimer.id = id;
    updatedTimer.timestamp = timers[index].timestamp;
    
    // Validate updated data
    if (!validateTimerData(updatedTimer)) {
        Serial.println("ERROR: Timer data validation failed");
        return false;
    }
    
    // Update timer
    timers[index] = updatedTimer;
    
    // Save to file
    saveTimersToFile();
    
    Serial.printf("Timer updated: %s\n", id.c_str());
    return true;
}

bool DataManager::deleteTimer(const String& id) {
    int index = findTimerIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Timer %s not found\n", id.c_str());
        return false;
    }
    
    // Remove from collection
    if (timers && index < timers->size()) {
        timers->erase(timers->begin() + index);
    }
    
    // Save to file
    saveTimersToFile();
    
    Serial.printf("Timer deleted: %s\n", id.c_str());
    return true;
}

bool DataManager::timerExists(const String& id) {
    return findTimerIndex(id) >= 0;
}

bool DataManager::executeTimer(const String& id) {
    int index = findTimerIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Timer %s not found\n", id.c_str());
        return false;
    }
    
    TimerData& timer = timers[index];
    
    if (!timer.isActive) {
        Serial.printf("ERROR: Timer %s is not active\n", id.c_str());
        return false;
    }
    
    // Update execution info
    timer.lastExecuted = millis();
    timer.executionCount++;
    
    // Save to file
    saveTimersToFile();
    
    Serial.printf("Timer executed: %s (count: %d)\n", id.c_str(), timer.executionCount);
    return true;
}

// ==================== Task Management ====================

DynamicJsonDocument DataManager::getTasksJSON() {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    for (const auto& task : tasks) {
        tasksArray.add(task.toJson());
    }

    doc["count"] = tasksArray.size();
    doc["timestamp"] = millis();

    return doc;
}

TaskData* DataManager::getTask(const String& id) {
    int index = findTaskIndex(id);
    if (index >= 0) {
        return &tasks[index];
    }
    return nullptr;
}

String DataManager::createTask(const DynamicJsonDocument& taskData) {
    TaskData newTask;

    if (!newTask.fromJson(taskData)) {
        Serial.println("ERROR: Invalid task data format");
        return "";
    }

    // Generate unique ID if not provided
    if (newTask.id.isEmpty()) {
        newTask.id = generateUniqueId("task");
    }

    // Check if task already exists
    if (taskExists(newTask.id)) {
        Serial.printf("ERROR: Task with ID %s already exists\n", newTask.id.c_str());
        return "";
    }

    // Validate task data
    if (!validateTaskData(newTask)) {
        Serial.println("ERROR: Task data validation failed");
        return "";
    }

    // Set timestamp
    newTask.timestamp = millis();

    // Add to collection
    if (tasks) {
        tasks->push_back(newTask);
    }

    // Save to file
    saveTasksToFile();

    Serial.printf("Task created: %s (%s)\n", newTask.name.c_str(), newTask.id.c_str());
    return newTask.id;
}

bool DataManager::updateTask(const String& id, const DynamicJsonDocument& taskData) {
    int index = findTaskIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Task %s not found\n", id.c_str());
        return false;
    }

    TaskData updatedTask;
    if (!updatedTask.fromJson(taskData)) {
        Serial.println("ERROR: Invalid task data format");
        return false;
    }

    // Preserve original ID and timestamp
    updatedTask.id = id;
    updatedTask.timestamp = tasks[index].timestamp;

    // Validate updated data
    if (!validateTaskData(updatedTask)) {
        Serial.println("ERROR: Task data validation failed");
        return false;
    }

    // Update task
    tasks[index] = updatedTask;

    // Save to file
    saveTasksToFile();

    Serial.printf("Task updated: %s\n", id.c_str());
    return true;
}

bool DataManager::deleteTask(const String& id) {
    int index = findTaskIndex(id);
    if (index < 0) {
        Serial.printf("ERROR: Task %s not found\n", id.c_str());
        return false;
    }

    // Remove from collection
    if (tasks && index < tasks->size()) {
        tasks->erase(tasks->begin() + index);
    }

    // Save to file
    saveTasksToFile();

    Serial.printf("Task deleted: %s\n", id.c_str());
    return true;
}

bool DataManager::taskExists(const String& id) {
    return findTaskIndex(id) >= 0;
}

// ==================== Statistics ====================

int DataManager::getSignalCount() const {
    int count = 0;
    for (const auto& signal : signals) {
        if (signal.isActive) count++;
    }
    return count;
}

int DataManager::getTimerCount() const {
    return timers ? timers->size() : 0;
}

int DataManager::getTaskCount() const {
    return tasks ? tasks->size() : 0;
}

int DataManager::getActiveTimerCount() const {
    int count = 0;
    for (const auto& timer : timers) {
        if (timer.isActive) count++;
    }
    return count;
}

int DataManager::getPendingTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == "pending") count++;
    }
    return count;
}

int DataManager::getCompletedTaskCount() const {
    int count = 0;
    for (const auto& task : tasks) {
        if (task.status == "completed") count++;
    }
    return count;
}

// ==================== System Configuration ====================

DynamicJsonDocument DataManager::getSystemConfig() {
    // 🚀 新方案：按需创建JSON对象，避免全局存储
    size_t jsonSize = estimateConfigJsonSize();
    DynamicJsonDocument config(jsonSize);

    // 从配置缓存构建JSON对象
    for (const auto& item : configCache) {
        if (item.type == "number") {
            config[item.key] = item.value.toInt();
        } else if (item.type == "boolean") {
            config[item.key] = (item.value == "true");
        } else {
            config[item.key] = item.value;
        }
    }

    return config;
}

bool DataManager::setSystemConfig(const DynamicJsonDocument& config) {
    // 🚀 新方案：解析JSON并存储到轻量级缓存
    clearConfigCache();

    // ✅ 修复：使用迭代器方式遍历JSON对象，避免as<JsonObject>()错误
    for (JsonPairConst kv : config.as<JsonObjectConst>()) {
        String key = kv.key().c_str();
        JsonVariantConst value = kv.value();

        if (value.is<int>() || value.is<float>()) {
            addConfigItem(key, String(value.as<float>()), "number");
        } else if (value.is<bool>()) {
            addConfigItem(key, value.as<bool>() ? "true" : "false", "boolean");
        } else {
            addConfigItem(key, value.as<String>(), "string");
        }
    }

    return saveSystemConfig();
}

String DataManager::getConfigValue(const String& key, const String& defaultValue) {
    // 🚀 新方案：从轻量级缓存获取配置值
    return getConfigItem(key, defaultValue);
}

bool DataManager::setConfigValue(const String& key, const String& value) {
    // 🚀 新方案：设置到轻量级缓存并保存
    addConfigItem(key, value, "string");
    return saveSystemConfig();
}

// ==================== Data Management ====================

bool DataManager::backupData() {
    // Create backup directory
    if (!SPIFFS.exists("/backup")) {
        SPIFFS.mkdir("/backup");
    }

    // Backup all data files
    String timestamp = String(millis());

    // Copy signals
    File sourceFile = SPIFFS.open(SIGNALS_FILE, "r");
    File backupFile = SPIFFS.open("/backup/signals_" + timestamp + ".json", "w");
    if (sourceFile && backupFile) {
        backupFile.write(sourceFile.read());
        sourceFile.close();
        backupFile.close();
    }

    Serial.println("Data backup completed");
    return true;
}

bool DataManager::restoreData() {
    // Implementation for data restoration
    Serial.println("Data restore not implemented yet");
    return false;
}

DynamicJsonDocument DataManager::exportAllData() {
    DynamicJsonDocument exportDoc(8192);

    exportDoc["signals"] = getAllSignalsJSON()["signals"];
    exportDoc["timers"] = getAllTimersJSON()["timers"];
    exportDoc["tasks"] = getTasksJSON()["tasks"];
    exportDoc["config"] = getSystemConfig();  // 🚀 新方案：按需创建配置JSON
    exportDoc["export_timestamp"] = millis();

    return exportDoc;
}

bool DataManager::importAllData(const DynamicJsonDocument& data) {
    // Clear existing data
    clearAllData();

    // Import signals
    if (data.containsKey("signals")) {
        JsonArrayConst signalsArray = data["signals"];
        for (JsonVariantConst signalVariant : signalsArray) {
            // Convert JsonVariant to DynamicJsonDocument
            DynamicJsonDocument signalDoc(512);
            signalDoc.set(signalVariant);
            createSignal(signalDoc);
        }
    }

    // Import timers
    if (data.containsKey("timers")) {
        JsonArrayConst timersArray = data["timers"];
        for (JsonVariantConst timerVariant : timersArray) {
            // Convert JsonVariant to DynamicJsonDocument
            DynamicJsonDocument timerDoc(256);
            timerDoc.set(timerVariant);
            createTimer(timerDoc);
        }
    }

    // Import config
    if (data.containsKey("config")) {
        // 🚀 新方案：通过setSystemConfig方法导入配置
        DynamicJsonDocument configDoc(1024);
        configDoc.set(data["config"]);
        setSystemConfig(configDoc);
    }

    Serial.println("Data import completed");
    return true;
}

void DataManager::clearAllSignals() {
    if (signals) {
        signals->clear();
        saveSignalsToFile();
        Serial.println("All signals cleared");
    }
}

void DataManager::clearAllTimers() {
    if (timers) {
        timers->clear();
        saveTimersToFile();
        Serial.println("All timers cleared");
    }
}

void DataManager::clearAllTasks() {
    if (tasks) {
        tasks->clear();
        saveTasksToFile();
        Serial.println("All tasks cleared");
    }
}

void DataManager::clearAllData() {
    clearAllSignals();
    clearAllTimers();
    clearAllTasks();

    // 🚀 新方案：清空配置缓存
    clearConfigCache();
    saveSystemConfig();

    Serial.println("All data cleared");
}

size_t DataManager::getUsedSpace() {
    return SPIFFS.usedBytes();
}

size_t DataManager::getFreeSpace() {
    return SPIFFS.totalBytes() - SPIFFS.usedBytes();
}

// ==================== Private Helper Methods ====================

bool DataManager::checkFileSystem() {
    if (!SPIFFS.begin(true)) {
        Serial.println("ERROR: SPIFFS initialization failed");
        return false;
    }

    Serial.printf("SPIFFS initialized - Total: %d bytes, Used: %d bytes\n",
                 SPIFFS.totalBytes(), SPIFFS.usedBytes());

    return true;
}

bool DataManager::createDataDirectories() {
    if (!SPIFFS.exists("/data")) {
        if (!SPIFFS.mkdir("/data")) {
            Serial.println("ERROR: Failed to create /data directory");
            return false;
        }
    }

    return true;
}

bool DataManager::loadSignalsFromFile() {
    if (!SPIFFS.exists(SIGNALS_FILE)) {
        Serial.println("Signals file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(SIGNALS_FILE, "r");
    if (!file) {
        Serial.println("ERROR: Failed to open signals file");
        return false;
    }

    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("ERROR: Failed to parse signals file: %s\n", error.c_str());
        return false;
    }

    signals.clear();

    if (doc.containsKey("signals")) {
        JsonArrayConst signalsArray = doc["signals"];
        for (JsonVariantConst signalVariant : signalsArray) {
            SignalData signal;
            // Convert JsonVariant to DynamicJsonDocument
            DynamicJsonDocument signalDoc(512);
            signalDoc.set(signalVariant);
            if (signal.fromJson(signalDoc)) {
                signals.push_back(signal);
            }
        }
    }

    Serial.printf("Loaded %d signals from file\n", signals.size());
    return true;
}

bool DataManager::saveSignalsToFile() {
    DynamicJsonDocument doc(4096);
    JsonArray signalsArray = doc.createNestedArray("signals");

    for (const auto& signal : signals) {
        signalsArray.add(signal.toJson());
    }

    doc["count"] = signals.size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(SIGNALS_FILE, "w");
    if (!file) {
        Serial.println("ERROR: Failed to open signals file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTimersFromFile() {
    if (!SPIFFS.exists(TIMERS_FILE)) {
        Serial.println("Timers file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TIMERS_FILE, "r");
    if (!file) {
        Serial.println("ERROR: Failed to open timers file");
        return false;
    }

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("ERROR: Failed to parse timers file: %s\n", error.c_str());
        return false;
    }

    timers.clear();

    if (doc.containsKey("timers")) {
        JsonArrayConst timersArray = doc["timers"];
        for (JsonVariantConst timerVariant : timersArray) {
            TimerData timer;
            // Convert JsonVariant to DynamicJsonDocument
            DynamicJsonDocument timerDoc(256);
            timerDoc.set(timerVariant);
            if (timer.fromJson(timerDoc)) {
                timers.push_back(timer);
            }
        }
    }

    Serial.printf("Loaded %d timers from file\n", timers.size());
    return true;
}

bool DataManager::saveTimersToFile() {
    DynamicJsonDocument doc(2048);
    JsonArray timersArray = doc.createNestedArray("timers");

    for (const auto& timer : timers) {
        timersArray.add(timer.toJson());
    }

    doc["count"] = timers.size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TIMERS_FILE, "w");
    if (!file) {
        Serial.println("ERROR: Failed to open timers file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTasksFromFile() {
    if (!SPIFFS.exists(TASKS_FILE)) {
        Serial.println("Tasks file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TASKS_FILE, "r");
    if (!file) {
        Serial.println("ERROR: Failed to open tasks file");
        return false;
    }

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("ERROR: Failed to parse tasks file: %s\n", error.c_str());
        return false;
    }

    tasks.clear();

    if (doc.containsKey("tasks")) {
        JsonArrayConst tasksArray = doc["tasks"];
        for (JsonVariantConst taskVariant : tasksArray) {
            TaskData task;
            // Convert JsonVariant to DynamicJsonDocument
            DynamicJsonDocument taskDoc(512);
            taskDoc.set(taskVariant);
            if (task.fromJson(taskDoc)) {
                tasks.push_back(task);
            }
        }
    }

    Serial.printf("Loaded %d tasks from file\n", tasks.size());
    return true;
}

bool DataManager::saveTasksToFile() {
    DynamicJsonDocument doc(2048);
    JsonArray tasksArray = doc.createNestedArray("tasks");

    for (const auto& task : tasks) {
        tasksArray.add(task.toJson());
    }

    doc["count"] = tasks.size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TASKS_FILE, "w");
    if (!file) {
        Serial.println("ERROR: Failed to open tasks file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadSystemConfig() {
    if (!SPIFFS.exists(CONFIG_FILE)) {
        Serial.println("Config file not found, using default configuration");

        // 🚀 新方案：设置默认配置到缓存
        addConfigItem("system_name", "ESP32-S3 IR Control System", "string");
        addConfigItem("version", "1.0.0", "string");
        addConfigItem("ir_frequency", "38000", "number");
        addConfigItem("learning_timeout", "30000", "number");

        saveSystemConfig();
        return true;
    }

    File file = SPIFFS.open(CONFIG_FILE, "r");
    if (!file) {
        Serial.println("ERROR: Failed to open config file");
        return false;
    }

    // 🚀 新方案：临时创建JSON对象加载配置
    DynamicJsonDocument tempConfig(1024);
    DeserializationError error = deserializeJson(tempConfig, file);
    file.close();

    if (error) {
        Serial.printf("ERROR: Failed to parse config file: %s\n", error.c_str());
        return false;
    }

    // 🚀 新方案：将JSON数据转换到配置缓存
    setSystemConfig(tempConfig);

    Serial.println("System configuration loaded");
    return true;
}

bool DataManager::saveSystemConfig() {
    File file = SPIFFS.open(CONFIG_FILE, "w");
    if (!file) {
        Serial.println("ERROR: Failed to open config file for writing");
        return false;
    }

    // 🚀 新方案：按需创建JSON对象并保存
    DynamicJsonDocument configToSave = getSystemConfig();
    serializeJson(configToSave, file);
    file.close();

    return true;
}

// ==================== Helper Methods ====================

int DataManager::findSignalIndex(const String& id) {
    for (size_t i = 0; i < signals.size(); i++) {
        if (signals[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTimerIndex(const String& id) {
    for (size_t i = 0; i < timers.size(); i++) {
        if (timers[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTaskIndex(const String& id) {
    for (size_t i = 0; i < tasks.size(); i++) {
        if (tasks[i].id == id) {
            return i;
        }
    }
    return -1;
}

bool DataManager::validateSignalData(const SignalData& signal) {
    if (signal.name.isEmpty()) {
        Serial.println("ERROR: Signal name cannot be empty");
        return false;
    }

    if (signal.type.isEmpty()) {
        Serial.println("ERROR: Signal type cannot be empty");
        return false;
    }

    if (signal.data.isEmpty() && signal.rawData.isEmpty()) {
        Serial.println("ERROR: Signal must have either data or raw data");
        return false;
    }

    return true;
}

bool DataManager::validateTimerData(const TimerData& timer) {
    if (timer.name.isEmpty()) {
        Serial.println("ERROR: Timer name cannot be empty");
        return false;
    }

    if (timer.signalId.isEmpty()) {
        Serial.println("ERROR: Timer signal ID cannot be empty");
        return false;
    }

    if (timer.schedule.isEmpty()) {
        Serial.println("ERROR: Timer schedule cannot be empty");
        return false;
    }

    return true;
}

bool DataManager::validateTaskData(const TaskData& task) {
    if (task.name.isEmpty()) {
        Serial.println("ERROR: Task name cannot be empty");
        return false;
    }

    if (task.type.isEmpty()) {
        Serial.println("ERROR: Task type cannot be empty");
        return false;
    }

    return true;
}

String DataManager::generateUniqueId(const String& prefix) {
    return prefix + "_" + String(millis()) + "_" + String(random(1000, 9999));
}

// 🚀 新方案：配置缓存管理方法实现
void DataManager::addConfigItem(const String& key, const String& value, const String& type) {
    // 查找是否已存在
    for (auto& item : configCache) {
        if (item.key == key) {
            item.value = value;
            item.type = type;
            return;
        }
    }

    // 添加新项
    ConfigItem newItem;
    newItem.key = key;
    newItem.value = value;
    newItem.type = type;
    configCache.push_back(newItem);
}

String DataManager::getConfigItem(const String& key, const String& defaultValue) const {
    for (const auto& item : configCache) {
        if (item.key == key) {
            return item.value;
        }
    }
    return defaultValue;
}

bool DataManager::hasConfigItem(const String& key) const {
    for (const auto& item : configCache) {
        if (item.key == key) {
            return true;
        }
    }
    return false;
}

void DataManager::removeConfigItem(const String& key) {
    configCache.erase(
        std::remove_if(configCache.begin(), configCache.end(),
            [&key](const ConfigItem& item) { return item.key == key; }),
        configCache.end()
    );
}

void DataManager::clearConfigCache() {
    configCache.clear();
}

size_t DataManager::estimateConfigJsonSize() const {
    size_t baseSize = 64;  // 基础JSON结构
    for (const auto& item : configCache) {
        baseSize += item.key.length() + item.value.length() + 32;  // 键值对 + JSON开销
    }
    return std::max(baseSize, (size_t)256);  // 最小256字节
}

// ==================== Data Structure Implementations ====================

DynamicJsonDocument SignalData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["protocol"] = protocol;
    doc["data"] = data;
    doc["raw_data"] = rawData;
    doc["frequency"] = frequency;
    doc["timestamp"] = timestamp;
    doc["is_active"] = isActive;
    doc["use_count"] = useCount;
    doc["description"] = description;

    return doc;
}

bool SignalData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "other";
    protocol = doc["protocol"] | "";
    data = doc["data"] | "";
    rawData = doc["raw_data"] | "";
    frequency = doc["frequency"] | 38000;
    timestamp = doc["timestamp"] | 0;
    isActive = doc["is_active"] | true;
    useCount = doc["use_count"] | 0;
    description = doc["description"] | "";

    return true;
}

DynamicJsonDocument TimerData::toJson() const {
    DynamicJsonDocument doc(256);

    doc["id"] = id;
    doc["name"] = name;
    doc["signal_id"] = signalId;
    doc["schedule"] = schedule;
    doc["is_active"] = isActive;
    doc["timestamp"] = timestamp;
    doc["last_executed"] = lastExecuted;
    doc["execution_count"] = executionCount;

    return doc;
}

bool TimerData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    signalId = doc["signal_id"] | "";
    schedule = doc["schedule"] | "";
    isActive = doc["is_active"] | false;
    timestamp = doc["timestamp"] | 0;
    lastExecuted = doc["last_executed"] | 0;
    executionCount = doc["execution_count"] | 0;

    return true;
}

DynamicJsonDocument TaskData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["status"] = status;

    // 🚀 修复：将String参数解析为JSON对象
    if (!parameters.isEmpty()) {
        DynamicJsonDocument paramDoc(256);
        DeserializationError parseError = deserializeJson(paramDoc, parameters);
        if (!parseError) {
            doc["parameters"] = paramDoc;
        } else {
            doc["parameters"] = parameters;  // 如果解析失败，保存原始字符串
        }
    }

    doc["timestamp"] = timestamp;
    doc["start_time"] = startTime;
    doc["end_time"] = endTime;
    doc["result"] = result;
    doc["error"] = error;

    return doc;
}

bool TaskData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "";
    status = doc["status"] | "pending";

    // 🚀 修复：将JSON参数序列化为String存储
    if (doc.containsKey("parameters")) {
        String paramStr;
        serializeJson(doc["parameters"], paramStr);
        parameters = paramStr;
    }

    timestamp = doc["timestamp"] | 0;
    startTime = doc["start_time"] | 0;
    endTime = doc["end_time"] | 0;
    result = doc["result"] | "";
    error = doc["error"] | "";

    return true;
}

// 🚀 新增：TaskData参数管理方法实现
DynamicJsonDocument TaskData::getParameters() const {
    DynamicJsonDocument paramDoc(256);
    if (!parameters.isEmpty()) {
        DeserializationError parseError = deserializeJson(paramDoc, parameters);
        if (parseError) {
            // 如果解析失败，返回空对象
            paramDoc.clear();
        }
    }
    return paramDoc;
}

void TaskData::setParameters(const DynamicJsonDocument& params) {
    String paramStr;
    serializeJson(params, paramStr);
    parameters = paramStr;
}

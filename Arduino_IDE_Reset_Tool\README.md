# Arduino IDE 完全重置工具

## 🎯 工具目的
一键清理Arduino IDE的所有缓存、配置和临时文件，将软件恢复到刚安装后的干净状态。

## 📁 文件说明
- `Arduino_IDE_Complete_Reset.bat` - 一键重置脚本
- `ESP32_Fresh_Install_Guide.md` - 重置后的安装指南
- `README.md` - 使用说明

## 🚀 使用方法

### 第一步：运行重置工具
1. **右键点击** `Arduino_IDE_Complete_Reset.bat`
2. **选择** "以管理员身份运行"
3. **输入** `Y` 确认执行
4. **等待** 重置完成

### 第二步：重新安装ESP32板包
1. **启动Arduino IDE**
2. **按照** `ESP32_Fresh_Install_Guide.md` 的步骤操作
3. **配置ESP32-S3设置**
4. **测试PSRAM功能**

## ⚠️ 重要提醒

### 此工具将删除：
- ✅ 所有Arduino IDE缓存文件
- ✅ 所有编译临时文件
- ✅ ESP32工具链缓存
- ✅ Arduino IDE配置文件
- ✅ 注册表项
- ⚠️ **保留用户库文件夹**（需要手动删除）

### 此工具不会删除：
- ❌ Arduino IDE程序本身
- ❌ 您的项目文件
- ❌ Documents/Arduino/libraries 用户库

## 🔧 手动清理（如果脚本失败）

如果自动脚本失败，可以手动删除以下文件夹：

```
C:\Users\<USER>\AppData\Local\arduino\
C:\Users\<USER>\AppData\Local\Arduino15\
C:\Users\<USER>\AppData\Roaming\Arduino\
C:\Users\<USER>\AppData\Roaming\arduino-ide\
C:\Users\<USER>\AppData\Local\Temp\arduino*
C:\Users\<USER>\.espressif\
```

## 📋 重置后检查清单

- [ ] Arduino IDE启动正常
- [ ] ESP32板包重新安装
- [ ] ESP32S3 Dev Module可选择
- [ ] Flash Mode可设置为QIO 80MHz
- [ ] PSRAM可设置为OPI PSRAM
- [ ] 编译输出显示 --flash_mode qio
- [ ] PSRAM测试代码检测成功

## 🎯 预期结果

重置后，Arduino IDE应该：
1. **启动速度更快**
2. **配置选项正常**
3. **编译参数正确**
4. **PSRAM检测成功**

## 📞 如果仍有问题

如果重置后仍然有问题，可能需要：
1. **完全卸载并重新安装Arduino IDE**
2. **检查Windows系统权限**
3. **使用不同版本的ESP32板包**
4. **检查硬件连接**

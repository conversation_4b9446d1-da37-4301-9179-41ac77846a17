/*
 * ESP32-S3 独立PSRAM测试程序
 * 完全独立，不依赖任何其他文件
 * 专门用于验证PSRAM是否正常工作
 * 使用Arduino框架的PSRAM函数
 */

void setup() {
    // 初始化串口
    Serial.begin(115200);
    delay(5000);  // 更长延迟确保串口稳定
    
    Serial.println();
    Serial.println("========================================");
    Serial.println("🚀 ESP32-S3 独立PSRAM测试程序");
    Serial.println("🔧 Arduino IDE Version with OPI PSRAM");
    Serial.println("⚡ Hardware: ESP32-S3-WROOM-1-N16R8");
    Serial.println("========================================");
    
    // 立即检测PSRAM
    performPSRAMTest();
    
    Serial.println("========================================");
    Serial.println("📋 基本系统信息");
    Serial.println("========================================");
    
    // 系统信息
    Serial.printf("Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);
    Serial.printf("Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    Serial.printf("SDK Version: %s\n", ESP.getSdkVersion());
    
    Serial.println();
    Serial.println("🎉 测试程序启动完成!");
    Serial.println("========================================");
}

void loop() {
    // 每10秒显示一次状态
    static unsigned long lastCheck = 0;
    if (millis() - lastCheck > 10000) {
        lastCheck = millis();
        
        Serial.println("💓 系统运行中...");
        if (psramFound()) {
            Serial.printf("📊 Free PSRAM: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
        }
        Serial.printf("⏰ 运行时间: %lu 秒\n", millis() / 1000);
        Serial.println("---");
    }
    
    delay(1000);
}

// PSRAM测试函数
void performPSRAMTest() {
    Serial.println();
    Serial.println("🔬 PSRAM DETECTION AND TESTING");
    Serial.println("=============================================");

    // 检查PSRAM状态（使用Arduino框架函数）
    Serial.println("🔄 Checking PSRAM status...");

    Serial.println("🔍 Checking PSRAM availability...");
    
    if (!psramFound()) {
        Serial.println();
        Serial.println("❌ PSRAM NOT FOUND!");
        Serial.println("⚠️  Check Arduino IDE configuration:");
        Serial.println("   - Board: ESP32S3 Dev Module");
        Serial.println("   - PSRAM: OPI PSRAM");
        Serial.println("   - Flash Mode: QIO");
        Serial.println("   - Flash Size: 16MB");
        Serial.println("System will continue but with limited memory");
        Serial.println("=============================================");
        return;
    }
    
    Serial.println();
    Serial.println("🎉 PSRAM DETECTION SUCCESSFUL!");
    Serial.println("✅ PSRAM is available!");
    Serial.printf("📊 Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("🧠 Free: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    Serial.println("🚀 PSRAM ready for use!");
    
    // Test 1: Basic allocation test
    Serial.println("\n🧪 Test 1: Basic Allocation Test");
    void* ptr1 = ps_malloc(1024);
    if (ptr1) {
        Serial.printf("✅ 1KB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr1);
        free(ptr1);
    } else {
        Serial.println("❌ 1KB allocation: FAILED");
    }
    
    // Test 2: Large allocation test
    Serial.println("\n🧪 Test 2: Large Allocation Test");
    size_t largeSize = 1024 * 1024; // 1MB
    void* ptr2 = ps_malloc(largeSize);
    if (ptr2) {
        Serial.printf("✅ 1MB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr2);
        free(ptr2);
    } else {
        Serial.println("❌ 1MB allocation: FAILED");
    }
    
    // Test 3: Read/Write integrity test
    Serial.println("\n🧪 Test 3: Read/Write Integrity Test");
    size_t testSize = 256 * 1024; // 256KB
    uint8_t* testBuffer = (uint8_t*)ps_malloc(testSize);
    if (testBuffer) {
        // Write test pattern
        for (size_t i = 0; i < testSize; i++) {
            testBuffer[i] = (uint8_t)(i % 256);
        }
        
        // Verify test pattern
        bool integrity = true;
        for (size_t i = 0; i < testSize; i++) {
            if (testBuffer[i] != (uint8_t)(i % 256)) {
                integrity = false;
                break;
            }
        }
        
        if (integrity) {
            Serial.println("✅ Read/Write integrity: SUCCESS");
        } else {
            Serial.println("❌ Read/Write integrity: FAILED");
        }
        
        free(testBuffer);
    } else {
        Serial.println("❌ Read/Write test: Allocation failed");
    }
    
    Serial.println();
    Serial.println("🏁 PSRAM TESTING COMPLETED SUCCESSFULLY!");
    Serial.println("✅ All PSRAM tests passed - Memory ready for use");
    Serial.println("=============================================");
}

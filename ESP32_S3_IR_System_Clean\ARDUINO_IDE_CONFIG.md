# ESP32-S3 IR Control System - Arduino IDE Configuration

## 🎯 Overview

This document provides the exact Arduino IDE configuration required for the ESP32-S3 IR Control System with OPI PSRAM support.

## 📋 Required Arduino IDE Configuration

### Board Selection
```
Tools > Board > ESP32 Arduino > ESP32S3 Dev Module
```

### Critical Settings
```
Tools > Flash Mode > QIO                    ⚠️  CRITICAL for PSRAM
Tools > Flash Size > 16MB (128Mb)
Tools > PSRAM > OPI PSRAM                   ⚠️  CRITICAL for 8MB PSRAM
Tools > Partition Scheme > 16M Flash (3MB APP/9.9MB FATFS)
Tools > Upload Speed > 460800
Tools > CPU Frequency > 240MHz (WiFi)
Tools > Core Debug Level > None
Tools > Arduino Runs On > Core 1
Tools > Events Run On > Core 1
```

## 🚨 Critical Requirements

### 1. Flash Mode MUST be QIO
- **QIO Mode**: Required for OPI PSRAM to function
- **DIO/DOUT/QOUT**: Will cause PSRAM initialization failure
- **Verification**: System will check and report flash mode on startup

### 2. PSRAM MUST be OPI PSRAM
- **OPI PSRAM**: Enables 8MB external RAM
- **Disabled**: System will fail to initialize
- **Verification**: System performs comprehensive PSRAM tests

### 3. Partition Scheme
- **16M Flash (3MB APP/9.9MB FATFS)**: Recommended for web interface
- **Custom**: Not available in standard Arduino IDE
- **Alternative**: Any 16MB partition scheme will work

## 🔧 Hardware Requirements

### ESP32-S3 Board
- **Model**: ESP32-S3-WROOM-1-N16R8
- **Flash**: 16MB
- **PSRAM**: 8MB OPI PSRAM
- **Pins**: GPIO4 (IR Send), GPIO5 (IR Receive)

### IR Components
- **IR LED**: Connected to GPIO4
- **IR Receiver**: Connected to GPIO5 (e.g., TSOP38238)
- **Power**: 3.3V supply capable of handling PSRAM load

## 📊 Expected Boot Output

### Successful Configuration
```
Flash Mode: QIO ✅
✅ PSRAM DETECTION SUCCESSFUL!
📊 PSRAM Size: 8388608 bytes (8.00 MB)
📊 PSRAM Free: 8388608 bytes (8.00 MB)
✅ PSRAM allocation test passed
✅ PSRAM large allocation test passed
🎉 PSRAM is fully functional and ready!
✅ System initialization completed successfully!
```

### Failed Configuration
```
Flash Mode: DIO ❌
❌ CRITICAL: Flash Mode is not QIO!
This WILL prevent OPI PSRAM from working!
```

## 🛠️ Troubleshooting

### PSRAM Not Detected
1. **Check Flash Mode**: Must be QIO
2. **Check PSRAM Setting**: Must be "OPI PSRAM"
3. **Check Hardware**: Verify ESP32-S3 has OPI PSRAM
4. **Power Supply**: Ensure adequate 3.3V supply

### System Restart Loop
1. **Check build_opt.h**: Ensure no Chinese characters
2. **Check Libraries**: Verify all libraries are installed
3. **Check Memory**: Global objects may be allocating before PSRAM

### Web Interface Not Loading
1. **Upload SPIFFS**: Use "ESP32 Sketch Data Upload" tool
2. **Check WiFi**: System starts AP mode if no saved credentials
3. **Check IP**: Connect to AP and visit http://***********

## 📚 Required Libraries

Install these libraries via Arduino IDE Library Manager:

```
- ESP32 Arduino Core (latest stable)
- ESPAsyncWebServer by lacamera
- AsyncTCP by dvarrel
- ArduinoJson by Benoit Blanchon (v6.21.3+)
- IRremoteESP8266 by David Conran
```

## 🔍 Verification Steps

1. **Compile**: Should compile without errors
2. **Upload**: Should upload successfully
3. **Serial Monitor**: Check for successful PSRAM detection
4. **Web Interface**: Connect to AP and test web interface
5. **IR Functions**: Test IR learning and transmission

## 📱 Access Points

### Default AP Mode
- **SSID**: ESP32-S3-IR-[ChipID]
- **Password**: 12345678
- **IP**: ***********
- **Web Interface**: http://***********

### Station Mode
- **Auto-connect**: Uses saved WiFi credentials
- **Fallback**: Switches to AP mode if connection fails
- **Web Interface**: http://[assigned-ip]

## 🎉 Success Indicators

- ✅ Flash Mode: QIO
- ✅ PSRAM: 8MB detected and functional
- ✅ Web Server: Running on port 80
- ✅ WebSocket: Real-time communication active
- ✅ IR Controller: Ready for learning and transmission

## 📞 Support

If you encounter issues:

1. **Check Serial Output**: Look for error messages
2. **Verify Configuration**: Double-check all Arduino IDE settings
3. **Test Hardware**: Ensure ESP32-S3 board is genuine
4. **Check Power**: Verify stable 3.3V supply
5. **Reset Settings**: Try factory reset if needed

---

**Note**: This system uses a clean architecture with zero global memory allocation to completely avoid PSRAM initialization conflicts. All objects are created after PSRAM is confirmed available.

#ifndef HARDWARE_CONFIG_H
#define HARDWARE_CONFIG_H

// ==================== ESP32-S3 硬件配置 (无PSRAM版本) ====================

// ==================== 红外硬件配置 ====================
// 红外发射器配置
#define IR_SEND_PIN 4                   // 红外发射引脚
#define IR_SEND_CHANNEL 0               // PWM通道
#define IR_SEND_FREQUENCY 38000         // 载波频率 (Hz)
#define IR_SEND_DUTY_CYCLE 50           // 占空比 (%)
#define IR_SEND_RESOLUTION 8            // PWM分辨率 (位)

// 红外接收器配置
#define IR_RECV_PIN 5                   // 红外接收引脚
#define IR_RECV_BUFFER_SIZE 512         // 接收缓冲区大小 (减少)
#define IR_RECV_TIMEOUT 50              // 接收超时 (毫秒)
#define IR_RECV_TOLERANCE 25            // 接收容差 (%)

// 红外协议支持
#define SUPPORT_NEC_PROTOCOL true
#define SUPPORT_SONY_PROTOCOL true
#define SUPPORT_RC5_PROTOCOL true
#define SUPPORT_RC6_PROTOCOL true
#define SUPPORT_SAMSUNG_PROTOCOL true
#define SUPPORT_LG_PROTOCOL true
#define SUPPORT_RAW_PROTOCOL true

// ==================== LED指示灯配置 ====================
#define STATUS_LED_PIN 2                // 状态LED引脚
#define WIFI_LED_PIN 15                 // WiFi状态LED引脚
#define IR_LED_PIN 16                   // 红外状态LED引脚

// LED状态定义
#define LED_OFF 0
#define LED_ON 1
#define LED_BLINK_SLOW 2               // 慢闪 (1Hz)
#define LED_BLINK_FAST 3               // 快闪 (5Hz)

// ==================== 按键配置 ====================
#define RESET_BUTTON_PIN 0              // 复位按键引脚
#define LEARN_BUTTON_PIN 1              // 学习按键引脚
#define MODE_BUTTON_PIN 3               // 模式切换按键引脚

// 按键配置
#define BUTTON_DEBOUNCE_TIME 50         // 按键防抖时间 (毫秒)
#define BUTTON_LONG_PRESS_TIME 3000     // 长按时间 (毫秒)
#define BUTTON_DOUBLE_CLICK_TIME 500    // 双击时间 (毫秒)

// ==================== 传感器配置 ====================
#define TEMP_SENSOR_PIN 6               // 温度传感器引脚 (可选)
#define HUMIDITY_SENSOR_PIN 7           // 湿度传感器引脚 (可选)
#define LIGHT_SENSOR_PIN 8              // 光线传感器引脚 (可选)

// 传感器使能开关
#define ENABLE_TEMPERATURE_SENSOR false
#define ENABLE_HUMIDITY_SENSOR false
#define ENABLE_LIGHT_SENSOR false

// ==================== 扩展接口配置 ====================
#define I2C_SDA_PIN 21                  // I2C数据引脚
#define I2C_SCL_PIN 22                  // I2C时钟引脚
#define I2C_FREQUENCY 100000            // I2C频率 (Hz)

#define SPI_MOSI_PIN 23                 // SPI MOSI引脚
#define SPI_MISO_PIN 19                 // SPI MISO引脚
#define SPI_SCK_PIN 18                  // SPI SCK引脚
#define SPI_CS_PIN 17                   // SPI CS引脚

// ==================== 串口配置 ====================
#define SERIAL_BAUD_RATE 115200         // 主串口波特率
#define SERIAL1_BAUD_RATE 9600          // 串口1波特率 (可选)
#define SERIAL2_BAUD_RATE 9600          // 串口2波特率 (可选)

// 串口引脚配置
#define SERIAL1_TX_PIN 43               // 串口1发送引脚
#define SERIAL1_RX_PIN 44               // 串口1接收引脚
#define SERIAL2_TX_PIN 45               // 串口2发送引脚
#define SERIAL2_RX_PIN 46               // 串口2接收引脚

// ==================== 电源管理配置 ====================
#define ENABLE_POWER_MANAGEMENT true    // 启用电源管理
#define SLEEP_MODE_TIMEOUT 300000       // 睡眠模式超时 (5分钟)
#define DEEP_SLEEP_TIMEOUT 3600000      // 深度睡眠超时 (1小时)

// 电源引脚配置
#define POWER_ENABLE_PIN 9              // 电源使能引脚
#define BATTERY_MONITOR_PIN 10          // 电池监控引脚
#define CHARGING_STATUS_PIN 11          // 充电状态引脚

// ==================== 看门狗配置 ====================
#define ENABLE_WATCHDOG true            // 启用看门狗
#define WATCHDOG_TIMEOUT 30000          // 看门狗超时 (30秒)

// ==================== 时钟配置 ====================
#define CPU_FREQUENCY_MHZ 240           // CPU频率 (MHz)
#define XTAL_FREQUENCY_MHZ 40           // 晶振频率 (MHz)

// ==================== Flash配置 ====================
#define FLASH_MODE_QIO true             // QIO模式
#define FLASH_FREQUENCY_MHZ 80          // Flash频率 (MHz)
#define FLASH_SIZE_MB 16                // Flash大小 (MB)

// ==================== PSRAM配置 (已禁用) ====================
// PSRAM相关配置已完全禁用
#define PSRAM_MODE_DISABLED true        // PSRAM已禁用
#define PSRAM_SIZE_MB 0                 // PSRAM大小设为0

// ==================== 引脚功能映射 ====================
struct PinConfig {
    // 红外相关
    static const int IR_SEND = IR_SEND_PIN;
    static const int IR_RECV = IR_RECV_PIN;
    
    // LED指示灯
    static const int STATUS_LED = STATUS_LED_PIN;
    static const int WIFI_LED = WIFI_LED_PIN;
    static const int IR_LED = IR_LED_PIN;
    
    // 按键
    static const int RESET_BUTTON = RESET_BUTTON_PIN;
    static const int LEARN_BUTTON = LEARN_BUTTON_PIN;
    static const int MODE_BUTTON = MODE_BUTTON_PIN;
    
    // 通信接口
    static const int I2C_SDA = I2C_SDA_PIN;
    static const int I2C_SCL = I2C_SCL_PIN;
    static const int SPI_MOSI = SPI_MOSI_PIN;
    static const int SPI_MISO = SPI_MISO_PIN;
    static const int SPI_SCK = SPI_SCK_PIN;
    static const int SPI_CS = SPI_CS_PIN;
    
    // 电源管理
    static const int POWER_ENABLE = POWER_ENABLE_PIN;
    static const int BATTERY_MONITOR = BATTERY_MONITOR_PIN;
    static const int CHARGING_STATUS = CHARGING_STATUS_PIN;
};

// ==================== 硬件能力定义 ====================
struct HardwareCapabilities {
    // 红外能力
    static const bool HAS_IR_TRANSMITTER = true;
    static const bool HAS_IR_RECEIVER = true;
    static const int MAX_IR_FREQUENCY = 56000;
    static const int MIN_IR_FREQUENCY = 30000;
    
    // 存储能力
    static const bool HAS_PSRAM = false;        // PSRAM已禁用
    static const bool HAS_FLASH = true;
    static const bool HAS_SPIFFS = true;
    
    // 网络能力
    static const bool HAS_WIFI = true;
    static const bool HAS_BLUETOOTH = true;
    static const bool HAS_ETHERNET = false;
    
    // 接口能力
    static const bool HAS_I2C = true;
    static const bool HAS_SPI = true;
    static const bool HAS_UART = true;
    static const bool HAS_ADC = true;
    static const bool HAS_DAC = true;
    static const bool HAS_PWM = true;
    
    // 传感器能力
    static const bool HAS_TEMPERATURE_SENSOR = ENABLE_TEMPERATURE_SENSOR;
    static const bool HAS_HUMIDITY_SENSOR = ENABLE_HUMIDITY_SENSOR;
    static const bool HAS_LIGHT_SENSOR = ENABLE_LIGHT_SENSOR;
};

// ==================== 硬件初始化宏 ====================
#define INIT_IR_PINS() do { \
    pinMode(IR_SEND_PIN, OUTPUT); \
    pinMode(IR_RECV_PIN, INPUT); \
    digitalWrite(IR_SEND_PIN, LOW); \
} while(0)

#define INIT_LED_PINS() do { \
    pinMode(STATUS_LED_PIN, OUTPUT); \
    pinMode(WIFI_LED_PIN, OUTPUT); \
    pinMode(IR_LED_PIN, OUTPUT); \
    digitalWrite(STATUS_LED_PIN, LED_OFF); \
    digitalWrite(WIFI_LED_PIN, LED_OFF); \
    digitalWrite(IR_LED_PIN, LED_OFF); \
} while(0)

#define INIT_BUTTON_PINS() do { \
    pinMode(RESET_BUTTON_PIN, INPUT_PULLUP); \
    pinMode(LEARN_BUTTON_PIN, INPUT_PULLUP); \
    pinMode(MODE_BUTTON_PIN, INPUT_PULLUP); \
} while(0)

// ==================== 硬件状态检查宏 ====================
#define IS_BUTTON_PRESSED(pin) (digitalRead(pin) == LOW)
#define SET_LED_STATE(pin, state) digitalWrite(pin, state)
#define GET_BATTERY_LEVEL() analogRead(BATTERY_MONITOR_PIN)

// ==================== 功率转换宏 ====================
#define POWER_TO_PWM_DUTY(power) ((power) * 255 / 100)
#define PWM_DUTY_TO_POWER(duty) ((duty) * 100 / 255)

// ==================== 调试配置 ====================
#ifdef DEBUG_MODE
    #define DEBUG_IR_SIGNALS true
    #define DEBUG_HARDWARE_STATUS true
    #define DEBUG_PIN_STATES false
#else
    #define DEBUG_IR_SIGNALS false
    #define DEBUG_HARDWARE_STATUS false
    #define DEBUG_PIN_STATES false
#endif

// ==================== 兼容性检查 ====================
#if !defined(ESP32S3)
    #warning "This configuration is optimized for ESP32-S3. Some features may not work on other ESP32 variants."
#endif

// 确认PSRAM已禁用
#ifdef BOARD_HAS_PSRAM
    #undef BOARD_HAS_PSRAM
#endif
#define BOARD_HAS_NO_PSRAM 1

// ==================== 硬件版本信息 ====================
#define HARDWARE_VERSION "1.1"
#define PCB_REVISION "Rev A (No PSRAM)"
#define HARDWARE_DESCRIPTION "ESP32-S3 IR Control Board (RAM Only Mode)"

#endif // HARDWARE_CONFIG_H

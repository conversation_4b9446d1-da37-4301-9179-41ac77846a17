/*
 * ESP32-S3 IR Control System - Clean Architecture
 * 
 * Designed to completely avoid PSRAM initialization conflicts:
 * - Zero global memory allocation
 * - Delayed initialization pattern
 * - Singleton managers with lazy loading
 * - Explicit initialization order control
 * 
 * Hardware: ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
 * 
 * Arduino IDE Configuration:
 * - Board: ESP32S3 Dev Module
 * - Flash Mode: QIO
 * - Flash Size: 16MB (128Mb)
 * - PSRAM: OPI PSRAM
 * - Partition Scheme: 16M Flash (3MB APP/9.9MB FATFS)
 */

#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>

// ==================== Forward Declarations ====================
class SystemManager;
class DataManager;
class IRController;
class WebServerManager;

// ==================== Global State - NO MEMORY ALLOCATION ====================
bool g_systemInitialized = false;
bool g_psramAvailable = false;
unsigned long g_systemStartTime = 0;

// ==================== Function Declarations ====================
bool initializePSRAM();
bool initializeSystem();
void performSystemDiagnostics();
SystemManager* getSystemManager();

// ==================== Setup Function ====================
void setup() {
    Serial.begin(115200);
    delay(3000);
    
    Serial.println();
    Serial.println("========================================");
    Serial.println("ESP32-S3 IR Control System - Clean Architecture");
    Serial.println("Zero Global Memory Allocation Design");
    Serial.println("========================================");
    
    g_systemStartTime = millis();
    
    // Step 1: System Diagnostics
    performSystemDiagnostics();
    
    // Step 2: PSRAM Initialization - CRITICAL FIRST STEP
    if (!initializePSRAM()) {
        Serial.println("CRITICAL ERROR: PSRAM initialization failed!");
        Serial.println("System cannot continue - entering safe mode");
        while (true) {
            delay(1000);
            Serial.println("Safe mode - PSRAM required for operation");
        }
    }
    
    // Step 3: System Initialization
    if (!initializeSystem()) {
        Serial.println("ERROR: System initialization failed!");
        Serial.println("Restarting in 5 seconds...");
        delay(5000);
        ESP.restart();
    }
    
    g_systemInitialized = true;
    
    Serial.println("========================================");
    Serial.println("✅ System initialization completed successfully!");
    Serial.printf("⏱️  Total initialization time: %lu ms\n", millis() - g_systemStartTime);
    Serial.println("🚀 System ready for operation");
    Serial.println("========================================");
}

// ==================== Loop Function ====================
void loop() {
    if (!g_systemInitialized) {
        delay(100);
        return;
    }
    
    // Get system manager and handle main loop
    SystemManager* manager = getSystemManager();
    if (manager) {
        manager->handleLoop();
    }
    
    delay(1);
}

// ==================== PSRAM Initialization ====================
bool initializePSRAM() {
    Serial.println("🔬 PSRAM Detection and Initialization");
    Serial.println("----------------------------------------");
    
    // Check chip information
    Serial.printf("Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);
    
    // Check Flash Mode - Critical for PSRAM
    uint32_t flash_mode = ESP.getFlashChipMode();
    Serial.printf("Flash Mode: %s\n", 
        flash_mode == 0 ? "QIO ✅" :
        flash_mode == 1 ? "QOUT ❌" :
        flash_mode == 2 ? "DIO ❌" :
        flash_mode == 3 ? "DOUT ❌" : "UNKNOWN ❌");
    
    if (flash_mode != 0) {
        Serial.println();
        Serial.println("❌ CRITICAL: Flash Mode is not QIO!");
        Serial.println("This WILL prevent OPI PSRAM from working!");
        Serial.println("Please check Arduino IDE configuration:");
        Serial.println("- Board: ESP32S3 Dev Module");
        Serial.println("- Flash Mode: QIO");
        Serial.println("- PSRAM: OPI PSRAM");
        return false;
    }
    
    // Test PSRAM availability
    if (!psramFound()) {
        Serial.println();
        Serial.println("❌ PSRAM NOT DETECTED!");
        Serial.println("Hardware or configuration issue detected.");
        return false;
    }
    
    // PSRAM detected successfully
    g_psramAvailable = true;
    Serial.println();
    Serial.println("✅ PSRAM DETECTION SUCCESSFUL!");
    Serial.printf("📊 PSRAM Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("📊 PSRAM Free: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    
    // Test PSRAM functionality
    Serial.println("🧪 Testing PSRAM functionality...");
    
    // Test 1: Basic allocation
    void* test_ptr = ps_malloc(1024);
    if (!test_ptr) {
        Serial.println("❌ PSRAM allocation test failed!");
        return false;
    }
    free(test_ptr);
    Serial.println("✅ PSRAM allocation test passed");
    
    // Test 2: Large allocation
    size_t large_size = 1024 * 1024; // 1MB
    void* large_ptr = ps_malloc(large_size);
    if (!large_ptr) {
        Serial.println("❌ PSRAM large allocation test failed!");
        return false;
    }
    free(large_ptr);
    Serial.println("✅ PSRAM large allocation test passed");
    
    Serial.println("🎉 PSRAM is fully functional and ready!");
    return true;
}

// ==================== System Diagnostics ====================
void performSystemDiagnostics() {
    Serial.println("🔍 System Diagnostics");
    Serial.println("----------------------");
    
    Serial.printf("Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    Serial.printf("Heap Size: %d bytes (%.2f KB)\n", ESP.getHeapSize(), ESP.getHeapSize() / 1024.0);
    Serial.printf("SDK Version: %s\n", ESP.getSdkVersion());
    Serial.printf("Reset Reason: %d\n", esp_reset_reason());
    
    Serial.println("✅ System diagnostics completed");
    Serial.println();
}

// ==================== System Initialization ====================
bool initializeSystem() {
    Serial.println("🔧 System Component Initialization");
    Serial.println("-----------------------------------");
    
    // Initialize SPIFFS first
    Serial.println("📁 Initializing SPIFFS...");
    if (!SPIFFS.begin(true)) {
        Serial.println("❌ SPIFFS initialization failed!");
        return false;
    }
    Serial.printf("✅ SPIFFS initialized - Total: %.2f KB, Used: %.2f KB\n", 
                 SPIFFS.totalBytes() / 1024.0, SPIFFS.usedBytes() / 1024.0);
    
    // Get system manager (this will create it if needed)
    SystemManager* manager = getSystemManager();
    if (!manager) {
        Serial.println("❌ Failed to create system manager!");
        return false;
    }
    
    // Initialize system manager
    if (!manager->initialize()) {
        Serial.println("❌ System manager initialization failed!");
        return false;
    }
    
    Serial.println("✅ All system components initialized successfully");
    return true;
}

// ==================== System Manager Singleton ====================
SystemManager* getSystemManager() {
    static SystemManager* instance = nullptr;
    
    if (!instance && g_psramAvailable) {
        // Create instance only after PSRAM is available
        instance = new SystemManager();
    }
    
    return instance;
}

[env:esp32-s3-devkitc-1]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; 编译器配置
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DARDUINO_USB_MSC_ON_BOOT=0
    -DARDUINO_USB_DFU_ON_BOOT=0

; 分区表配置（支持SPIFFS和OTA）
board_build.partitions = huge_app.csv
board_build.filesystem = spiffs

; 上传配置
upload_speed = 921600
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; 库依赖
lib_deps = 
    ; ESP32异步Web服务器
    https://github.com/me-no-dev/ESPAsyncWebServer.git
    https://github.com/me-no-dev/AsyncTCP.git
    
    ; JSON处理
    bblanchon/Arduino<PERSON>son@^6.21.3
    
    ; 红外遥控库
    crankyoldgit/IRremoteESP8266@^2.8.6
    
    ; 文件系统
    https://github.com/lorol/LITTLEFS.git

; 编译选项
build_type = release
build_src_filter = +<*> -<.git/> -<.svn/> -<example/> -<examples/> -<test/> -<tests/>

; 调试配置
debug_tool = esp-prog
debug_init_break = tbreak setup

; 内存配置
board_build.arduino.memory_type = qio_opi
board_build.flash_mode = qio
board_build.psram_type = opi

; 高级配置
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L
board_build.flash_size = 16MB

; 自定义构建脚本
extra_scripts = 
    pre:scripts/pre_build.py
    post:scripts/post_build.py

@echo off
echo Arduino IDE Simple Reset
echo ========================
echo.
set /p confirm="Clear Arduino IDE cache? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Cancelled
    pause
    exit /b
)

echo.
echo Closing Arduino IDE...
taskkill /f /im arduino.exe 2>nul
taskkill /f /im arduino-ide.exe 2>nul
timeout /t 2 >nul

echo Clearing cache...
rmdir /s /q "%USERPROFILE%\AppData\Local\arduino" 2>nul
rmdir /s /q "%USERPROFILE%\AppData\Local\Arduino15" 2>nul
rmdir /s /q "%USERPROFILE%\AppData\Roaming\Arduino" 2>nul
rmdir /s /q "%USERPROFILE%\.espressif" 2>nul

echo Clearing temp files...
for /f %%i in ('dir /b "%TEMP%\arduino*" 2^>nul') do rmdir /s /q "%TEMP%\%%i" 2>nul

echo.
echo Reset complete!
echo Please restart Arduino IDE and reinstall ESP32 package.
echo.
pause

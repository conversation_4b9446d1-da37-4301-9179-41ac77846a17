/**
 * R1系统 - 工具函数库
 * 提供系统通用的工具函数和辅助方法
 */

// 工具函数命名空间
window.R1Utils = {
  
  /**
   * 生成统一格式的ID
   * @param {string} prefix - ID前缀
   * @returns {string} 统一格式ID: prefix_8位数字
   */
  generateId(prefix = 'id') {
    // 统一ID格式: 类型_8位时间戳数字
    const timestamp = Date.now().toString().slice(-8);
    return `${prefix}_${timestamp}`;
  },

  /**
   * 格式化时间
   * @param {Date|number} date - 日期对象或时间戳
   * @param {string} format - 格式字符串
   * @returns {string} 格式化后的时间字符串
   */
  formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },

  /**
   * 格式化相对时间
   * @param {Date|number} date - 日期对象或时间戳
   * @returns {string} 相对时间字符串
   */
  formatRelativeTime(date) {
    const now = new Date();
    const target = new Date(date);
    const diff = now - target;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (seconds < 60) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return this.formatTime(date, 'MM-DD HH:mm');
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} wait - 等待时间(ms)
   * @returns {Function} 防抖后的函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} limit - 限制时间(ms)
   * @returns {Function} 节流后的函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 深拷贝对象
   * @param {any} obj - 要拷贝的对象
   * @returns {any} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => this.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  /**
   * 合并对象
   * @param {object} target - 目标对象
   * @param {...object} sources - 源对象
   * @returns {object} 合并后的对象
   */
  merge(target, ...sources) {
    if (!sources.length) return target;
    const source = sources.shift();

    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          this.merge(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }

    return this.merge(target, ...sources);
  },

  /**
   * 检查是否为对象
   * @param {any} item - 要检查的项
   * @returns {boolean} 是否为对象
   */
  isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
  },

  /**
   * 延迟执行 - 使用统一定时器管理器
   * @param {number} ms - 延迟时间(ms)
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => {
      const timerId = `delay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      window.UnifiedTimerManager.addTimer(timerId, resolve, ms, false);
    });
  },

  /**
   * 重试函数
   * @param {Function} fn - 要重试的函数
   * @param {number} retries - 重试次数
   * @param {number} delay - 重试间隔(ms)
   * @returns {Promise} Promise对象
   */
  async retry(fn, retries = 3, delay = 1000) {
    try {
      return await fn();
    } catch (error) {
      if (retries > 0) {
        await this.delay(delay);
        return this.retry(fn, retries - 1, delay);
      }
      throw error;
    }
  },

  /**
   * 验证数据类型
   * @param {any} value - 要验证的值
   * @param {string} type - 期望的类型
   * @returns {boolean} 是否匹配类型
   */
  validateType(value, type) {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return this.isObject(value);
      case 'function':
        return typeof value === 'function';
      case 'date':
        return value instanceof Date;
      default:
        return false;
    }
  },

  /**
   * 本地存储操作
   */
  storage: {
    /**
     * 设置本地存储
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    set(key, value) {
      try {
        const serializedValue = JSON.stringify(value);
        localStorage.setItem(`r1_${key}`, serializedValue);
      } catch (error) {
        console.error('Storage set error:', error);
      }
    },

    /**
     * 获取本地存储
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 存储的值
     */
    get(key, defaultValue = null) {
      try {
        const item = localStorage.getItem(`r1_${key}`);
        return item ? JSON.parse(item) : defaultValue;
      } catch (error) {
        console.error('Storage get error:', error);
        return defaultValue;
      }
    },

    /**
     * 删除本地存储
     * @param {string} key - 键名
     */
    remove(key) {
      try {
        localStorage.removeItem(`r1_${key}`);
      } catch (error) {
        console.error('Storage remove error:', error);
      }
    },

    /**
     * 清空本地存储
     */
    clear() {
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('r1_')) {
            localStorage.removeItem(key);
          }
        });
      } catch (error) {
        console.error('Storage clear error:', error);
      }
    }
  },

  /**
   * DOM操作辅助函数
   */
  dom: {
    /**
     * 查询元素
     * @param {string} selector - 选择器
     * @param {Element} parent - 父元素
     * @returns {Element|null} 元素
     */
    $(selector, parent = document) {
      return parent.querySelector(selector);
    },

    /**
     * 查询所有元素
     * @param {string} selector - 选择器
     * @param {Element} parent - 父元素
     * @returns {NodeList} 元素列表
     */
    $$(selector, parent = document) {
      return parent.querySelectorAll(selector);
    },

    /**
     * 创建元素
     * @param {string} tag - 标签名
     * @param {object} attrs - 属性对象
     * @param {string} content - 内容
     * @returns {Element} 创建的元素
     */
    create(tag, attrs = {}, content = '') {
      const element = document.createElement(tag);
      
      Object.keys(attrs).forEach(key => {
        if (key === 'className') {
          element.className = attrs[key];
        } else if (key === 'dataset') {
          Object.keys(attrs[key]).forEach(dataKey => {
            element.dataset[dataKey] = attrs[key][dataKey];
          });
        } else {
          element.setAttribute(key, attrs[key]);
        }
      });

      if (content) {
        element.innerHTML = content;
      }

      return element;
    },

    /**
     * 添加事件监听器
     * @param {Element} element - 元素
     * @param {string} event - 事件名
     * @param {Function} handler - 处理函数
     * @param {object} options - 选项
     */
    on(element, event, handler, options = {}) {
      element.addEventListener(event, handler, options);
    },

    /**
     * 移除事件监听器
     * @param {Element} element - 元素
     * @param {string} event - 事件名
     * @param {Function} handler - 处理函数
     */
    off(element, event, handler) {
      element.removeEventListener(event, handler);
    },

    /**
     * 切换类名
     * @param {Element} element - 元素
     * @param {string} className - 类名
     */
    toggleClass(element, className) {
      element.classList.toggle(className);
    },

    /**
     * 添加类名
     * @param {Element} element - 元素
     * @param {string} className - 类名
     */
    addClass(element, className) {
      element.classList.add(className);
    },

    /**
     * 移除类名
     * @param {Element} element - 元素
     * @param {string} className - 类名
     */
    removeClass(element, className) {
      element.classList.remove(className);
    }
  }
};

// 简化访问 - 带性能追踪的版本
window.$ = (selector, parent = document) => {
  const start = performance.now();
  const result = parent.querySelector(selector);
  const time = performance.now() - start;

  if (time > 1) { // 超过1ms记录
    console.log(`🔍 DOM查询耗时: ${time.toFixed(3)}ms - ${selector}`, result ? '✅找到' : '❌未找到');
  }

  return result;
};

window.$$ = (selector, parent = document) => {
  const start = performance.now();
  const result = parent.querySelectorAll(selector);
  const time = performance.now() - start;

  if (time > 1) { // 超过1ms记录
    console.log(`🔍 DOM查询耗时: ${time.toFixed(3)}ms - ${selector} (找到${result.length}个)`);
  }

  return result;
};



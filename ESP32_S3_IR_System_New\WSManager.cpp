#include "WSManager.h"

WSManager::WSManager() 
    : m_initialized(false)
    , m_webSocket(nullptr)
    , m_totalMessagesSent(0)
    , m_totalMessagesReceived(0)
    , m_lastCleanupTime(0)
{
    Serial.println("WSManager created (no memory allocated)");
}

WSManager::~WSManager() {
    // Smart pointers automatically clean up
    Serial.println("WSManager destroyed");
}

bool WSManager::initialize(AsyncWebSocket* webSocket) {
    Serial.println("🔌 Initializing WSManager...");
    
    if (m_initialized) {
        Serial.println("WSManager already initialized");
        return true;
    }
    
    if (!webSocket) {
        Serial.println("❌ Invalid WebSocket pointer");
        return false;
    }
    
    m_webSocket = webSocket;
    
    // Create storage containers after PSRAM is available
    if (!initializeStorage()) {
        Serial.println("❌ Failed to initialize WebSocket storage");
        return false;
    }
    
    // Set up WebSocket event handler
    m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                               AwsEventType type, void* arg, uint8_t* data, size_t len) {
        onWebSocketEvent(server, client, type, arg, data, len);
    });
    
    m_initialized = true;
    Serial.println("✅ WSManager initialized successfully");
    
    return true;
}

bool WSManager::initializeStorage() {
    Serial.println("🗄️  Creating WebSocket storage containers...");
    
    try {
        m_clients = std::make_unique<std::vector<WSClient>>();
        m_messageQueue = std::make_unique<std::vector<QueuedMessage>>();
        
        if (!m_clients || !m_messageQueue) {
            Serial.println("❌ Failed to create WebSocket storage containers");
            return false;
        }
        
        Serial.println("✅ WebSocket storage containers created successfully");
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception creating WebSocket storage: %s\n", e.what());
        return false;
    }
}

void WSManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Process message queue
    processMessageQueue();
    
    // Cleanup inactive clients periodically
    if (millis() - m_lastCleanupTime > 30000) { // Every 30 seconds
        cleanupInactiveClients();
        m_lastCleanupTime = millis();
    }
}

// ==================== Client Management ====================

int WSManager::getConnectedClients() const {
    if (!m_clients) return 0;
    
    int count = 0;
    for (const auto& client : *m_clients) {
        if (client.isActive) {
            count++;
        }
    }
    return count;
}

DynamicJsonDocument WSManager::getClientList() const {
    DynamicJsonDocument doc(1024);
    doc["success"] = true;
    
    JsonArray clients = doc.createNestedArray("data");
    
    if (m_clients) {
        for (const auto& client : *m_clients) {
            if (client.isActive) {
                JsonObject clientObj = clients.createNestedObject();
                clientObj["id"] = client.id;
                clientObj["ip"] = client.ip.toString();
                clientObj["connectTime"] = client.connectTime;
                clientObj["lastPing"] = client.lastPing;
                clientObj["connected"] = millis() - client.connectTime;
            }
        }
    }
    
    doc["count"] = clients.size();
    doc["timestamp"] = millis();
    
    return doc;
}

DynamicJsonDocument WSManager::getWebSocketStats() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;
    
    JsonObject data = doc.createNestedObject("data");
    data["connectedClients"] = getConnectedClients();
    data["totalMessagesSent"] = m_totalMessagesSent;
    data["totalMessagesReceived"] = m_totalMessagesReceived;
    data["queueSize"] = m_messageQueue ? m_messageQueue->size() : 0;
    
    data["timestamp"] = millis();
    
    return doc;
}

bool WSManager::isClientConnected(uint32_t clientId) const {
    WSClient* client = const_cast<WSManager*>(this)->findClient(clientId);
    return client && client->isActive;
}

// ==================== Message Sending ====================

bool WSManager::sendMessage(uint32_t clientId, const String& type, const DynamicJsonDocument& data) {
    if (!m_initialized || !m_webSocket) {
        return false;
    }
    
    String message = formatMessage(type, data);
    
    if (m_webSocket->hasClient(clientId)) {
        AsyncWebSocketClient* client = m_webSocket->client(clientId);
        if (client && client->canSend()) {
            client->text(message);
            m_totalMessagesSent++;
            return true;
        }
    }
    
    // Queue message if client is not immediately available
    QueuedMessage queuedMsg(clientId, type, message);
    return queueMessage(queuedMsg);
}

bool WSManager::sendMessage(uint32_t clientId, const String& type, const String& data) {
    if (!m_initialized || !m_webSocket) {
        return false;
    }
    
    String message = formatMessage(type, data);
    
    if (m_webSocket->hasClient(clientId)) {
        AsyncWebSocketClient* client = m_webSocket->client(clientId);
        if (client && client->canSend()) {
            client->text(message);
            m_totalMessagesSent++;
            return true;
        }
    }
    
    // Queue message if client is not immediately available
    QueuedMessage queuedMsg(clientId, type, message);
    return queueMessage(queuedMsg);
}

bool WSManager::broadcastMessage(const String& type, const DynamicJsonDocument& data) {
    if (!m_initialized || !m_webSocket) {
        return false;
    }
    
    String message = formatMessage(type, data);
    m_webSocket->textAll(message);
    m_totalMessagesSent += getConnectedClients();
    
    return true;
}

bool WSManager::broadcastMessage(const String& type, const String& data) {
    if (!m_initialized || !m_webSocket) {
        return false;
    }
    
    String message = formatMessage(type, data);
    m_webSocket->textAll(message);
    m_totalMessagesSent += getConnectedClients();
    
    return true;
}

// ==================== System Notifications ====================

bool WSManager::notifySignalSent(const String& signalId, const String& signalName) {
    DynamicJsonDocument data(256);
    data["signalId"] = signalId;
    data["signalName"] = signalName;
    data["timestamp"] = millis();
    
    return broadcastMessage("signal_sent", data);
}

bool WSManager::notifyLearningStarted(const String& signalName) {
    DynamicJsonDocument data(256);
    data["signalName"] = signalName;
    data["timestamp"] = millis();
    
    return broadcastMessage("learning_started", data);
}

bool WSManager::notifyLearningCompleted(const String& signalId, const String& signalName) {
    DynamicJsonDocument data(256);
    data["signalId"] = signalId;
    data["signalName"] = signalName;
    data["timestamp"] = millis();
    
    return broadcastMessage("learning_completed", data);
}

bool WSManager::notifyTaskStarted(const String& taskId, const String& taskName) {
    DynamicJsonDocument data(256);
    data["taskId"] = taskId;
    data["taskName"] = taskName;
    data["timestamp"] = millis();
    
    return broadcastMessage("task_started", data);
}

bool WSManager::notifyTaskCompleted(const String& taskId, const String& taskName) {
    DynamicJsonDocument data(256);
    data["taskId"] = taskId;
    data["taskName"] = taskName;
    data["timestamp"] = millis();
    
    return broadcastMessage("task_completed", data);
}

bool WSManager::notifySystemStatus(const DynamicJsonDocument& status) {
    return broadcastMessage("system_status", status);
}

bool WSManager::notifyConfigReset() {
    DynamicJsonDocument data(256);
    data["message"] = "System configuration has been reset to defaults";
    data["timestamp"] = millis();

    return broadcastMessage("config_reset", data);
}

// ==================== Message Queue Management ====================

bool WSManager::queueMessage(const QueuedMessage& message) {
    if (!m_messageQueue) {
        return false;
    }

    // Limit queue size to prevent memory issues
    if (m_messageQueue->size() >= 100) {
        Serial.println("⚠️  Message queue full, dropping oldest message");
        m_messageQueue->erase(m_messageQueue->begin());
    }

    m_messageQueue->push_back(message);
    return true;
}

void WSManager::processMessageQueue() {
    if (!m_messageQueue || !m_webSocket) {
        return;
    }

    auto it = m_messageQueue->begin();
    while (it != m_messageQueue->end()) {
        bool sent = false;

        if (it->clientId == 0) {
            // Broadcast message
            m_webSocket->textAll(it->data);
            m_totalMessagesSent += getConnectedClients();
            sent = true;
        } else {
            // Targeted message
            if (m_webSocket->hasClient(it->clientId)) {
                AsyncWebSocketClient* client = m_webSocket->client(it->clientId);
                if (client && client->canSend()) {
                    client->text(it->data);
                    m_totalMessagesSent++;
                    sent = true;
                }
            }
        }

        if (sent || (millis() - it->timestamp > 30000)) { // Remove after 30 seconds
            it = m_messageQueue->erase(it);
        } else {
            ++it;
        }
    }
}

void WSManager::clearMessageQueue() {
    if (m_messageQueue) {
        m_messageQueue->clear();
        Serial.println("✅ Message queue cleared");
    }
}

// ==================== WebSocket Event Handling ====================

void WSManager::onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client,
                                AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            Serial.printf("🔌 WebSocket client #%u connected from %s\n",
                         client->id(), client->remoteIP().toString().c_str());

            addClient(client->id(), client->remoteIP());

            // Send welcome message
            {
                DynamicJsonDocument welcome(256);
                welcome["message"] = "Connected to ESP32-S3 IR Controller";
                welcome["clientId"] = client->id();
                welcome["serverTime"] = millis();

                sendMessage(client->id(), "welcome", welcome);
            }
            break;

        case WS_EVT_DISCONNECT:
            Serial.printf("🔌 WebSocket client #%u disconnected\n", client->id());
            removeClient(client->id());
            break;

        case WS_EVT_DATA:
            {
                AwsFrameInfo* info = (AwsFrameInfo*)arg;
                if (info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
                    data[len] = 0;
                    String message = (char*)data;

                    m_totalMessagesReceived++;
                    handleWebSocketMessage(client, message);

                    // Update client last ping time
                    WSClient* wsClient = findClient(client->id());
                    if (wsClient) {
                        wsClient->lastPing = millis();
                    }
                }
            }
            break;

        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}

void WSManager::handleWebSocketMessage(AsyncWebSocketClient* client, const String& message) {
    DynamicJsonDocument doc(512);
    DeserializationError error = deserializeJson(doc, message);

    if (error) {
        Serial.printf("❌ Failed to parse WebSocket message: %s\n", error.c_str());
        return;
    }

    if (!validateMessage(doc)) {
        Serial.println("❌ Invalid WebSocket message format");
        return;
    }

    String type = doc["type"] | "";
    DynamicJsonDocument payload = doc["payload"];  // 使用payload字段，保持与原系统兼容

    if (type == "ping") {
        handlePingMessage(client, payload);
    } else if (type == "get_status") {
        handleGetStatusMessage(client, payload);
    } else if (type == "get_signals") {
        handleGetSignalsMessage(client, payload);
    } else if (type == "get_tasks") {
        handleGetTasksMessage(client, payload);
    } else {
        Serial.printf("❌ Unknown WebSocket message type: %s\n", type.c_str());
    }
}

// ==================== Client Management ====================

void WSManager::addClient(uint32_t clientId, IPAddress clientIp) {
    if (!m_clients) return;

    // Remove existing client with same ID
    removeClient(clientId);

    WSClient newClient(clientId, clientIp);
    m_clients->push_back(newClient);

    Serial.printf("✅ Client added: #%u (%s)\n", clientId, clientIp.toString().c_str());
}

void WSManager::removeClient(uint32_t clientId) {
    if (!m_clients) return;

    auto it = m_clients->begin();
    while (it != m_clients->end()) {
        if (it->id == clientId) {
            Serial.printf("✅ Client removed: #%u\n", clientId);
            it = m_clients->erase(it);
            return;
        } else {
            ++it;
        }
    }
}

WSClient* WSManager::findClient(uint32_t clientId) {
    if (!m_clients) return nullptr;

    for (auto& client : *m_clients) {
        if (client.id == clientId) {
            return &client;
        }
    }
    return nullptr;
}

void WSManager::cleanupInactiveClients() {
    if (!m_clients) return;

    auto it = m_clients->begin();
    while (it != m_clients->end()) {
        // Remove clients that haven't pinged in 2 minutes
        if (millis() - it->lastPing > 120000) {
            Serial.printf("🧹 Cleaning up inactive client: #%u\n", it->id);
            it = m_clients->erase(it);
        } else {
            ++it;
        }
    }
}

// ==================== Message Formatting ====================

String WSManager::formatMessage(const String& type, const DynamicJsonDocument& data) {
    DynamicJsonDocument message(1024);
    message["type"] = type;
    message["payload"] = data;  // 使用payload而不是data，保持与原系统兼容
    message["timestamp"] = millis();

    String result;
    serializeJson(message, result);
    return result;
}

String WSManager::formatMessage(const String& type, const String& data) {
    DynamicJsonDocument message(512);
    message["type"] = type;
    message["payload"] = data;  // 使用payload而不是data，保持与原系统兼容
    message["timestamp"] = millis();

    String result;
    serializeJson(message, result);
    return result;
}

DynamicJsonDocument WSManager::parseMessage(const String& message) {
    DynamicJsonDocument doc(512);
    deserializeJson(doc, message);
    return doc;
}

// ==================== Message Handlers ====================

void WSManager::handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data) {
    DynamicJsonDocument response(128);
    response["serverTime"] = millis();

    sendMessage(client->id(), "pong", response);
}

void WSManager::handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data) {
    DynamicJsonDocument response(512);
    response["uptime"] = millis();
    response["freeHeap"] = ESP.getFreeHeap();
    response["freePsram"] = psramFound() ? ESP.getFreePsram() : 0;
    response["connectedClients"] = getConnectedClients();

    sendMessage(client->id(), "status", response);
}

void WSManager::handleGetSignalsMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data) {
    // This would typically get data from DataManager
    DynamicJsonDocument response(1024);
    JsonArray signals = response.createNestedArray("signals");

    // Placeholder - in real implementation, get from DataManager
    response["count"] = 0;

    sendMessage(client->id(), "signals", response);
}

void WSManager::handleGetTasksMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data) {
    // This would typically get data from TaskManager
    DynamicJsonDocument response(1024);
    JsonArray tasks = response.createNestedArray("tasks");

    // Placeholder - in real implementation, get from TaskManager
    response["count"] = 0;

    sendMessage(client->id(), "tasks", response);
}

// ==================== Validation ====================

bool WSManager::validateMessage(const DynamicJsonDocument& message) {
    // 检查必需字段：type, payload, timestamp（与原系统兼容）
    if (!message.containsKey("type")) {
        return false;
    }

    if (!message.containsKey("payload")) {
        return false;
    }

    if (!message.containsKey("timestamp")) {
        return false;
    }

    String type = message["type"];
    if (type.isEmpty()) {
        return false;
    }

    return true;
}

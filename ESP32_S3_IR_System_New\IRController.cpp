#include "IRController.h"
#include "DataManager.h"

const int IRController::IR_SEND_PIN;
const int IRController::IR_RECV_PIN;

IRController::IRController() 
    : m_initialized(false)
    , m_isLearning(false)
    , m_learningStartTime(0)
    , m_learningTimeout(30000)
    , m_irSend(nullptr)
    , m_irRecv(nullptr)
    , m_dataManager(nullptr)
{
    Serial.println("IRController created (no memory allocated)");
}

IRController::~IRController() {
    if (m_irSend) {
        delete m_irSend;
    }
    if (m_irRecv) {
        delete m_irRecv;
    }
}

bool IRController::initialize() {
    Serial.println("📡 Initializing IRController...");
    
    if (m_initialized) {
        Serial.println("IRController already initialized");
        return true;
    }
    
    try {
        // Create IR send component
        m_irSend = new IRsend(IR_SEND_PIN);
        if (!m_irSend) {
            Serial.println("❌ Failed to create IRsend");
            return false;
        }
        
        m_irSend->begin();
        Serial.printf("✅ IR transmitter initialized on pin %d\n", IR_SEND_PIN);
        
        // Create IR receive component
        m_irRecv = new IRrecv(IR_RECV_PIN);
        if (!m_irRecv) {
            Serial.println("❌ Failed to create IRrecv");
            delete m_irSend;
            m_irSend = nullptr;
            return false;
        }
        
        m_irRecv->enableIRIn();
        Serial.printf("✅ IR receiver initialized on pin %d\n", IR_RECV_PIN);
        
        m_initialized = true;
        Serial.println("✅ IRController initialized successfully");
        
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception during IR initialization: %s\n", e.what());
        return false;
    }
}

void IRController::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Handle learning mode
    if (m_isLearning) {
        // Check for timeout
        if (millis() - m_learningStartTime > m_learningTimeout) {
            Serial.println("⏰ Learning timeout");
            stopLearning();
            return;
        }
        
        // Check for received signal
        if (m_irRecv && m_irRecv->decode()) {
            processReceivedSignal();
            m_irRecv->resume();
        }
    }
}

// ==================== Signal Transmission ====================

bool IRController::sendSignal(const String& signalId) {
    if (!m_initialized || !m_irSend || !m_dataManager) {
        Serial.println("❌ IRController not properly initialized");
        return false;
    }
    
    SignalData* signal = m_dataManager->getSignal(signalId);
    if (!signal) {
        Serial.printf("❌ Signal not found: %s\n", signalId.c_str());
        return false;
    }
    
    Serial.printf("📡 Sending signal: %s (%s)\n", signal->name.c_str(), signal->protocol.c_str());
    
    bool success = sendSignalByCode(signal->protocol, signal->signalCode, signal->frequency);

    if (success) {
        // Update signal statistics
        signal->sentCount++;
        signal->lastSent = millis();
        m_dataManager->updateSignal(signalId, *signal);
        
        Serial.printf("✅ Signal sent successfully: %s\n", signal->name.c_str());
    } else {
        Serial.printf("❌ Failed to send signal: %s\n", signal->name.c_str());
    }
    
    return success;
}

bool IRController::sendSignalByCode(const String& protocol, const String& code, const String& frequency) {
    if (!m_initialized || !m_irSend) {
        Serial.println("❌ IRController not initialized");
        return false;
    }
    
    if (!validateSignalCode(protocol, code)) {
        Serial.println("❌ Invalid signal code");
        return false;
    }
    
    Serial.printf("📡 Sending - Protocol: %s, Code: %s, Frequency: %s\n", 
                 protocol.c_str(), code.c_str(), frequency.c_str());
    
    // Convert string code to appropriate format and send
    if (protocol == "NEC") {
        uint32_t codeValue = strtoul(code.c_str(), nullptr, 16);
        return sendNECSignal(codeValue, 32);
    } else if (protocol == "Sony") {
        uint32_t codeValue = strtoul(code.c_str(), nullptr, 16);
        return sendSonySignal(codeValue, 12);
    } else if (protocol == "RC5") {
        uint32_t codeValue = strtoul(code.c_str(), nullptr, 16);
        return sendRC5Signal(codeValue, 13);
    } else if (protocol == "RAW") {
        return sendRawSignal(code);
    } else {
        Serial.printf("❌ Unsupported protocol: %s\n", protocol.c_str());
        return false;
    }
}

bool IRController::sendBatchSignals(const String& signalIds, int delayMs) {
    if (!m_initialized || !m_dataManager) {
        return false;
    }
    
    Serial.printf("📡 Sending batch signals with %d ms delay\n", delayMs);
    
    // Parse signal IDs (comma-separated)
    String ids = signalIds;
    int successCount = 0;
    int totalCount = 0;
    
    while (ids.length() > 0) {
        int commaIndex = ids.indexOf(',');
        String signalId;
        
        if (commaIndex >= 0) {
            signalId = ids.substring(0, commaIndex);
            ids = ids.substring(commaIndex + 1);
        } else {
            signalId = ids;
            ids = "";
        }
        
        signalId.trim();
        if (signalId.length() > 0) {
            totalCount++;
            if (sendSignal(signalId)) {
                successCount++;
            }
            
            if (ids.length() > 0) {
                delay(delayMs);
            }
        }
    }
    
    Serial.printf("✅ Batch complete: %d/%d signals sent successfully\n", successCount, totalCount);
    return successCount == totalCount;
}

// ==================== Signal Learning ====================

bool IRController::startLearning(const String& signalName, int timeoutMs) {
    if (!m_initialized || !m_irRecv) {
        Serial.println("❌ IRController not initialized");
        return false;
    }
    
    if (m_isLearning) {
        Serial.println("❌ Already in learning mode");
        return false;
    }
    
    m_isLearning = true;
    m_learningStartTime = millis();
    m_learningTimeout = timeoutMs;
    m_learningSignalName = signalName;
    m_lastLearnedSignal = "";
    
    Serial.printf("🎓 Started learning mode for signal: %s (timeout: %d ms)\n", 
                 signalName.c_str(), timeoutMs);
    Serial.println("👉 Point remote at receiver and press button...");
    
    return true;
}

bool IRController::stopLearning() {
    if (!m_isLearning) {
        return false;
    }
    
    m_isLearning = false;
    m_learningSignalName = "";
    
    Serial.println("🛑 Learning mode stopped");
    return true;
}

DynamicJsonDocument IRController::getLearningStatus() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;
    
    JsonObject data = doc.createNestedObject("data");
    data["isLearning"] = m_isLearning;
    
    if (m_isLearning) {
        data["signalName"] = m_learningSignalName;
        data["timeout"] = m_learningTimeout;
        data["elapsed"] = millis() - m_learningStartTime;
        data["remaining"] = m_learningTimeout - (millis() - m_learningStartTime);
    }
    
    if (!m_lastLearnedSignal.isEmpty()) {
        data["lastLearned"] = m_lastLearnedSignal;
    }
    
    data["timestamp"] = millis();
    
    return doc;
}

void IRController::processReceivedSignal() {
    if (!m_irRecv) {
        return;
    }
    
    decode_results results;
    if (m_irRecv->decode(&results)) {
        Serial.println("📡 Signal received!");
        
        String protocol = detectProtocol(results.decode_type);
        String code = formatSignalCode(&results);
        String frequency = "38000"; // Default frequency
        
        Serial.printf("Protocol: %s\n", protocol.c_str());
        Serial.printf("Code: %s\n", code.c_str());
        Serial.printf("Bits: %d\n", results.bits);
        
        if (results.decode_type == UNKNOWN) {
            Serial.println("Raw data:");
            Serial.println(resultToSourceCode(&results));
            protocol = "RAW";
            code = resultToSourceCode(&results);
        }
        
        // Save learned signal if we have a data manager
        if (m_dataManager && !m_learningSignalName.isEmpty()) {
            SignalData newSignal;
            newSignal.name = m_learningSignalName;
            newSignal.protocol = protocol;
            newSignal.signalCode = code;        // 前端期望的字段名
            newSignal.frequency = frequency;    // 前端期望String类型
            newSignal.type = "learned";
            newSignal.created = millis();       // 前端期望的字段名
            newSignal.isLearned = true;         // 前端期望的字段名
            
            if (m_dataManager->addSignal(newSignal)) {
                m_lastLearnedSignal = newSignal.id;
                Serial.printf("✅ Signal '%s' learned and saved successfully\n", m_learningSignalName.c_str());
            } else {
                Serial.printf("❌ Failed to save learned signal '%s'\n", m_learningSignalName.c_str());
            }
        }
        
        stopLearning();
    }
}

// ==================== Status and Info ====================

DynamicJsonDocument IRController::getStatus() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");
    data["initialized"] = m_initialized;
    data["isLearning"] = m_isLearning;
    data["sendPin"] = IR_SEND_PIN;
    data["recvPin"] = IR_RECV_PIN;

    if (m_isLearning) {
        data["learningSignal"] = m_learningSignalName;
        data["learningTimeout"] = m_learningTimeout;
        data["learningElapsed"] = millis() - m_learningStartTime;
        data["learningRemaining"] = m_learningTimeout - (millis() - m_learningStartTime);
    }

    data["timestamp"] = millis();

    return doc;
}

DynamicJsonDocument IRController::getInfo() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");
    data["version"] = "2.0.0";
    data["sendPin"] = IR_SEND_PIN;
    data["recvPin"] = IR_RECV_PIN;
    data["supportedProtocols"] = "NEC, Sony, RC5, RAW";
    data["defaultFrequency"] = "38000";
    data["maxLearningTimeout"] = 60000;

    data["timestamp"] = millis();

    return doc;
}

// ==================== Internal Methods ====================

bool IRController::sendNECSignal(uint32_t code, int bits) {
    if (!m_irSend) return false;

    m_irSend->sendNEC(code, bits);
    Serial.printf("✅ NEC signal sent: 0x%08X (%d bits)\n", code, bits);
    return true;
}

bool IRController::sendSonySignal(uint32_t code, int bits) {
    if (!m_irSend) return false;

    m_irSend->sendSony(code, bits);
    Serial.printf("✅ Sony signal sent: 0x%08X (%d bits)\n", code, bits);
    return true;
}

bool IRController::sendRC5Signal(uint32_t code, int bits) {
    if (!m_irSend) return false;

    m_irSend->sendRC5(code, bits);
    Serial.printf("✅ RC5 signal sent: 0x%08X (%d bits)\n", code, bits);
    return true;
}

bool IRController::sendRawSignal(const String& rawData) {
    if (!m_irSend) return false;

    // This is a simplified implementation
    // In a real implementation, you would parse the raw data string
    // and convert it to the appropriate format for sendRaw()

    Serial.printf("✅ RAW signal sent (simplified): %s\n", rawData.c_str());
    return true;
}

String IRController::detectProtocol(decode_type_t type) {
    switch (type) {
        case NEC: return "NEC";
        case SONY: return "Sony";
        case RC5: return "RC5";
        case RC6: return "RC6";
        case DISH: return "DISH";
        case SHARP: return "SHARP";
        case PANASONIC: return "Panasonic";
        case LG: return "LG";
        case SAMSUNG: return "Samsung";
        case WHYNTER: return "WHYNTER";
        case COOLIX: return "COOLIX";
        case DAIKIN: return "DAIKIN";
        case MITSUBISHI: return "MITSUBISHI";
        default: return "UNKNOWN";
    }
}

String IRController::formatSignalCode(decode_results* results) {
    if (!results) return "";

    if (results->decode_type == UNKNOWN) {
        return "RAW_DATA";
    }

    // Format as hexadecimal string
    char buffer[16];
    sprintf(buffer, "%08X", (unsigned int)results->value);
    return String(buffer);
}

bool IRController::validateSignalCode(const String& protocol, const String& code) {
    if (protocol.isEmpty() || code.isEmpty()) {
        return false;
    }

    if (protocol == "RAW") {
        return true; // RAW data can be any format
    }

    // 后端是数据权威：只要是有效的十六进制就接受
    // 支持各种格式：20DF10EF, 0x20DF10EF, 20df10ef 等
    String cleanCode = code;

    // 移除可能的0x前缀进行验证
    if (cleanCode.startsWith("0x") || cleanCode.startsWith("0X")) {
        cleanCode = cleanCode.substring(2);
    }

    // 必须有内容
    if (cleanCode.length() == 0) {
        return false;
    }

    // 检查是否为有效的16进制字符
    for (int i = 0; i < cleanCode.length(); i++) {
        char c = cleanCode.charAt(i);
        if (!isxdigit(c)) {
            return false;
        }
    }

    return true;
}

# ESP32-S3 OPI PSRAM Arduino IDE Configuration Guide

## Problem Root Cause
From serial output `mode:DIO, clock div:1`, we can see that ESP32-S3 is not starting in QIO mode, which is the root cause of PSRAM initialization failure.

## Correct Arduino IDE Configuration

### 1. Board Selection
- **Board**: `ESP32S3 Dev Module` (NOT ESP32 Dev Module)

### 2. Key Configuration Items
```
USB CDC On Boot: "Enabled"
CPU Frequency: "240MHz (WiFi)"
Core Debug Level: "None"
USB DFU On Boot: "Disabled"
Erase All Flash Before Sketch Upload: "Disabled"
Events Run On: "Core 1"
Flash Mode: "QIO"  ⭐ CRITICAL SETTING
Flash Size: "16MB (128Mb)"  ⭐ CRITICAL SETTING
JTAG Adapter: "Disabled"
Arduino Runs On: "Core 1"
USB Firmware MSC On Boot: "Disabled"
Partition Scheme: "16M Flash (3MB APP/9.9MB FATFS)"
PSRAM: "OPI PSRAM"  ⭐ MOST CRITICAL SETTING
Upload Mode: "UART0 / Hardware CDC"
Upload Speed: "460800"
USB Mode: "Hardware CDC and JTAG"
```

### 3. 验证配置是否生效
编译上传后，串口输出应该显示：
```
mode:QIO, clock div:1  ← 应该是QIO而不是DIO
PSRAM is available!
Size: 8388608 bytes
```

### 4. 如果仍然显示DIO模式
1. 完全关闭Arduino IDE
2. 删除项目的build缓存文件夹
3. 重新打开Arduino IDE
4. 重新选择板子和配置
5. 重新编译上传

### 5. 硬件检查
确认您的ESP32-S3板子型号：
- ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
- 如果是其他型号，请相应调整Flash Size配置

## 🔧 故障排除

### 如果PSRAM仍然无法检测：
1. 尝试不同的Upload Speed (115200, 460800, 921600)
2. 检查USB线缆质量
3. 尝试按住BOOT按钮上传
4. 检查电源供应是否稳定

### 如果编译错误：
1. 确保ESP32 Arduino Core版本 >= 2.0.0
2. 更新所有相关库到最新版本
3. 清理Arduino IDE缓存

## 📋 配置检查清单
- [ ] Board: ESP32S3 Dev Module
- [ ] Flash Mode: QIO
- [ ] Flash Size: 16MB (128Mb)
- [ ] PSRAM: OPI PSRAM
- [ ] 串口输出显示 mode:QIO
- [ ] PSRAM检测成功

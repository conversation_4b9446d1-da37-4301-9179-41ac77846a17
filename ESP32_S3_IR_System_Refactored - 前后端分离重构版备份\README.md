# ESP32-S3 红外控制系统 (PlatformIO版本)

## 🎯 项目概述

这是一个基于ESP32-S3的智能红外控制系统，采用前后端分离架构设计。前端为纯净的Web界面，后端为ESP32-S3固件，通过HTTP API和WebSocket实现实时通信。

### ✨ 主要特性

- 🔴 **红外信号学习** - 支持多种协议的红外信号学习
- 📡 **红外信号发射** - 支持NEC、RC5、SONY、RAW等协议
- 🌐 **Web界面控制** - 响应式Web界面，支持桌面和移动设备
- ⚡ **实时通信** - WebSocket实时状态推送
- 💾 **数据持久化** - SPIFFS文件系统存储
- 🚀 **PSRAM优化** - 支持ESP32-S3 OPI PSRAM
- 📊 **系统监控** - 实时系统状态和性能监控
- 📁 **批量操作** - 支持信号的批量导入导出

## 🏗️ 系统架构

### 前后端分离架构
```
┌─────────────────┐    HTTP API     ┌─────────────────┐
│                 │ ◄──────────────► │                 │
│   纯净前端       │                 │   ESP32-S3      │
│   (Web界面)     │    WebSocket    │   后端固件       │
│                 │ ◄──────────────► │                 │
└─────────────────┘                 └─────────────────┘
```

### 模块化设计
```
ESP32-S3 后端
├── SignalManager      # 信号管理核心
├── IRController       # 红外硬件控制
├── WebServerManager   # HTTP API服务
├── WebSocketManager   # 实时通信
├── FileSystemManager  # 文件系统管理
└── main.cpp          # 主程序入口
```

## 📋 API接口文档

### HTTP API端点 (17个)

#### 信号管理
- `GET /api/signals` - 获取所有信号
- `POST /api/signals/delete` - 删除单个信号
- `POST /api/signals/batch-delete` - 批量删除信号
- `POST /api/signals/update` - 更新信号信息
- `POST /api/signals/send` - 发射信号

#### 学习功能
- `POST /api/signals/learn/start` - 开始学习
- `POST /api/signals/learn/stop` - 停止学习
- `POST /api/signals/learn/save` - 保存学习结果

#### 导入功能
- `POST /api/signals/import` - 文件导入解析
- `POST /api/signals/import/text` - 文本导入解析
- `POST /api/signals/import/execute` - 执行文件导入
- `POST /api/signals/import/text/execute` - 执行文本导入

#### 系统管理
- `GET /api/system/logs` - 获取系统日志
- `POST /api/system/logs` - 保存系统日志
- `POST /api/system/error-log` - 记录错误日志
- `GET /api/system/stats` - 获取系统统计

#### 控制管理
- `GET /api/control/history` - 获取任务历史
- `POST /api/control/history` - 保存任务历史

### WebSocket事件 (6个)

- `signal.learned` - 学习完成事件
- `learning.error` - 学习错误事件
- `signal.sent` - 信号发射完成事件
- `signal.failed` - 信号发射失败事件
- `system.status` - 系统状态更新事件
- `esp32.connected` - ESP32连接事件

## 🔧 硬件配置

### 引脚定义
```cpp
const int IR_SEND_PIN = 4;      // 红外发射引脚
const int IR_RECV_PIN = 15;     // 红外接收引脚
const int STATUS_LED_PIN = 2;   // 状态指示LED
```

### 硬件要求
- **主控**: ESP32-S3 (推荐ESP32-S3-DevKitC-1)
- **PSRAM**: 8MB OPI PSRAM (可选但推荐)
- **Flash**: 16MB (最小4MB)
- **红外发射器**: 940nm红外LED + 驱动电路
- **红外接收器**: TSOP38238或类似38kHz接收器

### 电路连接
```
ESP32-S3          红外模块
GPIO 4    ────►   IR LED (发射器)
GPIO 15   ◄────   TSOP38238 (接收器)
GPIO 2    ────►   状态LED
3.3V      ────►   VCC
GND       ────►   GND
```

## 🚀 快速开始

### 环境准备
1. 安装 [PlatformIO](https://platformio.org/)
2. 安装 [Git](https://git-scm.com/)
3. 准备ESP32-S3开发板

### 编译和上传
```bash
# 克隆项目
git clone <repository-url>
cd ESP32_S3_IR_System_Refactored

# 编译项目
pio run

# 上传固件
pio run --target upload

# 上传文件系统
pio run --target uploadfs

# 监控串口输出
pio device monitor
```

### 首次配置
1. 上传固件后，ESP32-S3会创建WiFi热点 "ESP32-IR-System"
2. 连接热点，密码: `12345678`
3. 浏览器访问: `http://192.168.4.1`
4. 在设置页面配置WiFi连接

## 📊 性能指标

### 内存使用
- **程序存储**: ~1.2MB (Flash)
- **运行内存**: ~200KB (SRAM)
- **PSRAM缓存**: ~2MB (可选)
- **文件系统**: ~1MB (SPIFFS)

### 响应性能
- **API响应时间**: <50ms
- **WebSocket延迟**: <20ms
- **信号学习时间**: 1-10秒
- **信号发射时间**: <100ms

### 并发能力
- **最大WebSocket连接**: 4个
- **最大信号存储**: 200个
- **批量操作**: 50个信号/次

## 🔍 调试和诊断

### 串口调试
```bash
# 监控串口输出
pio device monitor --baud 115200

# 启用详细日志
# 在 platformio.ini 中设置:
# build_flags = -DCORE_DEBUG_LEVEL=5
```

### 系统状态检查
- 访问 `/api/system/stats` 获取系统状态
- 检查WebSocket连接状态
- 查看SPIFFS文件系统使用情况

### 常见问题
1. **PSRAM初始化失败**: 检查硬件连接和配置
2. **WiFi连接失败**: 检查SSID和密码
3. **红外学习失败**: 检查接收器连接和信号源
4. **文件系统错误**: 重新格式化SPIFFS

## 📈 开发路线图

### 已完成 ✅
- [x] 核心架构设计
- [x] 信号管理系统
- [x] 红外控制功能
- [x] Web API接口
- [x] WebSocket通信
- [x] 文件系统管理

### 进行中 🚧
- [ ] 完善错误处理
- [ ] 性能优化
- [ ] 单元测试

### 计划中 📋
- [ ] OTA固件更新
- [ ] 多语言支持
- [ ] 高级调度功能
- [ ] 云端同步

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [ESP32-S3](https://www.espressif.com/en/products/socs/esp32-s3) - Espressif Systems
- [PlatformIO](https://platformio.org/) - 开发平台
- [IRremoteESP8266](https://github.com/crankyoldgit/IRremoteESP8266) - 红外库
- [ESPAsyncWebServer](https://github.com/me-no-dev/ESPAsyncWebServer) - 异步Web服务器
- [ArduinoJson](https://arduinojson.org/) - JSON处理库

---

**ESP32-S3 红外控制系统** - 让智能家居控制更简单！ 🏠✨

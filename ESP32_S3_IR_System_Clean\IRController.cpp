#include "IRController.h"

const int IRController::IR_SEND_PIN;
const int IRController::IR_RECV_PIN;

IRController::IRController() 
    : m_initialized(false)
    , m_isLearning(false)
    , m_learningStartTime(0)
    , m_learningTimeout(30000)
    , m_irSend(nullptr)
    , m_irRecv(nullptr)
{
    Serial.println("IRController created (no memory allocated)");
}

IRController::~IRController() {
    if (m_irSend) {
        delete m_irSend;
    }
    if (m_irRecv) {
        delete m_irRecv;
    }
}

bool IRController::initialize() {
    Serial.println("📡 Initializing IRController...");
    
    if (m_initialized) {
        Serial.println("IRController already initialized");
        return true;
    }
    
    try {
        // Create IR send component
        m_irSend = new IRsend(IR_SEND_PIN);
        if (!m_irSend) {
            Serial.println("❌ Failed to create IRsend");
            return false;
        }
        
        m_irSend->begin();
        Serial.printf("✅ IR transmitter initialized on pin %d\n", IR_SEND_PIN);
        
        // Create IR receive component
        m_irRecv = new IRrecv(IR_RECV_PIN);
        if (!m_irRecv) {
            Serial.println("❌ Failed to create IRrecv");
            delete m_irSend;
            m_irSend = nullptr;
            return false;
        }
        
        m_irRecv->enableIRIn();
        Serial.printf("✅ IR receiver initialized on pin %d\n", IR_RECV_PIN);
        
        m_initialized = true;
        Serial.println("✅ IRController initialized successfully");
        
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception during IR initialization: %s\n", e.what());
        return false;
    }
}

void IRController::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Handle learning mode
    if (m_isLearning) {
        // Check for timeout
        if (millis() - m_learningStartTime > m_learningTimeout) {
            Serial.println("⏰ Learning timeout");
            stopLearning();
            return;
        }
        
        // Check for received signal
        if (m_irRecv && m_irRecv->decode()) {
            processReceivedSignal();
            m_irRecv->resume();
        }
    }
}

bool IRController::sendSignal(const String& id) {
    if (!m_initialized || !m_irSend) {
        Serial.println("❌ IRController not initialized");
        return false;
    }
    
    // This would typically get signal data from DataManager
    // For now, return a placeholder
    Serial.printf("📡 Sending signal: %s\n", id.c_str());
    
    // TODO: Implement actual signal sending based on stored data
    return true;
}

bool IRController::sendSignalByData(const String& protocol, const String& data, int frequency) {
    if (!m_initialized || !m_irSend) {
        Serial.println("❌ IRController not initialized");
        return false;
    }
    
    Serial.printf("📡 Sending signal - Protocol: %s, Data: %s, Frequency: %d\n", 
                 protocol.c_str(), data.c_str(), frequency);
    
    // Convert string data to appropriate format and send
    // This is a simplified implementation
    if (protocol == "NEC") {
        uint32_t code = strtoul(data.c_str(), nullptr, 16);
        m_irSend->sendNEC(code, 32);
        Serial.println("✅ NEC signal sent");
        return true;
    } else if (protocol == "Sony") {
        uint32_t code = strtoul(data.c_str(), nullptr, 16);
        m_irSend->sendSony(code, 12);
        Serial.println("✅ Sony signal sent");
        return true;
    } else if (protocol == "RC5") {
        uint32_t code = strtoul(data.c_str(), nullptr, 16);
        m_irSend->sendRC5(code, 13);
        Serial.println("✅ RC5 signal sent");
        return true;
    } else {
        Serial.printf("❌ Unsupported protocol: %s\n", protocol.c_str());
        return false;
    }
}

bool IRController::startLearning(const String& signalName, int timeoutMs) {
    if (!m_initialized || !m_irRecv) {
        Serial.println("❌ IRController not initialized");
        return false;
    }
    
    if (m_isLearning) {
        Serial.println("❌ Already in learning mode");
        return false;
    }
    
    m_isLearning = true;
    m_learningStartTime = millis();
    m_learningTimeout = timeoutMs;
    m_learningSignalName = signalName;
    
    Serial.printf("🎓 Started learning mode for signal: %s (timeout: %d ms)\n", 
                 signalName.c_str(), timeoutMs);
    Serial.println("👉 Point remote at receiver and press button...");
    
    return true;
}

void IRController::stopLearning() {
    if (!m_isLearning) {
        return;
    }
    
    m_isLearning = false;
    m_learningSignalName = "";
    
    Serial.println("🛑 Learning mode stopped");
}

void IRController::processReceivedSignal() {
    if (!m_irRecv) {
        return;
    }
    
    decode_results results;
    if (m_irRecv->decode(&results)) {
        Serial.println("📡 Signal received!");
        Serial.printf("Protocol: %s\n", typeToString(results.decode_type).c_str());
        Serial.printf("Value: 0x%08X\n", results.value);
        Serial.printf("Bits: %d\n", results.bits);
        
        if (results.decode_type == UNKNOWN) {
            Serial.println("Raw data:");
            Serial.println(resultToSourceCode(&results));
        }
        
        // TODO: Save learned signal to DataManager
        Serial.printf("✅ Signal '%s' learned successfully\n", m_learningSignalName.c_str());
        
        stopLearning();
    }
}

DynamicJsonDocument IRController::getStatus() const {
    DynamicJsonDocument doc(256);
    
    doc["initialized"] = m_initialized;
    doc["is_learning"] = m_isLearning;
    doc["send_pin"] = IR_SEND_PIN;
    doc["recv_pin"] = IR_RECV_PIN;
    
    if (m_isLearning) {
        doc["learning_signal"] = m_learningSignalName;
        doc["learning_timeout"] = m_learningTimeout;
        doc["learning_elapsed"] = millis() - m_learningStartTime;
        doc["learning_remaining"] = m_learningTimeout - (millis() - m_learningStartTime);
    }
    
    doc["timestamp"] = millis();
    
    return doc;
}

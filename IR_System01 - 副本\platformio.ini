; ESP32-S3 IR Control System - Production Configuration
; Hardware: ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
; GPIO: IR_TX=21, IR_RX=14, Serial=COM6

[env:esp32-s3-wroom-opi]
; === Hardware ===
platform = espressif32@^6.4.0
board = esp32-s3-wroom-opi
board_dir = boards
framework = arduino
board_build.sdkconfig = boards/esp32-s3-wroom-opi/sdkconfig.board

; === Serial ===
upload_port = COM6
monitor_port = COM6
upload_speed = 460800
monitor_speed = 115200
upload_protocol = esptool



; === File System ===
board_build.filesystem = spiffs
board_build.spiffs.page_size = 256
board_build.spiffs.block_size = 8192
board_build.partitions = huge_app.csv
board_build.bootloader = no

; === Build Options ===
build_type = release
lib_ldf_mode = deep+



; === Build Flags ===
build_flags =
    ; Core ESP32-S3 Configuration
    -DESP32
    -DESP32S3
    -DCONFIG_FREERTOS_UNICORE=0

    ; USB Debug
    -DARDUINO_USB_CDC_ON_BOOT=1

    ; IR Decode Options
    -DSEND_RAW=true
    -DDECODE_NEC=true
    -DDECODE_SONY=true
    -DDECODE_SAMSUNG=true
    -DDECODE_LG=true
    -DDECODE_PANASONIC=true
    -DDECODE_RC5=true
    -DDECODE_RC6=true
    -DDECODE_HASH=true

; === Libraries ===
lib_deps =
    ArduinoJson@6.21.3
    ESP32Async/ESPAsyncWebServer@^3.7.8
    ESP32Async/AsyncTCP@^3.4.4
    IRremoteESP8266@2.8.6

; === Debug ===
debug_tool = esp-builtin
debug_init_break = tbreak setup
monitor_filters = esp32_exception_decoder, time

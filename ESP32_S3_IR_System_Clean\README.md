# 🚀 ESP32-S3 IR Control System - Clean Architecture

A complete infrared control system for ESP32-S3 with OPI PSRAM, featuring **zero global memory allocation** design to completely eliminate PSRAM initialization conflicts.

## ✨ Key Features

### 🎯 **Clean Architecture Design**
- **Zero Global Memory Allocation**: All objects created after PSRAM initialization
- **Singleton Pattern**: Delayed initialization prevents memory conflicts
- **Smart Pointers**: Automatic memory management with std::unique_ptr
- **Component Isolation**: Each module is independently managed

### 🧠 **PSRAM Optimization**
- **8MB OPI PSRAM Support**: Full utilization of external memory
- **Comprehensive Testing**: Allocation, read/write integrity verification
- **Flash Mode Detection**: Automatic QIO mode verification
- **Safe Fallback**: System enters safe mode if PSRAM fails

### 📡 **IR Control Features**
- **Multi-Protocol Support**: NEC, Sony, RC5, and more
- **Signal Learning**: Capture and store remote control signals
- **Batch Transmission**: Send multiple signals in sequence
- **Real-time Feedback**: WebSocket-based status updates

### 🌐 **Web Interface**
- **Responsive Design**: Works on desktop and mobile
- **Real-time Updates**: WebSocket communication
- **RESTful API**: Complete HTTP API for integration
- **Modern UI**: Clean, intuitive interface

### 📊 **System Monitoring**
- **Memory Tracking**: Real-time Heap and PSRAM usage
- **Performance Metrics**: CPU, WiFi, and system statistics
- **Error Handling**: Comprehensive error reporting and recovery
- **Logging**: Detailed system logs with timestamps

## 🛠️ Hardware Requirements

### ESP32-S3 Board
- **Model**: ESP32-S3-WROOM-1-N16R8
- **Flash**: 16MB
- **PSRAM**: 8MB OPI PSRAM
- **Power**: Stable 3.3V supply

### IR Components
- **IR LED**: Connected to GPIO4
- **IR Receiver**: TSOP38238 or similar, connected to GPIO5
- **Current Limiting Resistor**: For IR LED (typically 100-330Ω)

## 📋 Quick Start

### 1. Arduino IDE Configuration
```
Board: ESP32S3 Dev Module
Flash Mode: QIO (CRITICAL!)
Flash Size: 16MB (128Mb)
PSRAM: OPI PSRAM (CRITICAL!)
Partition Scheme: 16M Flash (3MB APP/9.9MB FATFS)
Upload Speed: 460800
CPU Frequency: 240MHz (WiFi)
```

### 2. Install Required Libraries
```
- ESP32 Arduino Core (latest stable)
- ESPAsyncWebServer by lacamera
- AsyncTCP by dvarrel
- ArduinoJson by Benoit Blanchon (v6.21.3+)
- IRremoteESP8266 by David Conran
```

### 3. Upload Code
1. Open `ESP32_S3_IR_System_Clean.ino` in Arduino IDE
2. Verify all settings match the configuration above
3. Compile and upload to your ESP32-S3 board

### 4. Upload Web Interface (Optional)
1. Install "ESP32 Sketch Data Upload" tool
2. Select `Tools > ESP32 Sketch Data Upload`
3. Wait for SPIFFS upload to complete

### 5. Connect and Test
1. Open Serial Monitor (115200 baud)
2. Look for successful PSRAM detection
3. Connect to WiFi AP: `ESP32-S3-IR-[ChipID]`
4. Visit: http://***********

## 📊 Expected Output

### Successful Startup
```
========================================
ESP32-S3 IR Control System - Clean Architecture
Zero Global Memory Allocation Design
========================================

🔍 System Diagnostics
----------------------
Free Heap: 394532 bytes (385.28 KB)
Heap Size: 417792 bytes (408.00 KB)
SDK Version: v4.4.2
✅ System diagnostics completed

🔬 PSRAM Detection and Initialization
----------------------------------------
Chip Model: ESP32-S3
Flash Mode: QIO ✅
✅ PSRAM DETECTION SUCCESSFUL!
📊 PSRAM Size: 8388608 bytes (8.00 MB)
📊 PSRAM Free: 8388608 bytes (8.00 MB)
✅ PSRAM allocation test passed
✅ PSRAM large allocation test passed
🎉 PSRAM is fully functional and ready!

🔧 System Component Initialization
-----------------------------------
📁 Initializing SPIFFS...
✅ SPIFFS initialized - Total: 9728.00 KB, Used: 0.00 KB
✅ System initialization completed successfully!
⏱️  Total initialization time: 2847 ms
🚀 System ready for operation
========================================
```

## 🌐 Web Interface

### Access Points
- **AP Mode**: ESP32-S3-IR-[ChipID] (Password: 12345678)
- **Station Mode**: Auto-connects to saved WiFi credentials
- **Web Interface**: http://*********** (AP) or http://[assigned-ip] (Station)

### Features
- **Real-time Status**: System uptime, memory usage, connection status
- **IR Control**: Send signals, start learning mode
- **WebSocket Communication**: Live updates and bidirectional communication
- **Responsive Design**: Works on all screen sizes

## 🔧 API Reference

### REST Endpoints
```
GET  /api/status          - System status
GET  /api/signals         - List all signals
POST /api/signals         - Add new signal
POST /api/signals/send    - Send IR signal
```

### WebSocket Messages
```
{
  "type": "get_status",
  "timestamp": 1234567890
}

{
  "type": "ping",
  "timestamp": 1234567890
}
```

## 🎯 Architecture Benefits

### vs. Traditional ESP32 Code
- **No Global Memory Conflicts**: Objects created after PSRAM initialization
- **Better Error Handling**: Comprehensive initialization checks
- **Modular Design**: Easy to extend and maintain
- **Memory Efficiency**: Smart pointer management
- **Scalability**: Component-based architecture

### Performance Improvements
- **Faster Startup**: Optimized initialization sequence
- **Lower Memory Fragmentation**: Controlled allocation patterns
- **Better Stability**: Elimination of global constructor issues
- **Enhanced Debugging**: Clear initialization flow

## 🛠️ Troubleshooting

### PSRAM Issues
- **Check Flash Mode**: Must be QIO, not DIO/DOUT/QOUT
- **Verify Hardware**: Ensure ESP32-S3 has OPI PSRAM
- **Power Supply**: Check for stable 3.3V supply

### Compilation Errors
- **Library Versions**: Ensure compatible library versions
- **Board Selection**: Verify ESP32S3 Dev Module is selected
- **build_opt.h**: Check for non-ASCII characters

### Runtime Issues
- **Serial Monitor**: Check for error messages at 115200 baud
- **Memory Usage**: Monitor heap and PSRAM consumption
- **WiFi Connection**: Verify AP mode activation

## 📈 Performance Metrics

### Memory Usage
- **Heap**: ~385KB free after initialization
- **PSRAM**: ~8MB available for application use
- **Flash**: ~3MB available for application code

### Timing
- **Initialization**: ~3 seconds total
- **PSRAM Detection**: ~500ms
- **Component Setup**: ~2 seconds
- **Web Server Start**: ~300ms

## 🔮 Future Enhancements

- **Signal Database**: Expanded IR protocol support
- **Scheduling**: Timer-based signal transmission
- **Mobile App**: Native iOS/Android application
- **Cloud Integration**: Remote control via internet
- **Voice Control**: Integration with voice assistants

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

---

**Built with ❤️ for the ESP32-S3 community**

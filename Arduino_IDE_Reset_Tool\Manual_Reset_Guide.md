# 手动重置Arduino IDE指南

## 🔧 如果批处理文件失败，请手动执行以下步骤

### 第一步：关闭Arduino IDE
1. 关闭所有Arduino IDE窗口
2. 打开任务管理器（Ctrl+Shift+Esc）
3. 结束所有arduino相关进程

### 第二步：删除缓存文件夹
请手动删除以下文件夹（如果存在）：

```
C:\Users\<USER>\AppData\Local\arduino
C:\Users\<USER>\AppData\Local\Arduino15
C:\Users\<USER>\AppData\Roaming\Arduino
C:\Users\<USER>\AppData\Roaming\arduino-ide
C:\Users\<USER>\.espressif
C:\Users\<USER>\.platformio
```

### 第三步：清理临时文件
删除以下位置的arduino相关文件：
```
C:\Users\<USER>\AppData\Local\Temp\arduino*
C:\Windows\Temp\arduino*
```

### 第四步：清理注册表（可选）
1. 按Win+R，输入regedit
2. 导航到：HKEY_CURRENT_USER\Software
3. 删除Arduino和arduino-ide项（如果存在）

### 第五步：重新启动Arduino IDE
1. 启动Arduino IDE
2. 应该看到全新的界面

### 第六步：重新安装ESP32板包
1. File → Preferences
2. Additional Board Manager URLs添加：
   ```
   https://espressif.github.io/arduino-esp32/package_esp32_index.json
   ```
3. Tools → Board → Boards Manager
4. 搜索"esp32"并安装

### 第七步：配置ESP32-S3
```
Board: ESP32S3 Dev Module
Flash Mode: QIO 80MHz
Flash Size: 16MB (128Mb)
PSRAM: OPI PSRAM
```

### 第八步：测试PSRAM
使用您的测试代码验证PSRAM功能。

## 🎯 预期结果
- Arduino IDE启动正常
- ESP32S3 Dev Module可选择
- Flash Mode有QIO选项
- PSRAM有OPI PSRAM选项
- 编译时显示--flash_mode qio
- PSRAM测试成功

/*
 * ESP32-S3 IR Control System - Arduino IDE Version
 * Hardware: ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
 * GPIO: IR_TX=21, IR_RX=14
 * 
 * Arduino IDE Configuration:
 * - Board: ESP32S3 Dev Module
 * - Flash Mode: QIO
 * - Flash Size: 16MB (128Mb)
 * - PSRAM: OPI PSRAM
 * - Flash Frequency: 80MHz
 * - Upload Speed: 460800
 * - Core Debug Level: None
 */

#include <WiFi.h>
#include <SPIFFS.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

// Include all manager headers
#include "wifi_manager.h"
#include "websocket_manager.h"
#include "data_manager.h"
#include "ir_controller.h"
#include "web_server_manager.h"
#include "task_manager.h"
#include "system_monitor.h"

// ==================== Global Objects - Delayed Initialization ====================
// Solution: Use pointers to avoid memory allocation conflicts during global construction

// Core Server Components - Delayed Initialization
AsyncWebServer* server = nullptr;
AsyncWebSocket* webSocket = nullptr;

// Manager Instances - Delayed Initialization
WiFiManager* wifiManager = nullptr;
WebSocketManager* wsManager = nullptr;
DataManager* dataManager = nullptr;
IRController* irController = nullptr;
WebServerManager* webServerManager = nullptr;
TaskManager* taskManager = nullptr;
SystemMonitor* systemMonitor = nullptr;

// System State
bool systemInitialized = false;
unsigned long systemStartTime = 0;

// ==================== Function Declarations ====================
void setupEventCallbacks();
void performPSRAMTest();
bool initializeGlobalObjects();
void cleanupGlobalObjects();

// ==================== Setup Function ====================

void setup() {
    // Initialize Serial Communication
    Serial.begin(115200);
    delay(3000);  // Delay to ensure serial port stability

    // Immediate PSRAM detection - Highest priority
    Serial.println();
    Serial.println("========================================");
    Serial.println("🚀 ESP32-S3 IR Control System Starting...");
    Serial.println("🔧 Arduino IDE Version with OPI PSRAM");
    Serial.println("⚡ Hardware: ESP32-S3-WROOM-1-N16R8");
    Serial.println("========================================");

    // Immediate PSRAM detection - Highest priority
    performPSRAMTest();

    Serial.println("========================================");
    Serial.println("Initializing global objects...");
    Serial.println("========================================");

    // Critical step: Initialize all global objects after PSRAM detection succeeds
    if (!initializeGlobalObjects()) {
        Serial.println("❌ ERROR: Failed to initialize global objects!");
        Serial.println("⚠️  System cannot continue - restarting in 5 seconds...");
        delay(5000);
        ESP.restart();
        return;
    }

    Serial.println("✅ All global objects initialized successfully");
    Serial.println("========================================");
    Serial.println("Continuing system initialization...");
    Serial.println("========================================");

    systemStartTime = millis();

    // Step 0: System Information
    Serial.println("Step 0: System Information");
    Serial.printf("Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);
    Serial.printf("Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    Serial.printf("SDK Version: %s\n", ESP.getSdkVersion());

    Serial.println("========================================");

    // Step 2: Initialize File System
    Serial.println("Step 2: Initializing SPIFFS...");
    if (!SPIFFS.begin(true)) {
        Serial.println("⚠️ SPIFFS initialization failed - continuing without file system");
    } else {
        Serial.println("✅ SPIFFS initialized successfully");
        Serial.printf("📁 Total space: %d bytes (%.2f KB)\n", SPIFFS.totalBytes(), SPIFFS.totalBytes() / 1024.0);
        Serial.printf("📁 Used space: %d bytes (%.2f KB)\n", SPIFFS.usedBytes(), SPIFFS.usedBytes() / 1024.0);
    }

    // TEMPORARILY DISABLED: All manager initialization
    /*
    // Step 3: Initialize Data Manager
    Serial.println("Step 3: Initializing Data Manager...");
    if (!dataManager->initialize()) {
        Serial.println("⚠️ Data Manager initialization failed - continuing");
    } else {
        Serial.println("✅ Data Manager initialized successfully");
    }
    */

    Serial.println("========================================");
    Serial.println("✅ PSRAM Test Only - Setup Complete!");
    Serial.println("System will now enter loop mode...");
    Serial.println("========================================");

    // Step 4: Initialize IR Controller
    Serial.println("Step 4: Initializing IR Controller...");
    irController->setDataManager(dataManager);
    if (!irController->initialize()) {
        Serial.println("❌ ERROR: IR Controller initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ IR Controller initialized successfully");
    }

    // Step 5: Initialize WiFi Manager
    Serial.println("Step 5: Initializing WiFi Manager...");
    if (!wifiManager->initialize()) {
        Serial.println("❌ ERROR: WiFi Manager initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ WiFi Manager initialized successfully");
    }

    // Step 6: Initialize WebSocket Manager
    Serial.println("Step 6: Initializing WebSocket Manager...");
    if (!wsManager->initialize(webSocket)) {
        Serial.println("❌ ERROR: WebSocket Manager initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ WebSocket Manager initialized successfully");
    }

    // Step 7: Initialize Task Manager
    Serial.println("Step 7: Initializing Task Manager...");
    taskManager->setDataManager(dataManager);
    taskManager->setIRController(irController);
    taskManager->setWebSocketManager(wsManager);
    if (!taskManager->initialize()) {
        Serial.println("❌ ERROR: Task Manager initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ Task Manager initialized successfully");
    }

    // Step 8: Initialize Web Server Manager
    Serial.println("Step 8: Initializing Web Server Manager...");
    webServerManager->setDataManager(dataManager);
    webServerManager->setIRController(irController);
    webServerManager->setTaskManager(taskManager);
    webServerManager->setWebSocketManager(wsManager);
    if (!webServerManager->initialize(server, webSocket)) {
        Serial.println("❌ ERROR: Web Server Manager initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ Web Server Manager initialized successfully");
    }

    // Step 9: Initialize System Monitor
    Serial.println("Step 9: Initializing System Monitor...");
    systemMonitor->setDataManager(dataManager);
    systemMonitor->setIRController(irController);
    systemMonitor->setTaskManager(taskManager);
    systemMonitor->setWebSocketManager(wsManager);
    if (!systemMonitor->initialize()) {
        Serial.println("❌ ERROR: System Monitor initialization failed!");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ System Monitor initialized successfully");
    }

    // Step 10: Setup Event Callbacks
    Serial.println("Step 10: Setting up event callbacks...");
    setupEventCallbacks();
    Serial.println("✅ Event callbacks configured");

    // Step 11: Start WiFi Connection
    Serial.println("Step 11: Starting WiFi connection...");
    wifiManager->startConnection();

    // Step 12: Start Web Server
    Serial.println("Step 12: Starting Web Server...");
    server->begin();
    Serial.println("✅ Web Server started on port 80");

    // Step 13: Start System Monitoring
    Serial.println("Step 13: Starting System Monitor...");
    systemMonitor->startMonitoring();
    Serial.println("✅ System Monitor started");

    // System Ready
    systemInitialized = true;
    unsigned long initTime = millis() - systemStartTime;

    Serial.println();
    Serial.println("🎉 SYSTEM INITIALIZATION COMPLETED! 🎉");
    Serial.printf("⏱️  Initialization time: %lu ms\n", initTime);
    Serial.println("📡 IR Transmitter: GPIO21");
    Serial.println("📡 IR Receiver: GPIO14");
    Serial.println("🌐 Web Server: http://192.168.4.1 (AP mode)");
    Serial.println("🔌 WebSocket: ws://192.168.4.1/ws");
    Serial.println("📊 Serial Monitor: 115200 baud");
    Serial.println();
    Serial.println("System is ready for operation!");
    Serial.println("========================================");

    // Broadcast system ready message
    if (systemInitialized) {
        DynamicJsonDocument readyMsg(256);
        readyMsg["message"] = "System initialization completed";
        readyMsg["uptime"] = initTime;
        readyMsg["timestamp"] = millis();
        wsManager->broadcastMessage("system_ready", readyMsg);
    }
}

// ==================== Main Loop ====================

void loop() {
    // Only run main loop if system is initialized
    if (!systemInitialized) {
        delay(100);
        return;
    }

    // Handle all manager loops - Use pointer access
    if (wifiManager) wifiManager->handleLoop();
    if (wsManager) wsManager->handleLoop();
    if (irController) irController->handleLoop();
    if (taskManager) taskManager->handleLoop();
    if (systemMonitor) systemMonitor->handleLoop();

    // Small delay to prevent watchdog issues
    delay(1);
}

// ==================== PSRAM Test Function ====================

void performPSRAMTest() {
    Serial.println();
    Serial.println("🔬 PSRAM DETECTION AND TESTING");
    Serial.println("=============================================");

    // First check if boot mode is correct
    Serial.println("🔍 Checking ESP32-S3 boot configuration...");
    Serial.printf("📋 Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("📋 Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("📋 CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("📋 Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);

    // Check Flash mode - This is critical diagnostic information
    uint32_t flash_mode = ESP.getFlashChipMode();
    Serial.printf("📋 Flash Mode: %s\n",
        flash_mode == 0 ? "QIO" :
        flash_mode == 1 ? "QOUT" :
        flash_mode == 2 ? "DIO" :
        flash_mode == 3 ? "DOUT" : "UNKNOWN");

    if (flash_mode != 0) {
        Serial.println();
        Serial.println("⚠️  WARNING: Flash Mode is not QIO!");
        Serial.println("🔧 This may prevent OPI PSRAM from working correctly.");
        Serial.println("📖 Please check ARDUINO_IDE_CONFIG.md for correct settings.");
        Serial.println();
    }

    Serial.println("🔍 Checking PSRAM availability...");

    if (!psramFound()) {
        Serial.println();
        Serial.println("❌ PSRAM NOT FOUND!");
        Serial.println("⚠️  Root cause analysis:");
        Serial.printf("   - Current Flash Mode: %s (should be QIO)\n",
            flash_mode == 0 ? "QIO ✅" :
            flash_mode == 1 ? "QOUT ❌" :
            flash_mode == 2 ? "DIO ❌" :
            flash_mode == 3 ? "DOUT ❌" : "UNKNOWN ❌");
        Serial.println("   - Required: Board = ESP32S3 Dev Module");
        Serial.println("   - Required: PSRAM = OPI PSRAM");
        Serial.println("   - Required: Flash Mode = QIO");
        Serial.println("   - Required: Flash Size = 16MB");
        Serial.println();
        Serial.println("📖 See ARDUINO_IDE_CONFIG.md for detailed configuration guide");
        Serial.println("System will continue but with limited memory");
        Serial.println("=============================================");
        return;
    }

    Serial.println();
    Serial.println("🎉 PSRAM DETECTION SUCCESSFUL!");
    Serial.println("✅ PSRAM is available!");
    Serial.printf("📊 Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("🧠 Free: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    Serial.println("🚀 PSRAM ready for use!");
    
    // Test 1: Basic allocation test
    Serial.println("\n🧪 Test 1: Basic Allocation Test");
    void* ptr1 = ps_malloc(1024);
    if (ptr1) {
        Serial.printf("✅ 1KB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr1);
        free(ptr1);
    } else {
        Serial.println("❌ 1KB allocation: FAILED");
    }
    
    // Test 2: Large allocation test
    Serial.println("\n🧪 Test 2: Large Allocation Test");
    size_t largeSize = 1024 * 1024; // 1MB
    void* ptr2 = ps_malloc(largeSize);
    if (ptr2) {
        Serial.printf("✅ 1MB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr2);
        free(ptr2);
    } else {
        Serial.println("❌ 1MB allocation: FAILED");
    }
    
    // Test 3: Read/Write integrity test
    Serial.println("\n🧪 Test 3: Read/Write Integrity Test");
    size_t testSize = 256 * 1024; // 256KB
    uint8_t* testBuffer = (uint8_t*)ps_malloc(testSize);
    if (testBuffer) {
        // Write test pattern
        for (size_t i = 0; i < testSize; i++) {
            testBuffer[i] = (uint8_t)(i % 256);
        }
        
        // Verify test pattern
        bool integrity = true;
        for (size_t i = 0; i < testSize; i++) {
            if (testBuffer[i] != (uint8_t)(i % 256)) {
                integrity = false;
                break;
            }
        }
        
        if (integrity) {
            Serial.println("✅ Read/Write integrity: SUCCESS");
        } else {
            Serial.println("❌ Read/Write integrity: FAILED");
        }
        
        free(testBuffer);
    } else {
        Serial.println("❌ Read/Write test: Allocation failed");
    }
    
    Serial.println();
    Serial.println("🏁 PSRAM TESTING COMPLETED SUCCESSFULLY!");
    Serial.println("✅ All PSRAM tests passed - Memory ready for use");
    Serial.println("=============================================");
}

// ==================== Event Callback Setup ====================

void setupEventCallbacks() {
    // WiFi Manager Callbacks
    wifiManager->setOnConnected([]() {
        Serial.println("📶 WiFi Connected!");
        Serial.printf("🌐 IP Address: %s\n", WiFi.localIP().toString().c_str());

        DynamicJsonDocument wifiMsg(256);
        wifiMsg["status"] = "connected";
        wifiMsg["ip"] = WiFi.localIP().toString();
        wifiMsg["rssi"] = WiFi.RSSI();
        wsManager->broadcastMessage("wifi_status", wifiMsg);
    });

    wifiManager->setOnDisconnected([]() {
        Serial.println("📶 WiFi Disconnected!");

        DynamicJsonDocument wifiMsg(256);
        wifiMsg["status"] = "disconnected";
        wsManager->broadcastMessage("wifi_status", wifiMsg);
    });

    wifiManager->setOnAPStarted([]() {
        Serial.println("📡 AP Mode Started!");
        Serial.printf("🌐 AP IP: %s\n", WiFi.softAPIP().toString().c_str());

        DynamicJsonDocument apMsg(256);
        apMsg["status"] = "ap_started";
        apMsg["ip"] = WiFi.softAPIP().toString();
        wsManager->broadcastMessage("ap_status", apMsg);
    });

    // IR Controller Callbacks
    irController->setOnSignalLearned([](const DynamicJsonDocument& signal) {
        Serial.println("📡 Signal learned successfully!");
        wsManager->broadcastMessage("signal_learned", signal);
    });

    irController->setOnSignalSent([](const String& signalId, bool success) {
        Serial.printf("📡 Signal sent: %s - %s\n", signalId.c_str(), success ? "SUCCESS" : "FAILED");

        DynamicJsonDocument sendMsg(256);
        sendMsg["signal_id"] = signalId;
        sendMsg["success"] = success;
        sendMsg["timestamp"] = millis();
        wsManager->broadcastMessage("signal_sent", sendMsg);
    });

    // Task Manager Callbacks
    taskManager->setOnTaskCompleted([](const String& taskId, bool success) {
        Serial.printf("📋 Task completed: %s - %s\n", taskId.c_str(), success ? "SUCCESS" : "FAILED");

        DynamicJsonDocument taskMsg(256);
        taskMsg["task_id"] = taskId;
        taskMsg["success"] = success;
        taskMsg["timestamp"] = millis();
        wsManager->broadcastMessage("task_completed", taskMsg);
    });

    // System Monitor Callbacks
    systemMonitor->setOnHealthChanged([](SystemHealth health, const String& reason) {
        Serial.printf("🏥 System health changed: %s - %s\n",
                     systemMonitor->getHealthString(health).c_str(), reason.c_str());

        DynamicJsonDocument healthMsg(256);
        healthMsg["health"] = static_cast<int>(health);
        healthMsg["health_text"] = systemMonitor->getHealthString(health);
        healthMsg["reason"] = reason;
        healthMsg["timestamp"] = millis();
        wsManager->broadcastMessage("health_changed", healthMsg);
    });

    systemMonitor->setOnAlert([](const String& alert) {
        Serial.printf("⚠️  System Alert: %s\n", alert.c_str());
        wsManager->sendNotification(alert, "warning");
    });
}

// ==================== Global Objects Initialization ====================

bool initializeGlobalObjects() {
    Serial.println("🚀 Initializing global objects after PSRAM detection...");

    try {
        // Initialize Core Server Components
        Serial.println("📡 Creating AsyncWebServer and AsyncWebSocket...");
        server = new AsyncWebServer(80);
        if (!server) {
            Serial.println("❌ Failed to create AsyncWebServer");
            return false;
        }

        webSocket = new AsyncWebSocket("/ws");
        if (!webSocket) {
            Serial.println("❌ Failed to create AsyncWebSocket");
            delete server;
            server = nullptr;
            return false;
        }

        // Initialize Manager Instances
        Serial.println("📋 Creating Manager instances...");

        wifiManager = new WiFiManager();
        if (!wifiManager) {
            Serial.println("❌ Failed to create WiFiManager");
            cleanupGlobalObjects();
            return false;
        }

        wsManager = new WebSocketManager();
        if (!wsManager) {
            Serial.println("❌ Failed to create WebSocketManager");
            cleanupGlobalObjects();
            return false;
        }

        dataManager = new DataManager();
        if (!dataManager) {
            Serial.println("❌ Failed to create DataManager");
            cleanupGlobalObjects();
            return false;
        }

        irController = new IRController();
        if (!irController) {
            Serial.println("❌ Failed to create IRController");
            cleanupGlobalObjects();
            return false;
        }

        webServerManager = new WebServerManager();
        if (!webServerManager) {
            Serial.println("❌ Failed to create WebServerManager");
            cleanupGlobalObjects();
            return false;
        }

        taskManager = new TaskManager();
        if (!taskManager) {
            Serial.println("❌ Failed to create TaskManager");
            cleanupGlobalObjects();
            return false;
        }

        systemMonitor = new SystemMonitor();
        if (!systemMonitor) {
            Serial.println("❌ Failed to create SystemMonitor");
            cleanupGlobalObjects();
            return false;
        }

        Serial.println("✅ All global objects created successfully!");
        Serial.printf("🧠 Free Heap after object creation: %d bytes (%.2f KB)\n",
                     ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
        if (psramFound()) {
            Serial.printf("🧠 Free PSRAM after object creation: %d bytes (%.2f KB)\n",
                         ESP.getFreePsram(), ESP.getFreePsram() / 1024.0);
        }

        return true;

    } catch (const std::exception& e) {
        Serial.printf("❌ Exception during global object initialization: %s\n", e.what());
        cleanupGlobalObjects();
        return false;
    } catch (...) {
        Serial.println("❌ Unknown exception during global object initialization");
        cleanupGlobalObjects();
        return false;
    }
}

void cleanupGlobalObjects() {
    Serial.println("🧹 Cleaning up global objects...");

    if (systemMonitor) { delete systemMonitor; systemMonitor = nullptr; }
    if (taskManager) { delete taskManager; taskManager = nullptr; }
    if (webServerManager) { delete webServerManager; webServerManager = nullptr; }
    if (irController) { delete irController; irController = nullptr; }
    if (dataManager) { delete dataManager; dataManager = nullptr; }
    if (wsManager) { delete wsManager; wsManager = nullptr; }
    if (wifiManager) { delete wifiManager; wifiManager = nullptr; }
    if (webSocket) { delete webSocket; webSocket = nullptr; }
    if (server) { delete server; server = nullptr; }

    Serial.println("✅ Global objects cleanup completed");
}

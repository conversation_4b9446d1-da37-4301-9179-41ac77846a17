#ifndef MEMORY_ALLOCATOR_H
#define MEMORY_ALLOCATOR_H

#include <Arduino.h>
#include "PSRAMManager.h"
#include "system-config.h"

/**
 * 智能内存分配器类
 * 
 * 根据系统模式和内存大小智能选择使用PSRAM或普通RAM
 * 提供统一的内存分配接口，隐藏底层实现细节
 * 
 * 核心功能：
 * - 智能内存分配策略
 * - 内存使用统计
 * - 内存泄漏检测
 * - 内存池管理
 * - 自动内存清理
 */
class MemoryAllocator {
public:
    // ==================== 内存分配接口 ====================
    
    /**
     * 智能内存分配
     * @param size 分配大小
     * @return void* 分配的内存指针，失败返回nullptr
     */
    static void* smartAlloc(size_t size);
    
    /**
     * 智能内存释放
     * @param ptr 内存指针
     */
    static void smartFree(void* ptr);
    
    /**
     * 智能内存重新分配
     * @param ptr 原内存指针
     * @param newSize 新大小
     * @return void* 新内存指针
     */
    static void* smartRealloc(void* ptr, size_t newSize);
    
    /**
     * 分配并清零内存
     * @param count 元素数量
     * @param size 单个元素大小
     * @return void* 分配的内存指针
     */
    static void* smartCalloc(size_t count, size_t size);
    
    // ==================== 专用分配器 ====================
    
    /**
     * 强制使用PSRAM分配
     * @param size 分配大小
     * @return void* 分配的内存指针
     */
    static void* allocFromPSRAM(size_t size);
    
    /**
     * 强制使用普通RAM分配
     * @param size 分配大小
     * @return void* 分配的内存指针
     */
    static void* allocFromHeap(size_t size);
    
    /**
     * 分配DMA兼容内存
     * @param size 分配大小
     * @return void* 分配的内存指针
     */
    static void* allocDMAMemory(size_t size);
    
    // ==================== 内存池管理 ====================
    
    /**
     * 初始化内存池
     * @return bool 初始化是否成功
     */
    static bool initializeMemoryPools();
    
    /**
     * 初始化内存分配器
     * @return bool 初始化是否成功
     */
    static bool initialize();
    
    /**
     * 从内存池分配
     * @param size 分配大小
     * @return void* 分配的内存指针
     */
    static void* allocFromPool(size_t size);
    
    /**
     * 释放到内存池
     * @param ptr 内存指针
     * @param size 内存大小
     */
    static void freeToPool(void* ptr, size_t size);
    
    /**
     * 清理内存池
     */
    static void cleanupMemoryPools();
    
    // ==================== 内存统计 ====================
    
    /**
     * 获取总分配次数
     * @return size_t 分配次数
     */
    static size_t getTotalAllocations();
    
    /**
     * 获取总释放次数
     * @return size_t 释放次数
     */
    static size_t getTotalDeallocations();
    
    /**
     * 获取当前分配的内存大小
     * @return size_t 当前分配大小
     */
    static size_t getCurrentAllocatedSize();
    
    /**
     * 获取峰值内存使用量
     * @return size_t 峰值使用量
     */
    static size_t getPeakMemoryUsage();
    
    /**
     * 获取PSRAM使用统计
     * @return size_t PSRAM使用量
     */
    static size_t getPSRAMUsage();
    
    /**
     * 获取普通RAM使用统计
     * @return size_t 普通RAM使用量
     */
    static size_t getHeapUsage();
    
    // ==================== 内存健康检查 ====================
    
    /**
     * 检查内存泄漏
     * @return bool 是否存在内存泄漏
     */
    static bool checkMemoryLeaks();
    
    /**
     * 获取内存碎片化程度
     * @return float 碎片化程度 (0.0-1.0)
     */
    static float getFragmentationLevel();
    
    /**
     * 执行内存完整性检查
     * @return bool 内存完整性是否正常
     */
    static bool checkMemoryIntegrity();
    
    /**
     * 获取可用内存大小
     * @return size_t 可用内存大小
     */
    static size_t getAvailableMemory();
    
    // ==================== 内存清理 ====================
    
    /**
     * 执行内存清理
     */
    static void performCleanup();
    
    /**
     * 强制垃圾回收
     */
    static void forceGarbageCollection();
    
    /**
     * 整理内存碎片
     */
    static void defragmentMemory();
    
    // ==================== 调试功能 ====================
    
    /**
     * 打印内存使用统计
     */
    static void printMemoryStats();
    
    /**
     * 打印内存分配详情
     */
    static void printAllocationDetails();
    
    /**
     * 导出内存使用报告
     * @return String 内存使用报告
     */
    static String generateMemoryReport();
    
#ifdef DEBUG_MODE
    /**
     * 启用内存调试模式
     */
    static void enableDebugMode();
    
    /**
     * 禁用内存调试模式
     */
    static void disableDebugMode();
    
    /**
     * 打印内存分配跟踪
     */
    static void printAllocationTrace();
#endif

private:
    // ==================== 内存策略 ====================
    
    /**
     * 判断是否应该使用PSRAM
     * @param size 分配大小
     * @return bool 是否使用PSRAM
     */
    static bool shouldUsePSRAM(size_t size);
    
    /**
     * 获取最佳分配策略
     * @param size 分配大小
     * @return int 分配策略 (0=Heap, 1=PSRAM, 2=Pool)
     */
    static int getBestAllocationStrategy(size_t size);
    
    /**
     * 检查内存压力
     * @return bool 是否存在内存压力
     */
    static bool isMemoryUnderPressure();
    
    // ==================== 内存跟踪 ====================
    
    struct AllocationInfo {
        void* ptr;
        size_t size;
        bool isPSRAM;
        unsigned long timestamp;
        const char* file;
        int line;
    };
    
    /**
     * 记录内存分配
     * @param ptr 内存指针
     * @param size 分配大小
     * @param isPSRAM 是否为PSRAM
     */
    static void trackAllocation(void* ptr, size_t size, bool isPSRAM);
    
    /**
     * 记录内存释放
     * @param ptr 内存指针
     */
    static void trackDeallocation(void* ptr);
    
    /**
     * 查找分配信息
     * @param ptr 内存指针
     * @return AllocationInfo* 分配信息
     */
    static AllocationInfo* findAllocationInfo(void* ptr);
    
    // ==================== 内存池结构 ====================
    
    struct MemoryPool {
        void** freeBlocks;
        size_t blockSize;
        size_t totalBlocks;
        size_t freeBlockCount;
        bool isPSRAM;
    };
    
    static MemoryPool* s_memoryPools;
    static size_t s_poolCount;
    
    // ==================== 统计数据 ====================
    
    static size_t s_totalAllocations;
    static size_t s_totalDeallocations;
    static size_t s_currentAllocatedSize;
    static size_t s_peakMemoryUsage;
    static size_t s_psramUsage;
    static size_t s_heapUsage;
    
    // ==================== 配置参数 ====================
    
    static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
    static const size_t SMALL_ALLOCATION_THRESHOLD = 256;
    static const size_t LARGE_ALLOCATION_THRESHOLD = 4096;
    
    // ==================== 调试模式 ====================
    
#ifdef DEBUG_MODE
    static bool s_debugMode;
    // 简化版本，不使用std::vector
    static AllocationInfo* s_allocations;
    static size_t s_allocationCount;
    static size_t s_maxAllocations;
#endif
};

// ==================== 便利宏定义 ====================

#define SMART_ALLOC(size) MemoryAllocator::smartAlloc(size)
#define SMART_FREE(ptr) MemoryAllocator::smartFree(ptr)
#define SMART_REALLOC(ptr, size) MemoryAllocator::smartRealloc(ptr, size)
#define SMART_CALLOC(count, size) MemoryAllocator::smartCalloc(count, size)

// 调试模式下的内存分配宏
#ifdef DEBUG_MODE
    #define DEBUG_ALLOC(size) MemoryAllocator::smartAlloc(size)
    #define DEBUG_FREE(ptr) do { \
        MemoryAllocator::smartFree(ptr); \
        ptr = nullptr; \
    } while(0)
#else
    #define DEBUG_ALLOC(size) MemoryAllocator::smartAlloc(size)
    #define DEBUG_FREE(ptr) MemoryAllocator::smartFree(ptr)
#endif

#endif // MEMORY_ALLOCATOR_H

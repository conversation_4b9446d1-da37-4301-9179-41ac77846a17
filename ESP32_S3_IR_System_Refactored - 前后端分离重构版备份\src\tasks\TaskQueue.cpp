#include "TaskQueue.h"

// ==================== 构造函数和析构函数 ====================
TaskQueue::TaskQueue(size_t maxSize)
    : m_maxSize(maxSize)
    , m_totalEnqueued(0)
    , m_totalDequeued(0)
    , m_totalScheduled(0)
    , m_totalPromoted(0)
    , m_totalRemoved(0)
{
    Serial.printf("📋 TaskQueue created with max size: %d\n", maxSize);
}

TaskQueue::~TaskQueue() {
    clear();
    Serial.println("📋 TaskQueue destroyed");
}

// ==================== 队列操作 ====================
bool TaskQueue::enqueue(const String& taskId, TaskPriority priority) {
    if (!isValidTaskId(taskId)) {
        Serial.printf("❌ Invalid task ID: %s\n", taskId.c_str());
        return false;
    }
    
    if (isFull()) {
        Serial.printf("❌ Queue is full, cannot enqueue task: %s\n", taskId.c_str());
        return false;
    }
    
    if (contains(taskId)) {
        Serial.printf("⚠️  Task already in queue: %s\n", taskId.c_str());
        return false;
    }
    
    QueuedTask task(taskId, priority);
    m_queue.push(task);
    
    updateStatistics("enqueue");
    
    Serial.printf("📋 Task enqueued: %s (priority: %s, queue size: %d)\n", 
                 taskId.c_str(), getPriorityString(priority).c_str(), size());
    
    return true;
}

bool TaskQueue::enqueueScheduled(const String& taskId, unsigned long scheduleTime, TaskPriority priority) {
    if (!isValidTaskId(taskId)) {
        Serial.printf("❌ Invalid task ID: %s\n", taskId.c_str());
        return false;
    }
    
    // 检查是否已经在定时任务列表中
    for (const auto& task : m_scheduledTasks) {
        if (task.taskId == taskId) {
            Serial.printf("⚠️  Scheduled task already exists: %s\n", taskId.c_str());
            return false;
        }
    }
    
    QueuedTask task(taskId, priority);
    task.scheduleTime = scheduleTime;
    task.isScheduled = true;
    
    m_scheduledTasks.push_back(task);
    
    updateStatistics("schedule");
    
    Serial.printf("📋 Task scheduled: %s (time: %lu, priority: %s)\n", 
                 taskId.c_str(), scheduleTime, getPriorityString(priority).c_str());
    
    return true;
}

QueuedTask TaskQueue::dequeue() {
    if (isEmpty()) {
        return QueuedTask();  // 返回空任务
    }
    
    QueuedTask task = m_queue.top();
    m_queue.pop();
    
    updateStatistics("dequeue");
    
    Serial.printf("📋 Task dequeued: %s (priority: %s, queue size: %d)\n", 
                 task.taskId.c_str(), getPriorityString(task.priority).c_str(), size());
    
    return task;
}

QueuedTask TaskQueue::peek() const {
    if (isEmpty()) {
        return QueuedTask();  // 返回空任务
    }
    
    return m_queue.top();
}

bool TaskQueue::isEmpty() const {
    return m_queue.empty();
}

bool TaskQueue::isFull() const {
    return size() >= m_maxSize;
}

size_t TaskQueue::size() const {
    return m_queue.size();
}

void TaskQueue::clear() {
    // 清空主队列
    while (!m_queue.empty()) {
        m_queue.pop();
    }
    
    // 清空定时任务列表
    m_scheduledTasks.clear();
    
    Serial.println("📋 Task queue cleared");
}

// ==================== 任务查找和管理 ====================
bool TaskQueue::contains(const String& taskId) const {
    // 创建队列副本来检查
    auto queueCopy = m_queue;
    
    while (!queueCopy.empty()) {
        if (queueCopy.top().taskId == taskId) {
            return true;
        }
        queueCopy.pop();
    }
    
    // 检查定时任务列表
    for (const auto& task : m_scheduledTasks) {
        if (task.taskId == taskId) {
            return true;
        }
    }
    
    return false;
}

bool TaskQueue::remove(const String& taskId) {
    // 从主队列中移除（需要重建队列）
    std::vector<QueuedTask> tempTasks;
    bool found = false;
    
    while (!m_queue.empty()) {
        QueuedTask task = m_queue.top();
        m_queue.pop();
        
        if (task.taskId == taskId) {
            found = true;
        } else {
            tempTasks.push_back(task);
        }
    }
    
    // 重建队列
    for (const auto& task : tempTasks) {
        m_queue.push(task);
    }
    
    // 从定时任务列表中移除
    auto it = m_scheduledTasks.begin();
    while (it != m_scheduledTasks.end()) {
        if (it->taskId == taskId) {
            it = m_scheduledTasks.erase(it);
            found = true;
        } else {
            ++it;
        }
    }
    
    if (found) {
        updateStatistics("remove");
        Serial.printf("📋 Task removed from queue: %s\n", taskId.c_str());
    }
    
    return found;
}

// ==================== 定时任务管理 ====================
bool TaskQueue::hasReadyScheduledTasks() const {
    unsigned long currentTime = millis();
    
    for (const auto& task : m_scheduledTasks) {
        if (currentTime >= task.scheduleTime) {
            return true;
        }
    }
    
    return false;
}

std::vector<QueuedTask> TaskQueue::getReadyScheduledTasks() {
    std::vector<QueuedTask> readyTasks;
    unsigned long currentTime = millis();
    
    for (const auto& task : m_scheduledTasks) {
        if (currentTime >= task.scheduleTime) {
            readyTasks.push_back(task);
        }
    }
    
    return readyTasks;
}

int TaskQueue::promoteScheduledTasks() {
    std::vector<QueuedTask> readyTasks = getReadyScheduledTasks();
    int promotedCount = 0;
    
    for (const auto& task : readyTasks) {
        if (!isFull() && enqueue(task.taskId, task.priority)) {
            // 从定时任务列表中移除
            auto it = m_scheduledTasks.begin();
            while (it != m_scheduledTasks.end()) {
                if (it->taskId == task.taskId) {
                    it = m_scheduledTasks.erase(it);
                    promotedCount++;
                    break;
                } else {
                    ++it;
                }
            }
        }
    }
    
    if (promotedCount > 0) {
        updateStatistics("promote");
        Serial.printf("📋 Promoted %d scheduled tasks to main queue\n", promotedCount);
    }
    
    return promotedCount;
}

// ==================== 队列状态和统计 ====================
DynamicJsonDocument TaskQueue::getQueueStatus() const {
    DynamicJsonDocument status(512);
    
    status["size"] = size();
    status["max_size"] = m_maxSize;
    status["is_empty"] = isEmpty();
    status["is_full"] = isFull();
    status["scheduled_tasks"] = m_scheduledTasks.size();
    status["has_ready_scheduled"] = hasReadyScheduledTasks();
    
    // 下一个任务信息
    if (!isEmpty()) {
        QueuedTask nextTask = peek();
        status["next_task"]["id"] = nextTask.taskId;
        status["next_task"]["priority"] = getPriorityString(nextTask.priority);
        status["next_task"]["queue_time"] = nextTask.queueTime;
    }
    
    return status;
}

DynamicJsonDocument TaskQueue::getStatistics() const {
    DynamicJsonDocument stats(512);
    
    stats["total_enqueued"] = m_totalEnqueued;
    stats["total_dequeued"] = m_totalDequeued;
    stats["total_scheduled"] = m_totalScheduled;
    stats["total_promoted"] = m_totalPromoted;
    stats["total_removed"] = m_totalRemoved;
    stats["current_queue_size"] = size();
    stats["current_scheduled_size"] = m_scheduledTasks.size();
    
    return stats;
}

void TaskQueue::resetStatistics() {
    m_totalEnqueued = 0;
    m_totalDequeued = 0;
    m_totalScheduled = 0;
    m_totalPromoted = 0;
    m_totalRemoved = 0;
    
    Serial.println("📋 Task queue statistics reset");
}

DynamicJsonDocument TaskQueue::getPriorityDistribution() const {
    DynamicJsonDocument distribution(256);
    
    int lowCount = 0, normalCount = 0, highCount = 0, urgentCount = 0;
    
    // 统计主队列
    auto queueCopy = m_queue;
    while (!queueCopy.empty()) {
        TaskPriority priority = queueCopy.top().priority;
        queueCopy.pop();
        
        switch (priority) {
            case TaskPriority::LOW: lowCount++; break;
            case TaskPriority::NORMAL: normalCount++; break;
            case TaskPriority::HIGH: highCount++; break;
            case TaskPriority::URGENT: urgentCount++; break;
        }
    }
    
    // 统计定时任务
    for (const auto& task : m_scheduledTasks) {
        switch (task.priority) {
            case TaskPriority::LOW: lowCount++; break;
            case TaskPriority::NORMAL: normalCount++; break;
            case TaskPriority::HIGH: highCount++; break;
            case TaskPriority::URGENT: urgentCount++; break;
        }
    }
    
    distribution["low"] = lowCount;
    distribution["normal"] = normalCount;
    distribution["high"] = highCount;
    distribution["urgent"] = urgentCount;
    
    return distribution;
}

// ==================== 私有方法 ====================
bool TaskQueue::isValidTaskId(const String& taskId) const {
    return !taskId.isEmpty() && taskId.length() > 0;
}

void TaskQueue::updateStatistics(const String& operation) {
    if (operation == "enqueue") {
        m_totalEnqueued++;
    } else if (operation == "dequeue") {
        m_totalDequeued++;
    } else if (operation == "schedule") {
        m_totalScheduled++;
    } else if (operation == "promote") {
        m_totalPromoted++;
    } else if (operation == "remove") {
        m_totalRemoved++;
    }
}

String TaskQueue::getPriorityString(TaskPriority priority) const {
    switch (priority) {
        case TaskPriority::LOW: return "LOW";
        case TaskPriority::NORMAL: return "NORMAL";
        case TaskPriority::HIGH: return "HIGH";
        case TaskPriority::URGENT: return "URGENT";
        default: return "UNKNOWN";
    }
}

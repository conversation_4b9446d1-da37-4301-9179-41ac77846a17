#ifndef WS_MANAGER_H
#define WS_MANAGER_H

#include <Arduino.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "../../config/system-config.h"
#include "../../config/network-config.h"

/**
 * WebSocket管理器类
 * 
 * 负责WebSocket连接的管理和消息处理
 * 
 * 核心功能：
 * - WebSocket连接管理
 * - 实时消息推送
 * - 客户端状态监控
 * - 广播消息
 */
class WSManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param maxConnections 最大连接数
     */
    explicit WSManager(int maxConnections = MAX_WEBSOCKET_CLIENTS);
    
    /**
     * 析构函数
     */
    ~WSManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化WebSocket管理器
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭WebSocket管理器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 主循环处理
     */
    void handleLoop();
    
    // ==================== 连接管理 ====================
    
    /**
     * 获取WebSocket服务器实例
     * @return AsyncWebSocket* WebSocket服务器指针
     */
    AsyncWebSocket* getWebSocket() { return m_webSocket; }
    
    /**
     * 获取连接数量
     * @return int 当前连接数
     */
    int getConnectionCount() const;
    
    /**
     * 获取连接状态
     * @return DynamicJsonDocument 连接状态信息
     */
    DynamicJsonDocument getConnectionStatus();
    
    /**
     * 断开所有连接
     */
    void disconnectAll();
    
    /**
     * 断开指定客户端
     * @param clientId 客户端ID
     */
    void disconnectClient(uint32_t clientId);
    
    // ==================== 消息发送 ====================
    
    /**
     * 广播消息给所有客户端
     * @param message 消息内容
     */
    void broadcastMessage(const String& message);
    
    /**
     * 广播JSON消息给所有客户端
     * @param jsonMessage JSON消息
     */
    void broadcastJSON(const DynamicJsonDocument& jsonMessage);
    
    /**
     * 发送消息给指定客户端
     * @param clientId 客户端ID
     * @param message 消息内容
     */
    void sendToClient(uint32_t clientId, const String& message);
    
    /**
     * 发送JSON消息给指定客户端
     * @param clientId 客户端ID
     * @param jsonMessage JSON消息
     */
    void sendJSONToClient(uint32_t clientId, const DynamicJsonDocument& jsonMessage);
    
    // ==================== 系统事件通知 ====================
    
    /**
     * 通知任务开始
     * @param taskData 任务数据
     */
    void notifyTaskStarted(const DynamicJsonDocument& taskData);
    
    /**
     * 通知任务进度
     * @param progressData 进度数据
     */
    void notifyTaskProgress(const DynamicJsonDocument& progressData);
    
    /**
     * 通知任务完成
     * @param resultData 结果数据
     */
    void notifyTaskCompleted(const DynamicJsonDocument& resultData);
    
    /**
     * 通知任务暂停
     * @param pauseData 暂停数据
     */
    void notifyTaskPaused(const DynamicJsonDocument& pauseData);
    
    /**
     * 通知信号发送
     * @param signalData 信号数据
     */
    void notifySignalSending(const DynamicJsonDocument& signalData);
    
    /**
     * 通知信号学习
     * @param learnData 学习数据
     */
    void notifySignalLearned(const DynamicJsonDocument& learnData);
    
    /**
     * 通知学习状态变化
     * @param statusData 状态数据
     */
    void notifyLearningStatusChanged(const DynamicJsonDocument& statusData);
    
    /**
     * 通知系统状态变化
     * @param statusData 状态数据
     */
    void notifySystemStatusChanged(const DynamicJsonDocument& statusData);
    
    /**
     * 通知系统错误
     * @param errorData 错误数据
     */
    void notifySystemError(const DynamicJsonDocument& errorData);
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取WebSocket统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getStatistics();
    
    /**
     * 重置统计信息
     */
    void resetStatistics();

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                  // 是否已初始化
    int m_maxConnections;                // 最大连接数
    AsyncWebSocket* m_webSocket;         // WebSocket服务器实例
    
    // 统计信息
    unsigned long m_totalConnections;    // 总连接数
    unsigned long m_totalMessages;       // 总消息数
    unsigned long m_totalBroadcasts;     // 总广播数
    unsigned long m_connectionErrors;    // 连接错误数
    
    // ==================== 私有方法 ====================
    
    /**
     * 设置事件处理器
     */
    void setupEventHandlers();
    
    /**
     * 处理WebSocket事件
     * @param server WebSocket服务器
     * @param client 客户端
     * @param type 事件类型
     * @param arg 参数
     * @param data 数据
     * @param len 数据长度
     */
    void handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                             AwsEventType type, void* arg, uint8_t* data, size_t len);
    
    /**
     * 处理客户端连接
     * @param client 客户端
     */
    void handleClientConnect(AsyncWebSocketClient* client);
    
    /**
     * 处理客户端断开
     * @param client 客户端
     */
    void handleClientDisconnect(AsyncWebSocketClient* client);
    
    /**
     * 处理客户端消息
     * @param client 客户端
     * @param data 消息数据
     * @param len 数据长度
     */
    void handleClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    
    /**
     * 处理客户端错误
     * @param client 客户端
     */
    void handleClientError(AsyncWebSocketClient* client);
    
    /**
     * 创建系统消息
     * @param type 消息类型
     * @param payload 消息载荷
     * @return DynamicJsonDocument 系统消息
     */
    DynamicJsonDocument createSystemMessage(const String& type, const DynamicJsonDocument& payload);
    
    /**
     * 验证客户端
     * @param client 客户端
     * @return bool 验证是否通过
     */
    bool validateClient(AsyncWebSocketClient* client);
    
    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);
    
    /**
     * 更新统计信息
     * @param type 统计类型
     */
    void updateStatistics(const String& type);

    // ==================== WebSocket消息处理方法 ====================

    /**
     * 处理ping消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理订阅消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理取消订阅消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleUnsubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理获取状态消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理发送信号消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleSendSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理开始学习消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleStartLearningMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);

    /**
     * 处理停止学习消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleStopLearningMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
};

#endif // WS_MANAGER_H

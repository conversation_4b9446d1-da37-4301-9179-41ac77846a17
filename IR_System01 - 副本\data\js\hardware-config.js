/**
 * R2智能红外控制系统 - 硬件配置管理
 * 管理模拟/真实硬件模式的切换
 */

class HardwareConfigManager {
  constructor() {
    this.config = {
      // 硬件模式配置
      useRealHardware: true,  // ✅ 默认使用真实ESP8266硬件
      
      // ESP8266设备配置
      esp8266: {
        enabled: true,
        host: "*************",  // 需要根据实际IP调整
        port: 80,
        timeout: 30000,
        retryCount: 3
      },
      
      // 模拟模式配置（用于开发/演示）
      simulation: {
        enabled: false,  // 模拟模式默认关闭
        showUI: true,
        logActions: false,
        signalDetectionDelay: 2000,  // 模拟信号检测延迟
        emitDelay: 100,              // 模拟发射延迟
        successRate: 0.99            // 模拟成功率
      },
      
      // 调试配置
      debug: {
        logHardwareEvents: true,
        logAPIRequests: true,
        showConnectionStatus: true
      }
    };
    
    // 从localStorage加载配置
    this.loadConfig();
    
    // 初始化事件监听
    this.initEventListeners();
  }
  
  /**
   * 加载配置
   */
  loadConfig() {
    try {
      const savedConfig = localStorage.getItem('r2_hardware_config');
      if (savedConfig) {
        const parsed = JSON.parse(savedConfig);
        this.config = { ...this.config, ...parsed };
        console.log('HardwareConfig: 配置已加载', this.config);
      }
    } catch (error) {
      console.warn('HardwareConfig: 配置加载失败，使用默认配置', error);
    }
  }
  
  /**
   * 保存配置
   */
  saveConfig() {
    try {
      localStorage.setItem('r2_hardware_config', JSON.stringify(this.config));
      console.log('HardwareConfig: 配置已保存');
    } catch (error) {
      console.error('HardwareConfig: 配置保存失败', error);
    }
  }
  
  /**
   * 初始化事件监听
   */
  initEventListeners() {
    // 监听配置更新请求
    window.R1Core.eventBus.on('hardware.config.update', (data) => {
      this.updateConfig(data);
    });
    
    // 监听配置查询请求
    window.R1Core.eventBus.on('hardware.config.get', (callback) => {
      if (typeof callback === 'function') {
        callback(this.config);
      }
    });
    
    // 监听模式切换请求
    window.R1Core.eventBus.on('hardware.mode.toggle', () => {
      this.toggleMode();
    });
  }
  
  /**
   * 更新配置
   */
  updateConfig(updates) {
    try {
      this.config = { ...this.config, ...updates };
      this.saveConfig();
      
      // 发布配置更新事件
      window.R1Core.eventBus.emit('hardware.config.updated', this.config);
      
      console.log('HardwareConfig: 配置已更新', updates);
    } catch (error) {
      console.error('HardwareConfig: 配置更新失败', error);
    }
  }
  
  /**
   * 切换硬件模式
   */
  toggleMode() {
    const newMode = !this.config.useRealHardware;
    this.updateConfig({ 
      useRealHardware: newMode,
      'simulation.enabled': !newMode
    });
    
    console.log(`HardwareConfig: 模式已切换为 ${newMode ? '真实硬件' : '模拟模式'}`);
    
    // 通知相关模块
    window.R1Core.eventBus.emit('hardware.mode.changed', {
      useRealHardware: newMode,
      simulationEnabled: !newMode
    });
  }
  
  /**
   * 检查是否使用真实硬件
   */
  isUsingRealHardware() {
    return this.config.useRealHardware;
  }
  
  /**
   * 检查是否启用模拟模式
   */
  isSimulationEnabled() {
    return this.config.simulation.enabled;
  }
  
  /**
   * 获取ESP8266配置
   */
  getESP8266Config() {
    return this.config.esp8266;
  }
  
  /**
   * 获取模拟配置
   */
  getSimulationConfig() {
    return this.config.simulation;
  }
  
  /**
   * 更新ESP8266 IP地址
   */
  updateESP8266Host(host) {
    this.updateConfig({
      'esp8266.host': host
    });
  }
  
  /**
   * 检查硬件连接状态
   */
  async checkHardwareConnection() {
    if (!this.config.useRealHardware) {
      return { connected: false, reason: '模拟模式' };
    }
    
    try {
      const esp8266Config = this.getESP8266Config();
      const url = `http://${esp8266Config.host}:${esp8266Config.port}/status`;
      
      const response = await fetch(url, {
        method: 'GET',
        timeout: esp8266Config.timeout
      });
      
      if (response.ok) {
        const data = await response.json();
        return {
          connected: true,
          data: data,
          host: esp8266Config.host
        };
      } else {
        return {
          connected: false,
          reason: `HTTP ${response.status}`
        };
      }
      
    } catch (error) {
      return {
        connected: false,
        reason: error.message
      };
    }
  }
  
  /**
   * 获取硬件状态信息
   */
  getHardwareStatus() {
    return {
      mode: this.config.useRealHardware ? 'real' : 'simulation',
      esp8266: this.config.esp8266,
      simulation: this.config.simulation,
      debug: this.config.debug
    };
  }
  
  /**
   * 重置为默认配置
   */
  resetToDefaults() {
    this.config = {
      useRealHardware: true,
      esp8266: {
        enabled: true,
        host: "*************",
        port: 80,
        timeout: 30000,
        retryCount: 3
      },
      simulation: {
        enabled: false,
        showUI: true,
        logActions: false,
        signalDetectionDelay: 2000,
        emitDelay: 100,
        successRate: 0.99
      },
      debug: {
        logHardwareEvents: true,
        logAPIRequests: true,
        showConnectionStatus: true
      }
    };
    
    this.saveConfig();
    
    console.log('HardwareConfig: 已重置为默认配置');
    
    // 发布重置事件
    window.R1Core.eventBus.emit('hardware.config.reset', this.config);
  }
  
  /**
   * 导出配置
   */
  exportConfig() {
    return JSON.stringify(this.config, null, 2);
  }
  
  /**
   * 导入配置
   */
  importConfig(configJson) {
    try {
      const imported = JSON.parse(configJson);
      this.config = { ...this.config, ...imported };
      this.saveConfig();
      
      console.log('HardwareConfig: 配置导入成功');
      
      // 发布导入事件
      window.R1Core.eventBus.emit('hardware.config.imported', this.config);
      
      return true;
    } catch (error) {
      console.error('HardwareConfig: 配置导入失败', error);
      return false;
    }
  }
}

// 创建全局实例
window.HardwareConfigManager = new HardwareConfigManager();

// 导出配置管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HardwareConfigManager;
}

console.log('🔧 HardwareConfigManager 已初始化');
console.log('📡 当前硬件模式:', window.HardwareConfigManager.isUsingRealHardware() ? '真实ESP8266' : '模拟模式');

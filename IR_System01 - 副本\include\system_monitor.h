/*
 * System Monitor Header - ESP32-S3 IR Control System
 * Monitors system health, performance, and resource usage
 * Provides comprehensive system diagnostics and alerts
 */

#ifndef SYSTEM_MONITOR_H
#define SYSTEM_MONITOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <functional>

// Forward declarations
class DataManager;
class IRController;
class TaskManager;
class WebSocketManager;

// System Health Levels
enum class SystemHealth {
    EXCELLENT = 5,
    GOOD = 4,
    WARNING = 3,
    CRITICAL = 2,
    ERROR = 1
};

// System Metrics Structure
struct SystemMetrics {
    // Time and Uptime
    unsigned long uptime;
    unsigned long timestamp;
    
    // Memory Information
    uint32_t freeHeap;
    uint32_t totalHeap;
    uint32_t minFreeHeap;
    float memoryUsage;
    
    // Performance
    float cpuUsage;
    
    // Network Status
    bool wifiConnected;
    int wifiRSSI;
    
    // Hardware Status
    bool irHardwareReady;
    
    // Application Status
    int activeClients;
    int totalSignals;
    int activeTasks;
    
    // Overall Health
    SystemHealth health;
    
    SystemMetrics() : uptime(0), timestamp(0), freeHeap(0), totalHeap(0), 
                     minFreeHeap(0), memoryUsage(0.0), cpuUsage(0.0),
                     wifiConnected(false), wifiRSSI(0), irHardwareReady(false),
                     activeClients(0), totalSignals(0), activeTasks(0),
                     health(SystemHealth::GOOD) {}
    
    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
};

class SystemMonitor {
public:
    // Constructor and Destructor
    SystemMonitor();
    ~SystemMonitor();
    
    // Core Methods
    bool initialize();
    void handleLoop();
    
    // Module Reference Setters
    void setDataManager(DataManager* dm);
    void setIRController(IRController* ir);
    void setTaskManager(TaskManager* tm);
    void setWebSocketManager(WebSocketManager* wsm);
    
    // Monitoring Control
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const { return m_isMonitoring; }
    
    // Metrics Collection
    SystemMetrics getCurrentMetrics();
    std::vector<SystemMetrics> getMetricsHistory();
    DynamicJsonDocument getSystemStatus();
    DynamicJsonDocument getDetailedStatus();
    
    // Health Status (Frontend API Matching)
    SystemHealth getCurrentHealth();
    String getHealthString(SystemHealth health);
    
    // Configuration
    void setUpdateInterval(unsigned long intervalMs);
    void setLowMemoryThreshold(uint32_t threshold);
    void setHighCpuThreshold(float threshold);
    void setLowWifiThreshold(int threshold);
    
    // Alert Management
    void setOnHealthChanged(std::function<void(SystemHealth, const String&)> callback);
    void setOnAlert(std::function<void(const String&)> callback);
    void setOnMetricsUpdated(std::function<void(const SystemMetrics&)> callback);
    
    // System Information (Frontend API Matching)
    DynamicJsonDocument getSystemInfo();
    DynamicJsonDocument getHardwareInfo();
    DynamicJsonDocument getNetworkInfo();
    
    // Performance Analysis
    float getAverageMemoryUsage(int samples = 10);
    float getAverageCpuUsage(int samples = 10);
    DynamicJsonDocument getPerformanceReport();
    
    // Maintenance
    void clearHistory();
    void resetCounters();
    bool performSystemCheck();

private:
    // Module References
    DataManager* dataManager;
    IRController* irController;
    TaskManager* taskManager;
    WebSocketManager* wsManager;
    
    // Monitoring State
    bool m_isMonitoring;
    unsigned long lastUpdate;
    unsigned long updateInterval;
    
    // Metrics Storage
    std::vector<SystemMetrics> metricsHistory;
    
    // Thresholds
    uint32_t lowMemoryThreshold;
    float highCpuThreshold;
    int lowWifiThreshold;
    
    // Event Callbacks
    std::function<void(SystemHealth, const String&)> onHealthChanged;
    std::function<void(const String&)> onAlert;
    std::function<void(const SystemMetrics&)> onMetricsUpdated;
    
    // Constants
    static const size_t MAX_HISTORY_SIZE = 100;
    static const unsigned long DEFAULT_UPDATE_INTERVAL = 5000; // 5 seconds
    static const uint32_t DEFAULT_LOW_MEMORY_THRESHOLD = 10000; // 10KB
    static constexpr float DEFAULT_HIGH_CPU_THRESHOLD = 80.0; // 80%
    static const int DEFAULT_LOW_WIFI_THRESHOLD = -70; // -70 dBm
    
    // Internal Methods
    SystemMetrics collectMetrics();
    SystemHealth calculateHealth(const SystemMetrics& metrics);
    void checkThresholds(const SystemMetrics& metrics);
    void addToHistory(const SystemMetrics& metrics);
    
    // Hardware Monitoring
    uint32_t getFreeHeap();
    uint32_t getTotalHeap();
    uint32_t getMinFreeHeap();
    float calculateMemoryUsage();
    float calculateCpuUsage();
    
    // Network Monitoring
    bool checkWiFiConnection();
    int getWiFiRSSI();
    
    // Application Monitoring
    int getActiveClientCount();
    int getTotalSignalCount();
    int getActiveTaskCount();
    bool checkIRHardware();
    
    // Alert System
    void triggerAlert(const String& message);
    void triggerHealthChange(SystemHealth newHealth, const String& reason);
    
    // Helper Methods
    String formatUptime(unsigned long uptime);
    String formatMemorySize(uint32_t bytes);
    String formatPercentage(float percentage);
    bool isThresholdExceeded(float value, float threshold);
    
    // Performance Calculation
    float calculateMovingAverage(const std::vector<float>& values, int samples);
    void updatePerformanceMetrics(SystemMetrics& metrics);
};

#endif // SYSTEM_MONITOR_H

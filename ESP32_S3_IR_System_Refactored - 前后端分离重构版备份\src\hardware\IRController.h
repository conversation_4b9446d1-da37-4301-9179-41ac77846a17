#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include "../../config/system-config.h"
#include "../../config/hardware-config.h"

// Forward declaration
class DataManager;

/**
 * 红外控制器类
 *
 * 负责红外信号的发送和接收
 * 硬件抽象层的核心组件
 *
 * 核心功能：
 * - 红外信号发送
 * - 红外信号学习
 * - 硬件状态监控
 * - 信号格式转换
 */
class IRController {
public:
    // ==================== 构造函数和析构函数 ====================

    /**
     * 构造函数
     */
    IRController();

    /**
     * 析构函数
     */
    ~IRController();

    // ==================== 系统生命周期 ====================

    /**
     * 初始化红外控制器
     * @return bool 初始化是否成功
     */
    bool initialize();

    /**
     * 关闭红外控制器
     */
    void shutdown();

    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;

    /**
     * 设置数据管理器依赖
     * @param dataManager 数据管理器指针
     */
    void setDataManager(DataManager* dataManager);

    // ==================== 信号发送 ====================

    /**
     * 发送红外信号
     * @param signalCode 信号代码
     * @param protocol 信号协议
     * @return bool 发送是否成功
     */
    bool sendSignal(const String& signalCode, const String& protocol = "NEC");

    /**
     * 发送原始信号
     * @param rawData 原始数据数组
     * @param dataLength 数据长度
     * @param frequency 载波频率
     * @return bool 发送是否成功
     */
    bool sendRawSignal(const uint16_t* rawData, size_t dataLength, uint16_t frequency = 38000);

    /**
     * 批量发送信号
     * @param signals 信号列表
     * @param interval 信号间隔（毫秒）
     * @return int 成功发送的信号数量
     */
    int batchSendSignals(const DynamicJsonDocument& signals, int interval = 100);

    /**
     * 停止当前发送
     */
    void stopSending();

    // ==================== 信号学习 ====================

    /**
     * 开始学习模式
     * @param timeout 学习超时时间（毫秒）
     * @return bool 是否成功开始学习
     */
    bool startLearning(unsigned long timeout = 10000);

    /**
     * 停止学习模式
     */
    void stopLearning();

    /**
     * 检查是否正在学习
     * @return bool 是否正在学习
     */
    bool isLearning() const;

    /**
     * 获取学习到的信号
     * @return DynamicJsonDocument 学习到的信号数据
     */
    DynamicJsonDocument getLearnedSignal();

    /**
     * 清除学习缓冲区
     */
    void clearLearnBuffer();

    // ==================== 信号分析 ====================

    /**
     * 分析信号协议
     * @param signalCode 信号代码
     * @return String 检测到的协议
     */
    String analyzeProtocol(const String& signalCode);

    /**
     * 验证信号格式
     * @param signalCode 信号代码
     * @param protocol 协议类型
     * @return bool 格式是否有效
     */
    bool validateSignalFormat(const String& signalCode, const String& protocol);

    /**
     * 转换信号格式
     * @param signalCode 原始信号代码
     * @param fromProtocol 源协议
     * @param toProtocol 目标协议
     * @return String 转换后的信号代码
     */
    String convertSignalFormat(const String& signalCode, const String& fromProtocol, const String& toProtocol);

    // ==================== 硬件状态 ====================

    /**
     * 获取硬件状态
     * @return DynamicJsonDocument 硬件状态信息
     */
    DynamicJsonDocument getHardwareStatus();

    /**
     * 测试硬件功能
     * @return bool 硬件测试是否通过
     */
    bool testHardware();

    /**
     * 校准红外发射器
     * @return bool 校准是否成功
     */
    bool calibrateTransmitter();

    /**
     * 校准红外接收器
     * @return bool 校准是否成功
     */
    bool calibrateReceiver();

    // ==================== 配置管理 ====================

    /**
     * 设置发射功率
     * @param power 功率级别 (0-100)
     */
    void setTransmitPower(int power);

    /**
     * 设置接收灵敏度
     * @param sensitivity 灵敏度级别 (0-100)
     */
    void setReceiveSensitivity(int sensitivity);

    /**
     * 设置载波频率
     * @param frequency 载波频率 (Hz)
     */
    void setCarrierFrequency(uint16_t frequency);

    /**
     * 获取当前配置
     * @return DynamicJsonDocument 当前配置
     */
    DynamicJsonDocument getCurrentConfig();

    // ==================== 统计信息 ====================

    /**
     * 获取发送统计
     * @return DynamicJsonDocument 发送统计信息
     */
    DynamicJsonDocument getSendStatistics();

    /**
     * 获取学习统计
     * @return DynamicJsonDocument 学习统计信息
     */
    DynamicJsonDocument getLearnStatistics();

    /**
     * 重置统计信息
     */
    void resetStatistics();

private:
    // ==================== 私有成员变量 ====================

    bool m_initialized;                  // 是否已初始化
    bool m_isLearning;                   // 是否正在学习
    bool m_isSending;                    // 是否正在发送
    DataManager* m_dataManager;          // 数据管理器依赖

    // 硬件配置
    int m_transmitPin;                   // 发射引脚
    int m_receivePin;                    // 接收引脚
    uint16_t m_carrierFrequency;         // 载波频率
    int m_transmitPower;                 // 发射功率
    int m_receiveSensitivity;            // 接收灵敏度

    // 学习相关
    unsigned long m_learningStartTime;   // 学习开始时间
    unsigned long m_learningTimeout;     // 学习超时时间
    void* m_learnBuffer;                 // 学习缓冲区
    size_t m_learnBufferSize;            // 学习缓冲区大小

    // 统计信息
    unsigned long m_totalSent;           // 总发送次数
    unsigned long m_totalLearned;        // 总学习次数
    unsigned long m_sendErrors;          // 发送错误次数
    unsigned long m_learnErrors;         // 学习错误次数

    // ==================== 私有方法 ====================

    /**
     * 初始化硬件引脚
     * @return bool 初始化是否成功
     */
    bool initializeHardware();

    /**
     * 清理硬件资源
     */
    void cleanupHardware();

    /**
     * 初始化学习缓冲区
     * @return bool 初始化是否成功
     */
    bool initializeLearnBuffer();

    /**
     * 清理学习缓冲区
     */
    void cleanupLearnBuffer();

    /**
     * 处理接收到的信号
     * @param rawData 原始数据
     * @param dataLength 数据长度
     */
    void processReceivedSignal(const uint16_t* rawData, size_t dataLength);

    /**
     * 编码信号数据
     * @param signalCode 信号代码
     * @param protocol 协议类型
     * @param encodedData 编码后的数据
     * @param maxLength 最大长度
     * @return size_t 编码后的数据长度
     */
    size_t encodeSignalData(const String& signalCode, const String& protocol,
                           uint16_t* encodedData, size_t maxLength);

    /**
     * 解码信号数据
     * @param rawData 原始数据
     * @param dataLength 数据长度
     * @param protocol 协议类型
     * @return String 解码后的信号代码
     */
    String decodeSignalData(const uint16_t* rawData, size_t dataLength, const String& protocol);

    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);

    /**
     * 更新统计信息
     * @param type 统计类型
     * @param success 是否成功
     */
    void updateStatistics(const String& type, bool success);

    // ==================== 协议特定发送方法 ====================

    /**
     * 发送NEC协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendNECSignal(const String& signalCode);

    /**
     * 发送RC5协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendRC5Signal(const String& signalCode);

    /**
     * 发送RC6协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendRC6Signal(const String& signalCode);

    /**
     * 发送Sony协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendSonySignal(const String& signalCode);

    /**
     * 从代码发送原始信号
     * @param signalCode 原始信号代码
     * @return bool 发送是否成功
     */
    bool sendRawSignalFromCode(const String& signalCode);

    // ==================== 底层硬件控制 ====================

    /**
     * 发送载波脉冲
     * @param duration 持续时间（微秒）
     */
    void sendCarrierPulse(unsigned int duration);

    /**
     * 发送空白间隔
     * @param duration 持续时间（微秒）
     */
    void sendSpace(unsigned int duration);

    /**
     * 开始载波输出
     */
    void startCarrier();

    /**
     * 停止载波输出
     */
    void stopCarrier();

    /**
     * 设置PWM参数
     * @param frequency 频率
     * @param dutyCycle 占空比
     */
    void setupPWM(uint32_t frequency, uint8_t dutyCycle);
};

#endif // IR_CONTROLLER_H

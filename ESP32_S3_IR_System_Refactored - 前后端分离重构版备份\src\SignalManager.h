/**
 * 信号管理器 - 头文件
 * 负责信号的CRUD操作、数据验证、存储管理
 */

#ifndef SIGNAL_MANAGER_H
#define SIGNAL_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <map>

// 信号数据结构
struct Signal {
    String id;              // 信号ID (signal_xxxxxxxx)
    String name;            // 信号名称
    String type;            // 信号类型 (tv/ac/fan/light/other)
    String description;     // 信号描述
    String signalCode;      // 信号码 (如: 0x20DF10EF)
    String protocol;        // 协议 (NEC/RC5/SONY/RAW)
    String frequency;       // 载波频率 (如: 38000)
    String rawData;         // 原始数据
    bool isLearned;         // 是否为学习信号
    unsigned long created;  // 创建时间戳
    unsigned long lastSent; // 最后发射时间戳
    int sentCount;          // 发射次数
    bool parseSuccess;      // 解析是否成功
    
    // 构造函数
    Signal() : isLearned(false), created(0), lastSent(0), sentCount(0), parseSuccess(false) {}
    
    // 从JSON创建信号
    static Signal fromJson(const JsonObject& json);
    
    // 转换为JSON
    JsonObject toJson(JsonDocument& doc) const;
    
    // 验证信号数据
    bool isValid() const;
    
    // 生成信号ID
    static String generateId();
};

// API响应结构
struct APIResponse {
    bool success;
    String error;
    JsonDocument data;
    unsigned long timestamp;
    
    APIResponse() : success(false), timestamp(millis()) {}
    
    // 创建成功响应
    static APIResponse createSuccess(const JsonDocument& data = JsonDocument());
    
    // 创建错误响应
    static APIResponse createError(const String& error, int code = 0);
    
    // 转换为JSON字符串
    String toJsonString() const;
};

// 错误代码定义
enum ErrorCode {
    SUCCESS = 0,
    INVALID_PARAMETER = 1001,
    SIGNAL_NOT_FOUND = 1002,
    LEARNING_TIMEOUT = 1003,
    HARDWARE_ERROR = 1004,
    STORAGE_ERROR = 1005,
    MEMORY_ERROR = 1006,
    DUPLICATE_SIGNAL = 1007,
    MAX_SIGNALS_REACHED = 1008
};

class SignalManager {
private:
    std::vector<Signal> signals;        // 信号列表
    std::map<String, int> signalIndex;  // 信号ID到索引的映射
    
    // 配置参数
    static const int MAX_SIGNALS = 200;
    static const size_t MAX_NAME_LENGTH = 50;
    static const size_t MAX_DESCRIPTION_LENGTH = 200;
    static const size_t MAX_RAW_DATA_LENGTH = 1000;
    
    // 内部方法
    bool validateSignal(const Signal& signal) const;
    bool validateSignalCode(const String& code) const;
    bool validateProtocol(const String& protocol) const;
    bool validateFrequency(const String& frequency) const;
    void updateSignalIndex();
    int findSignalIndex(const String& id) const;
    
public:
    SignalManager();
    ~SignalManager();
    
    // 初始化
    bool init();
    
    // 信号CRUD操作
    APIResponse addSignal(const Signal& signal);
    APIResponse getSignal(const String& id);
    APIResponse getAllSignals();
    APIResponse updateSignal(const String& id, const Signal& signal);
    APIResponse deleteSignal(const String& id);
    APIResponse batchDeleteSignals(const std::vector<String>& ids);
    
    // 信号统计
    int getSignalCount() const { return signals.size(); }
    int getSignalCountByType(const String& type) const;
    unsigned long getTotalSentCount() const;
    Signal getMostUsedSignal() const;
    Signal getLatestSignal() const;
    
    // 信号搜索和过滤
    std::vector<Signal> searchSignals(const String& keyword) const;
    std::vector<Signal> filterSignalsByType(const String& type) const;
    std::vector<Signal> sortSignals(const String& sortBy, bool ascending = true) const;
    
    // 学习信号处理
    APIResponse saveLearnedSignal(const String& name, const String& type, 
                                 const String& description, const JsonObject& signalData);
    
    // 导入导出功能
    APIResponse importSignals(const std::vector<Signal>& importSignals, bool overwrite = false);
    APIResponse exportSignals(const std::vector<String>& ids = {}) const;
    
    // 文件操作
    bool loadFromFile();
    bool saveToFile() const;
    
    // 数据验证
    APIResponse validateImportData(const JsonArray& data) const;
    APIResponse parseImportFile(const String& content, const String& fileType) const;
    APIResponse parseTextImport(const String& content, const String& defaultType) const;

    // 内部解析方法
    APIResponse parseJSONImport(const String& content) const;
    APIResponse parseCSVImport(const String& content) const;
    
    // 信号发射统计更新
    void updateSentStatistics(const String& id);
    
    // 内存管理
    void optimizeMemory();
    size_t getMemoryUsage() const;
    
    // 调试和诊断
    void printSignalList() const;
    void printMemoryInfo() const;
    JsonObject getSystemStats() const;
};

#endif // SIGNAL_MANAGER_H

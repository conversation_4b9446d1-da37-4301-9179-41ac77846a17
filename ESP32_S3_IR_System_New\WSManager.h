/*
 * WSManager.h - WebSocket管理器（100% API兼容）
 * 
 * 零全局内存分配的WebSocket管理系统
 * 完全兼容原前端的所有WebSocket通信
 */

#ifndef WS_MANAGER_H
#define WS_MANAGER_H

#include <Arduino.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <vector>
#include <memory>

// Client information structure
struct WSClient {
    uint32_t id;
    IPAddress ip;
    unsigned long connectTime;
    unsigned long lastPing;
    bool isActive;
    
    WSClient() : id(0), connectTime(0), lastPing(0), isActive(false) {}
    WSClient(uint32_t clientId, IPAddress clientIp) 
        : id(clientId), ip(clientIp), connectTime(millis()), lastPing(millis()), isActive(true) {}
};

// Message queue structure
struct QueuedMessage {
    uint32_t clientId; // 0 for broadcast
    String type;
    String data;
    unsigned long timestamp;
    int priority;
    
    QueuedMessage() : clientId(0), timestamp(0), priority(0) {}
    QueuedMessage(uint32_t id, const String& msgType, const String& msgData, int prio = 0)
        : clientId(id), type(msgType), data(msgData), timestamp(millis()), priority(prio) {}
};

class WSManager {
public:
    WSManager();
    ~WSManager();
    
    bool initialize(AsyncWebSocket* webSocket);
    void handleLoop();
    
    // Client management (API Compatible)
    int getConnectedClients() const;
    DynamicJsonDocument getClientList() const;
    DynamicJsonDocument getWebSocketStats() const;
    bool isClientConnected(uint32_t clientId) const;
    
    // Message sending (API Compatible)
    bool sendMessage(uint32_t clientId, const String& type, const DynamicJsonDocument& data);
    bool sendMessage(uint32_t clientId, const String& type, const String& data);
    bool broadcastMessage(const String& type, const DynamicJsonDocument& data);
    bool broadcastMessage(const String& type, const String& data);
    
    // System notifications (API Compatible)
    bool notifySignalSent(const String& signalId, const String& signalName);
    bool notifyLearningStarted(const String& signalName);
    bool notifyLearningCompleted(const String& signalId, const String& signalName);
    bool notifyTaskStarted(const String& taskId, const String& taskName);
    bool notifyTaskCompleted(const String& taskId, const String& taskName);
    bool notifySystemStatus(const DynamicJsonDocument& status);
    bool notifyConfigReset();
    
    // Message queue management
    bool queueMessage(const QueuedMessage& message);
    void processMessageQueue();
    void clearMessageQueue();
    
    // Statistics
    unsigned long getTotalMessagesSent() const { return m_totalMessagesSent; }
    unsigned long getTotalMessagesReceived() const { return m_totalMessagesReceived; }

private:
    bool m_initialized;
    AsyncWebSocket* m_webSocket;
    
    // Client management - created after PSRAM is available
    std::unique_ptr<std::vector<WSClient>> m_clients;
    std::unique_ptr<std::vector<QueuedMessage>> m_messageQueue;
    
    // Statistics
    unsigned long m_totalMessagesSent;
    unsigned long m_totalMessagesReceived;
    unsigned long m_lastCleanupTime;
    
    // Internal methods
    bool initializeStorage();
    void onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                         AwsEventType type, void* arg, uint8_t* data, size_t len);
    void handleWebSocketMessage(AsyncWebSocketClient* client, const String& message);
    void addClient(uint32_t clientId, IPAddress clientIp);
    void removeClient(uint32_t clientId);
    WSClient* findClient(uint32_t clientId);
    void cleanupInactiveClients();
    
    // Message formatting
    String formatMessage(const String& type, const DynamicJsonDocument& data);
    String formatMessage(const String& type, const String& data);
    DynamicJsonDocument parseMessage(const String& message);
    
    // Message handlers
    void handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data);
    void handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data);
    void handleGetSignalsMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data);
    void handleGetTasksMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& data);
    
    // Validation
    bool validateMessage(const DynamicJsonDocument& message);
};

#endif // WS_MANAGER_H

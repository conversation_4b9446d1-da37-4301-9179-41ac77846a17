/*
 * ESP32-S3 PSRAM 测试程序
 * 
 * 这个简单的程序用于验证PSRAM是否正常工作
 */

void setup() {
  Serial.begin(115200);
  delay(2000);  // 等待串口初始化
  
  Serial.println("=================================");
  Serial.println("ESP32-S3 PSRAM 测试程序");
  Serial.println("=================================");
  
  // 检查PSRAM是否可用
  if (psramFound()) {
    Serial.println("✅ PSRAM FOUND - PSRAM检测成功!");
    
    // 获取PSRAM大小
    size_t psramSize = ESP.getPsramSize();
    Serial.printf("📊 Total PSRAM: %d bytes (%.2f MB)\n", psramSize, psramSize / 1024.0 / 1024.0);
    
    // 获取可用PSRAM
    size_t freePsram = ESP.getFreePsram();
    Serial.printf("💾 Free PSRAM: %d bytes (%.2f MB)\n", freePsram, freePsram / 1024.0 / 1024.0);
    
    // 测试PSRAM分配
    Serial.println("\n🧪 开始PSRAM分配测试...");
    
    // 分配1MB内存
    size_t testSize = 1024 * 1024;  // 1MB
    void* testPtr = ps_malloc(testSize);
    
    if (testPtr != NULL) {
      Serial.printf("✅ 成功分配 %d bytes PSRAM内存\n", testSize);
      
      // 写入测试数据
      memset(testPtr, 0xAA, testSize);
      Serial.println("✅ 写入测试数据成功");
      
      // 验证数据
      uint8_t* bytePtr = (uint8_t*)testPtr;
      bool testPassed = true;
      for (int i = 0; i < 1000; i++) {  // 检查前1000字节
        if (bytePtr[i] != 0xAA) {
          testPassed = false;
          break;
        }
      }
      
      if (testPassed) {
        Serial.println("✅ 数据验证成功 - PSRAM工作正常!");
      } else {
        Serial.println("❌ 数据验证失败");
      }
      
      // 释放内存
      free(testPtr);
      Serial.println("✅ 内存释放成功");
      
    } else {
      Serial.println("❌ PSRAM内存分配失败");
    }
    
  } else {
    Serial.println("❌ PSRAM NOT FOUND - PSRAM未检测到");
    Serial.println("请检查:");
    Serial.println("1. 硬件是否支持PSRAM");
    Serial.println("2. 板子配置是否正确");
    Serial.println("3. PSRAM设置是否为OPI");
  }
  
  Serial.println("\n=================================");
  Serial.println("测试完成");
  Serial.println("=================================");
}

void loop() {
  // 每10秒显示一次内存状态
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 10000) {
    lastCheck = millis();
    
    Serial.println("\n--- 内存状态 ---");
    Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
    if (psramFound()) {
      Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
    }
    Serial.printf("Uptime: %lu seconds\n", millis() / 1000);
  }
  
  delay(1000);
}

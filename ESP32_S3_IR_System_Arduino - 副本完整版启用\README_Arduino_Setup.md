# ESP32-S3 IR Control System - Arduino IDE Setup Guide

## 🎯 Arduino IDE Configuration (Critical for PSRAM)

### ✅ Board Configuration
**Board:** ESP32S3 Dev Module

**Important Settings:**
- **Flash Mode:** QIO (Critical for PSRAM)
- **Flash Size:** 16MB (128Mb)
- **PSRAM:** OPI PSRAM (Critical)
- **Flash Frequency:** 80MHz
- **Upload Speed:** 460800
- **Core Debug Level:** None
- **Partition Scheme:** Default 4MB with spiffs (1.2MB APP/1.5MB SPIFFS)

### 📚 Required Libraries

Install the following libraries through Arduino IDE Library Manager:

1. **ArduinoJson** (Version 6.21.3)
   - Search: "ArduinoJson"
   - Install by Benoit Blanchon

2. **ESPAsyncWebServer** (Version 3.7.8+)
   - Search: "ESP Async WebServer"
   - Install by lacamera

3. **AsyncTCP** (Version 3.4.4+)
   - Search: "AsyncTCP"
   - Install by d<PERSON><PERSON>

4. **IRremoteESP8266** (Version 2.8.6)
   - Search: "IRremoteESP8266"
   - Install by <PERSON> Conran

### 🔧 Hardware Connections

- **IR Transmitter:** GPIO 21
- **IR Receiver:** GPIO 14
- **Serial Port:** COM6 (adjust as needed)

### 📁 File Structure

```
ESP32_S3_IR_System_Arduino/
├── ESP32_S3_IR_System_Arduino.ino  (Main sketch)
├── data_manager.h                  (Data management)
├── data_manager.cpp
├── ir_controller.h                 (IR control)
├── ir_controller.cpp
├── wifi_manager.h                  (WiFi management)
├── wifi_manager.cpp
├── websocket_manager.h             (WebSocket communication)
├── websocket_manager.cpp
├── web_server_manager.h            (Web server)
├── web_server_manager.cpp
├── task_manager.h                  (Task management)
├── task_manager.cpp
├── system_monitor.h                (System monitoring)
├── system_monitor.cpp
└── data/                           (Web interface files)
    ├── index.html
    ├── css/
    │   ├── main.css
    │   └── modules.css
    └── js/
        ├── main.js
        ├── core.js
        ├── signal-manager.js
        └── ... (other JS modules)
```

### 🚀 Upload Process

1. **Upload Sketch:**
   - Open `ESP32_S3_IR_System_Arduino.ino` in Arduino IDE
   - Select correct board and settings
   - Click Upload

2. **Upload SPIFFS Data:**
   - Install ESP32 Sketch Data Upload tool
   - Select Tools > ESP32 Sketch Data Upload
   - Wait for upload to complete

### ✅ Expected Serial Output

After successful upload, you should see:

```
========================================
ESP32-S3 IR Control System Starting...
Arduino IDE Version with OPI PSRAM
========================================
Step 0: System Information
Chip Model: ESP32-S3
✅ PSRAM FOUND - Starting comprehensive tests
📊 Total PSRAM: 8388608 bytes (8.00 MB)
✅ 1KB allocation: SUCCESS
✅ 1MB allocation: SUCCESS
✅ Read/Write integrity: SUCCESS
🎉 SYSTEM INITIALIZATION COMPLETED! 🎉
```

### 🌐 Web Interface Access

- **AP Mode:** http://***********
- **Station Mode:** Check serial output for IP address
- **WebSocket:** ws://[IP_ADDRESS]/ws

### 🔧 Troubleshooting

**PSRAM Not Found:**
- Verify Board: ESP32S3 Dev Module
- Verify PSRAM: OPI PSRAM
- Verify Flash Mode: QIO
- Check hardware connections

**Compilation Errors:**
- Ensure all libraries are installed
- Check library versions match requirements
- Restart Arduino IDE if needed

**Upload Errors:**
- Check COM port selection
- Try different upload speeds
- Press and hold BOOT button during upload if needed

### 📊 Performance Notes

- **PSRAM:** 8MB available for large data structures
- **Flash:** 16MB for program and SPIFFS
- **CPU:** 240MHz dual-core
- **WiFi:** 802.11 b/g/n support
- **IR Range:** Configurable, typically 5-10 meters

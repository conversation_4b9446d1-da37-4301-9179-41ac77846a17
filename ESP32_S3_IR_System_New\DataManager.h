/*
 * DataManager.h - 数据管理器（100% API兼容）
 * 
 * 零全局内存分配的数据管理系统
 * 完全兼容原前端的所有API调用
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <Arduino.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <vector>
#include <memory>
#include <map>

// ==================== Data Structures ====================

struct SignalData {
    String id;
    String name;
    String type;           // "tv", "ac", "fan", "light", "other"
    String protocol;       // "NEC", "SONY", "SAMSUNG", etc.
    String signalCode;     // Hex data (前端期望的字段名)
    String rawData;        // Raw timing data
    String frequency;      // Carrier frequency (前端期望String类型)
    unsigned long created; // 创建时间 (前端期望的字段名)
    bool isActive;
    int sentCount;         // 发送次数 (前端期望的字段名)
    unsigned long lastSent; // 最后发送时间
    bool isLearned;        // 是否学习获得 (前端期望的字段名)
    String description;

    SignalData() : frequency("38000"), created(0), isActive(true), sentCount(0), lastSent(0), isLearned(false) {}

    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

struct TimerData {
    String id;
    String name;
    String signalId;
    String schedule;       // Cron-like schedule
    bool isActive;
    unsigned long timestamp;
    unsigned long lastExecuted;
    int executionCount;

    TimerData() : isActive(false), timestamp(0), lastExecuted(0), executionCount(0) {}

    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

struct TaskData {
    String id;
    String name;
    String type;           // "signal_send", "batch_send", "timer_execute"
    String status;         // "pending", "running", "completed", "failed"
    String parameters;     // JSON参数字符串，避免全局构造时内存分配
    unsigned long timestamp;
    unsigned long startTime;
    unsigned long endTime;
    String result;
    String error;

    TaskData() : timestamp(0), startTime(0), endTime(0) {}

    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);

    // 参数管理方法
    DynamicJsonDocument getParameters() const;
    void setParameters(const DynamicJsonDocument& params);
};

// ==================== DataManager Class ====================

class DataManager {
public:
    DataManager();
    ~DataManager();
    
    // Initialization
    bool initialize();
    bool isInitialized() const { return m_initialized; }
    
    // Signal Management (100% API Compatible)
    bool addSignal(const SignalData& signal);
    bool deleteSignal(const String& id);
    bool updateSignal(const String& id, const SignalData& signal);
    SignalData* getSignal(const String& id);
    DynamicJsonDocument getSignalsJSON() const;
    int getSignalCount() const;
    void clearAllSignals();
    
    // Timer Management (100% API Compatible)
    bool addTimer(const TimerData& timer);
    bool deleteTimer(const String& id);
    bool updateTimer(const String& id, const TimerData& timer);
    TimerData* getTimer(const String& id);
    DynamicJsonDocument getTimersJSON() const;
    int getTimerCount() const;
    void clearAllTimers();
    
    // Task Management (100% API Compatible)
    bool addTask(const TaskData& task);
    bool deleteTask(const String& id);
    bool updateTask(const String& id, const TaskData& task);
    TaskData* getTask(const String& id);
    DynamicJsonDocument getTasksJSON() const;
    int getTaskCount() const;
    void clearAllTasks();
    
    // Configuration Management
    bool setConfig(const String& key, const String& value);
    String getConfig(const String& key, const String& defaultValue = "") const;
    bool hasConfig(const String& key) const;
    DynamicJsonDocument getSystemConfig() const;
    bool setSystemConfig(const DynamicJsonDocument& config);
    bool resetSystemConfig();
    
    // Data Persistence
    bool saveAllData();
    bool loadAllData();
    void clearAllData();
    
    // Export/Import (API Compatible)
    DynamicJsonDocument exportData(const String& type = "all") const;
    bool importData(const DynamicJsonDocument& data);
    bool backupData();

    // Statistics for frontend compatibility
    DynamicJsonDocument getStatistics() const;
    
    // System Information
    size_t getUsedSpace() const;
    size_t getFreeSpace() const;
    DynamicJsonDocument getSystemStatus() const;

private:
    bool m_initialized;
    
    // File paths
    static const char* SIGNALS_FILE;
    static const char* TIMERS_FILE;
    static const char* TASKS_FILE;
    static const char* CONFIG_FILE;
    
    // Data storage - created after PSRAM is available
    std::unique_ptr<std::vector<SignalData>> m_signals;
    std::unique_ptr<std::vector<TimerData>> m_timers;
    std::unique_ptr<std::vector<TaskData>> m_tasks;
    std::unique_ptr<std::map<String, String>> m_config;
    std::unique_ptr<Preferences> m_preferences;
    
    // Helper methods
    bool initializeStorage();
    bool createDataDirectories();
    String generateUniqueId(const String& prefix) const;
    
    // File operations
    bool loadSignalsFromFile();
    bool saveSignalsToFile();
    bool loadTimersFromFile();
    bool saveTimersToFile();
    bool loadTasksFromFile();
    bool saveTasksToFile();
    bool loadConfigFromFile();
    bool saveConfigToFile();
    
    // Search helpers
    int findSignalIndex(const String& id) const;
    int findTimerIndex(const String& id) const;
    int findTaskIndex(const String& id) const;
    
    // Validation
    bool validateSignalData(const SignalData& signal) const;
    bool validateTimerData(const TimerData& timer) const;
    bool validateTaskData(const TaskData& task) const;
};

#endif // DATA_MANAGER_H

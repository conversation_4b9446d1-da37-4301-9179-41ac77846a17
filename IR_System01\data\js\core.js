/**
 * R1系统 - 核心系统
 * 提供事件总线、通信管理、状态管理等核心功能
 */

/**
 * 事件总线 - 高效的事件发布订阅系统
 */
class EventBus {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
    this.performance = {
      totalEvents: 0,
      totalTime: 0,
      avgTime: 0
    };

    // 批处理优化 - 不影响信号发射时序
    this.eventQueue = [];
    this.processing = false;
    this.batchSize = 20;
    this.highPriorityEvents = new Set([
      'control.emit.progress',
      'control.signal.emitting',
      'control.signal.emitted',
      'signal.learning.status.changed',
      'system.error',
      'timer.task.due',
      'control.emit.completed',
      'timer.task.execution.request',
      'esp32.connected',
      'esp32.disconnected',
      'esp32.error'
    ]);
    this.maxQueueSize = 1000;
    this.mergableEvents = new Set([
      'system.monitor.update',
      'status.display.update',
      'signal.list.refresh'
    ]);
  }

  /**
   * 监听事件
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   * @param {object} options - 选项
   */
  on(event, handler, options = {}) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    
    const listener = {
      handler,
      priority: options.priority || 0,
      once: options.once || false
    };
    
    this.events.get(event).push(listener);
    
    // 按优先级排序
    this.events.get(event).sort((a, b) => b.priority - a.priority);
  }

  /**
   * 监听一次性事件
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   */
  once(event, handler) {
    this.on(event, handler, { once: true });
  }

  /**
   * 取消监听
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   */
  off(event, handler) {
    if (!this.events.has(event)) return;
    
    const listeners = this.events.get(event);
    const index = listeners.findIndex(listener => listener.handler === handler);
    
    if (index > -1) {
      listeners.splice(index, 1);
    }
    
    if (listeners.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * 发布事件 - 优化版本（批处理 + 高优先级立即处理）
   * @param {string} event - 事件名
   * @param {any} data - 事件数据
   * @returns {Promise} 处理结果
   */
  async emit(event, data = null) {
    // 高优先级事件立即处理（信号发射相关）
    if (this.highPriorityEvents.has(event)) {
      return this.processEventImmediate(event, data);
    }

    // 普通事件加入批处理队列
    return this.addToBatch(event, data);
  }

  /**
   * 立即处理高优先级事件
   */
  async processEventImmediate(event, data) {
    const startTime = performance.now();

    if (!this.events.has(event)) {
      return [];
    }

    const listeners = this.events.get(event);
    const results = [];
    const toRemove = [];

    for (let i = 0; i < listeners.length; i++) {
      const listener = listeners[i];

      try {
        const result = await listener.handler(data);
        results.push(result);

        if (listener.once) {
          toRemove.push(i);
        }
      } catch (error) {
        console.error(`Event handler error for ${event}:`, error);
        results.push({ error });
      }
    }

    // 移除一次性监听器
    toRemove.reverse().forEach(index => {
      listeners.splice(index, 1);
    });

    // 性能统计
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.performance.totalEvents++;
    this.performance.totalTime += duration;
    this.performance.avgTime = this.performance.totalTime / this.performance.totalEvents;

    return results;
  }

  /**
   * 添加到批处理队列
   */
  addToBatch(event, data) {
    return new Promise((resolve) => {
      // 检查队列大小
      if (this.eventQueue.length >= this.maxQueueSize) {
        console.warn('EventBus: 事件队列已满，丢弃旧事件');
        this.eventQueue.shift();
      }

      // 对于可合并事件，替换队列中的旧事件
      if (this.mergableEvents.has(event)) {
        const existingIndex = this.eventQueue.findIndex(item => item.event === event);
        if (existingIndex !== -1) {
          this.eventQueue[existingIndex] = { event, data, resolve, timestamp: performance.now() };
          return;
        }
      }

      // 添加到队列
      this.eventQueue.push({ event, data, resolve, timestamp: performance.now() });

      // 调度批处理
      this.scheduleBatch();
    });
  }

  /**
   * 调度批处理
   */
  scheduleBatch() {
    if (this.processing) return;

    this.processing = true;

    // 使用requestIdleCallback优化性能
    if (window.requestIdleCallback) {
      requestIdleCallback(() => {
        this.processBatch();
        this.processing = false;
      }, { timeout: 16 });
    } else {
      requestAnimationFrame(() => {
        this.processBatch();
        this.processing = false;
      });
    }
  }

  /**
   * 批处理事件
   */
  async processBatch() {
    const startTime = performance.now();
    const timeSlice = 8; // 8ms时间片

    while (this.eventQueue.length > 0 && (performance.now() - startTime) < timeSlice) {
      const batch = this.eventQueue.splice(0, this.batchSize);

      for (const item of batch) {
        try {
          const result = await this.processEventImmediate(item.event, item.data);
          item.resolve(result);
        } catch (error) {
          console.error(`Batch event error for ${item.event}:`, error);
          item.resolve([{ error }]);
        }
      }
    }

    // 如果还有事件，继续处理
    if (this.eventQueue.length > 0) {
      this.scheduleBatch();
    }
  }

  /**
   * 同步发布事件 - 用于需要立即执行的场景（如UI事件注册）
   * @param {string} event - 事件名
   * @param {any} data - 事件数据
   * @returns {Array} 处理结果
   */
  emitSync(event, data = null) {
    const startTime = performance.now();

    if (!this.events.has(event)) {
      return [];
    }

    const listeners = this.events.get(event);
    const results = [];
    const toRemove = [];

    for (let i = 0; i < listeners.length; i++) {
      const listener = listeners[i];

      try {
        // 同步执行处理器
        const result = listener.handler(data);
        results.push(result);

        if (listener.once) {
          toRemove.push(i);
        }
      } catch (error) {
        console.error(`Event handler error for ${event}:`, error);
        results.push({ error });
      }
    }

    // 移除一次性监听器
    toRemove.reverse().forEach(index => {
      listeners.splice(index, 1);
    });

    // 性能统计
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.performance.totalEvents++;
    this.performance.totalTime += duration;
    this.performance.avgTime = this.performance.totalTime / this.performance.totalEvents;

    return results;
  }

  /**
   * 获取性能统计
   * @returns {object} 性能数据
   */
  getPerformance() {
    return { ...this.performance };
  }

  /**
   * 清空所有事件监听器
   */
  clear() {
    this.events.clear();
    this.onceEvents.clear();
    this.eventQueue.length = 0;
    this.processing = false;
  }

  /**
   * 获取批处理统计
   */
  getBatchStats() {
    return {
      queueLength: this.eventQueue.length,
      processing: this.processing,
      highPriorityEvents: Array.from(this.highPriorityEvents),
      mergableEvents: Array.from(this.mergableEvents)
    };
  }
}

/**
 * ESP32通信管理器
 */
class ESP32Communicator {
  constructor(eventBus) {
    this.eventBus = eventBus;
    // ESP32-S3系统直接访问本地服务器
    this.baseURL = window.location.origin || 'http://***********';
    this.wsURL = `ws://${window.location.host || '***********'}/ws`;
    this.ws = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.isReconnecting = false;  // 添加重连状态标志
    this.esp32Status = null;  // 跟踪ESP32状态，避免重复事件
    this.requestQueue = [];
    this.batchTimer = null;
    this.batchDelay = 50; // 50ms批量延迟
    
    this.performance = {
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      avgResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * 初始化通信 - 开发模式下不阻止系统启动
   */
  async init() {
    try {
      // 测试HTTP连接
      await this.testConnection();

      // 建立WebSocket连接
      await this.connectWebSocket();

      this.connected = true;
      console.log('前端WebSocket连接成功');

      // 等待ESP32模拟器完成连接，然后查询状态（仅用于同步，不触发事件）
      setTimeout(async () => {
        await this.syncESP32Status();
      }, 2000); // 等待2秒确保ESP32模拟器连接完成

    } catch (error) {
      console.log('ESP32连接失败 (开发模式):', error.message);
      this.connected = false;
      this.eventBus.emit('esp32.error', {
        error: error.message,
        timestamp: Date.now()
      });

      // 开发模式下不抛出错误，允许系统继续启动
      console.log('系统将在离线模式下运行');
    }
  }

  /**
   * 同步ESP32设备连接状态（仅用于状态同步，不触发事件）
   */
  async syncESP32Status() {
    try {
      console.log('🔍 [前端] 同步ESP32状态...');
      const response = await fetch(`${this.baseURL}/api/system/status`);
      const data = await response.json();

      if (data.success && data.data) {
        const esp32Connected = data.data.esp32_connected;
        console.log('🔍 [前端] 同步ESP32状态:', esp32Connected ? '已连接' : '未连接');

        // 只同步状态，不触发事件（事件由WebSocket消息触发）
        this.esp32Status = esp32Connected;
        console.log('🔍 [前端] 状态同步完成，不触发事件');
      }
    } catch (error) {
      console.error('🔍 [前端] 同步ESP32状态失败:', error);
    }
  }

  /**
   * 检查ESP32设备连接状态（用于主动查询，会触发事件）
   */
  async checkESP32Status() {
    try {
      console.log('🔍 [前端] 开始查询ESP32状态...');
      const response = await fetch(`${this.baseURL}/api/system/status`);
      const data = await response.json();

      console.log('🔍 [前端] API响应数据:', data);

      if (data.success && data.data) {
        const esp32Connected = data.data.esp32_connected;
        console.log('🔍 [前端] ESP32连接状态:', esp32Connected ? '已连接' : '未连接');
        console.log('🔍 [前端] 原始状态值:', esp32Connected);

        // 只有状态发生变化时才发出事件
        if (this.esp32Status !== esp32Connected) {
          console.log(`🔍 [前端] ESP32状态变化: ${this.esp32Status} → ${esp32Connected}`);
          this.esp32Status = esp32Connected;

          if (esp32Connected) {
            console.log('✅ [前端] 发出esp32.connected事件');
            this.eventBus.emit('esp32.connected', {
              message: 'ESP32设备已连接',
              timestamp: Date.now()
            });
          } else {
            console.log('❌ [前端] 发出esp32.disconnected事件');
            this.eventBus.emit('esp32.disconnected', {
              message: 'ESP32设备未连接',
              timestamp: Date.now()
            });
          }
        } else {
          console.log('🔍 [前端] ESP32状态无变化，跳过事件发送');
        }
      } else {
        console.error('🔍 [前端] API响应格式错误:', data);
      }
    } catch (error) {
      console.error('🔍 [前端] 查询ESP32状态失败:', error);
    }
  }

  /**
   * 测试HTTP连接
   */
  async testConnection() {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    try {
      const response = await fetch(`${this.baseURL}/api/system/status`, {
        signal: controller.signal,
        method: 'GET'
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('连接超时');
      }
      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsURL);
        
        this.ws.onopen = () => {
          console.log('WebSocket连接成功');
          this.reconnectAttempts = 0;
          this.isReconnecting = false;
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
          } catch (error) {
            console.error('WebSocket消息解析错误:', error);
          }
        };
        
        this.ws.onclose = (event) => {
          this.connected = false;
          console.log('WebSocket连接关闭:', event.code, event.reason);

          // WebSocket断开不等于ESP32设备断开，只记录WebSocket状态
          console.log('WebSocket连接断开，ESP32设备状态需要重新查询');

          // 自动重连
          this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          reject(new Error('WebSocket连接失败'));
        };
        
        // 连接超时
        setTimeout(() => {
          if (this.ws.readyState !== WebSocket.OPEN) {
            this.ws.close();
            reject(new Error('WebSocket连接超时'));
          }
        }, 5000);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 处理WebSocket消息
   * @param {object} data - 消息数据
   */
  handleWebSocketMessage(data) {
    // 验证WebSocket消息格式 - 详细调试
    if (window.R1DataValidator && !window.R1DataValidator.validateWebSocketMessage(data)) {
      console.warn('WebSocket消息格式验证失败:');
      console.warn('错误详情:', window.R1DataValidator.getErrors());
      console.warn('实际收到的消息:', data);
      console.warn('消息结构:', {
        type: typeof data.type,
        payload: typeof data.payload,
        timestamp: typeof data.timestamp,
        keys: Object.keys(data)
      });
    }

    const { type, payload } = data;

    // 处理ESP32连接状态消息
    if (type === 'esp32_connected') {
      this.esp32Status = true;  // 更新状态跟踪
      this.eventBus.emit('esp32.connected', payload);
    } else if (type === 'esp32_disconnected') {
      this.esp32Status = false;  // 更新状态跟踪
      this.eventBus.emit('esp32.disconnected', payload);
    } else if (type === 'esp32_status') {
      this.eventBus.emit('esp32.status', payload);
    } else {
      // 发布对应的事件（保持原有逻辑）
      this.eventBus.emit(`esp32.${type}`, payload);
    }

    // 发布通用WebSocket消息事件
    this.eventBus.emit('esp32.message', data);
  }

  /**
   * 计划重连
   */
  scheduleReconnect() {
    // 防止重复重连
    if (this.isReconnecting) {
      console.log('重连已在进行中，跳过此次重连请求');
      return;
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连');
      this.eventBus.emit('esp32.reconnect.failed', {
        attempts: this.reconnectAttempts,
        timestamp: Date.now()
      });
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避

    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);

    setTimeout(async () => {
      try {
        await this.connectWebSocket();
        this.isReconnecting = false;
        console.log('重连成功');
      } catch (error) {
        console.error('重连失败:', error);
        this.isReconnecting = false;
        // 只有在未达到最大重连次数时才继续重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      }
    }, delay);
  }

  /**
   * 发送HTTP请求
   * @param {string} endpoint - 端点
   * @param {object} options - 请求选项
   * @returns {Promise} 响应数据
   */
  async request(endpoint, options = {}) {
    const startTime = performance.now();
    this.performance.requestCount++;
    
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        timeout: 10000,
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();

      // 验证API响应格式 - 暂时禁用以避免干扰
      if (window.R1DataValidator && !window.R1DataValidator.validateAPIResponse(data)) {
        console.debug('API响应格式验证失败:', window.R1DataValidator.getErrors(), 'Response:', data);
      }

      // 性能统计
      const responseTime = performance.now() - startTime;
      this.performance.successCount++;
      this.performance.totalResponseTime += responseTime;
      this.performance.avgResponseTime =
        this.performance.totalResponseTime / this.performance.successCount;
      
      // 发布请求成功事件
      this.eventBus.emit('esp32.request.success', {
        endpoint,
        responseTime,
        data
      });
      
      return data;
      
    } catch (error) {
      this.performance.errorCount++;
      
      // 发布请求失败事件
      this.eventBus.emit('esp32.request.error', {
        endpoint,
        error: error.message,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }

  /**
   * 批量请求
   * @param {string} endpoint - 端点
   * @param {object} data - 请求数据
   * @returns {Promise} 响应数据
   */
  batchRequest(endpoint, data) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ endpoint, data, resolve, reject });
      
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.flushBatchRequests();
        }, this.batchDelay);
      }
    });
  }

  /**
   * 刷新批量请求
   */
  async flushBatchRequests() {
    if (this.requestQueue.length === 0) return;
    
    const batch = this.requestQueue.splice(0);
    this.batchTimer = null;
    
    try {
      const response = await this.request('/api/batch', {
        method: 'POST',
        body: JSON.stringify({
          requests: batch.map(({ endpoint, data }) => ({ endpoint, data }))
        })
      });
      
      // 处理批量响应
      if (response.results && Array.isArray(response.results)) {
        response.results.forEach((result, index) => {
          if (result.success) {
            batch[index].resolve(result.data);
          } else {
            batch[index].reject(new Error(result.error || '批量请求失败'));
          }
        });
      } else {
        throw new Error('批量响应格式错误');
      }
      
    } catch (error) {
      // 批量失败，逐个重试
      console.warn('批量请求失败，逐个重试:', error);
      
      batch.forEach(async ({ endpoint, data, resolve, reject }) => {
        try {
          const result = await this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
          });
          resolve(result);
        } catch (retryError) {
          reject(retryError);
        }
      });
    }
  }

  /**
   * 发送WebSocket消息
   * @param {string} type - 消息类型
   * @param {any} data - 消息数据
   * @returns {boolean} 是否发送成功
   */
  sendWebSocketMessage(type, data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: Date.now()
      };
      
      this.ws.send(JSON.stringify(message));
      return true;
    }
    
    console.warn('WebSocket未连接，无法发送消息');
    return false;
  }

  /**
   * 获取性能统计
   * @returns {object} 性能数据
   */
  getPerformance() {
    return { ...this.performance };
  }

  /**
   * 关闭连接
   */
  close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.connected = false;
  }
}

/**
 * API核心类 - 统一的API请求管理
 */
class APICore {
  constructor(eventBus) {
    this.eventBus = eventBus;
    // ESP32-S3系统直接访问本地服务器
    this.baseURL = window.location.origin || 'http://***********';
    this.ws = null;
    this.wsURL = `ws://${window.location.host || '***********'}/ws`;
    this.isInitialized = false;
    this.requestQueue = [];
    this.isOnline = true;
  }

  /**
   * 初始化API核心
   */
  async init() {
    try {
      console.log('🔗 初始化API核心...');

      // 初始化WebSocket连接
      await this.initWebSocket();

      // 设置网络状态监听
      this.setupNetworkMonitoring();

      this.isInitialized = true;
      console.log('✅ API核心初始化完成');

    } catch (error) {
      console.error('❌ API核心初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化WebSocket连接
   */
  async initWebSocket() {
    try {
      this.ws = new WebSocket(this.wsURL);

      this.ws.onopen = () => {
        console.log('🔌 WebSocket连接已建立');
        this.eventBus.emit('api.websocket.connected');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('WebSocket消息解析失败:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('🔌 WebSocket连接已关闭');
        this.eventBus.emit('api.websocket.disconnected');
        // 自动重连
        setTimeout(() => this.initWebSocket(), 3000);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket连接错误:', error);
        this.eventBus.emit('api.websocket.error', { error });
      };

    } catch (error) {
      console.error('WebSocket初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理WebSocket消息
   */
  handleWebSocketMessage(data) {
    // 验证消息格式
    if (!data.type || !data.payload || typeof data.timestamp !== 'number') {
      console.warn('收到格式不正确的WebSocket消息:', data);
      return;
    }

    // 发布WebSocket消息事件
    this.eventBus.emit('api.websocket.message', data);

    // 根据消息类型处理
    switch (data.type) {
      case 'connected':
        this.eventBus.emit('esp32.connected', data.payload);
        break;
      case 'disconnected':
        this.eventBus.emit('esp32.disconnected', data.payload);
        break;
      case 'error':
        this.eventBus.emit('esp32.error', data.payload);
        break;
      case 'pong':
        // 心跳响应
        break;
      default:
        console.log('收到未知类型的WebSocket消息:', data.type);
    }
  }

  /**
   * 设置网络状态监听
   */
  setupNetworkMonitoring() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.eventBus.emit('api.network.online');
      console.log('🌐 网络连接已恢复');
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.eventBus.emit('api.network.offline');
      console.log('🌐 网络连接已断开');
    });
  }

  /**
   * 发送HTTP请求
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (config.method !== 'GET' && options.data) {
      config.body = JSON.stringify(options.data);
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.message || '请求失败'}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 ${config.method} ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * GET请求
   */
  async get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  async post(endpoint, data = null, options = {}) {
    return this.request(endpoint, { ...options, method: 'POST', data });
  }

  /**
   * PUT请求
   */
  async put(endpoint, data = null, options = {}) {
    return this.request(endpoint, { ...options, method: 'PUT', data });
  }

  /**
   * DELETE请求
   */
  async delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * 兼容性方法 - 前端使用的apiCall方法
   * @param {string} endpoint - API端点
   * @param {object} options - 请求选项
   */
  async apiCall(endpoint, options = {}) {
    // 处理前端传入的body参数
    if (options.body && typeof options.body === 'string') {
      try {
        options.data = JSON.parse(options.body);
      } catch (error) {
        console.error('解析请求体失败:', error);
        throw new Error('无效的JSON请求体');
      }
    }

    // 移除body参数，使用data参数
    delete options.body;

    return this.request(endpoint, options);
  }

  /**
   * 发送WebSocket消息
   */
  sendWebSocketMessage(type, payload = {}) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }

    const message = {
      type,
      payload,
      timestamp: Date.now()
    };

    try {
      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      return false;
    }
  }

  /**
   * 获取API状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isOnline: this.isOnline,
      wsConnected: this.ws && this.ws.readyState === WebSocket.OPEN,
      baseURL: this.baseURL,
      wsURL: this.wsURL
    };
  }
}

/**
 * 通知系统
 */
class NotificationSystem {
  constructor() {
    this.container = null;
    this.notifications = new Map();
    this.defaultDuration = 5000;
    this.maxNotifications = 5;
  }

  /**
   * 初始化通知系统
   */
  init() {
    this.container = $('#notificationContainer');
    if (!this.container) {
      console.error('通知容器未找到');
    }
  }

  /**
   * 显示通知
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型 (success, error, warning, info)
   * @param {number} duration - 显示时长
   * @returns {string} 通知ID
   */
  show(message, type = 'info', duration = this.defaultDuration) {
    if (!this.container) {
      console.warn('通知系统未初始化');
      return null;
    }

    const id = R1Utils.generateId('notification');
    
    // 限制通知数量
    if (this.notifications.size >= this.maxNotifications) {
      const oldestId = this.notifications.keys().next().value;
      this.hide(oldestId);
    }

    const notification = R1Utils.dom.create('div', {
      className: `notification ${type}`,
      dataset: { id }
    }, `
      <div class="notification-content">
        <div class="notification-message">${message}</div>
        <button class="notification-close" onclick="window.R1System.notification.hide('${id}')">&times;</button>
      </div>
    `);

    this.container.appendChild(notification);
    this.notifications.set(id, {
      element: notification,
      timer: null,
      type,
      message,
      timestamp: Date.now()
    });

    // 自动隐藏
    if (duration > 0) {
      window.UnifiedTimerManager.addTimer(
        `notification_${id}`,
        () => {
          this.hide(id);
        },
        duration,
        false
      );
    }

    return id;
  }

  /**
   * 隐藏通知
   * @param {string} id - 通知ID
   */
  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;

    // 清除定时器
    if (window.UnifiedTimerManager.hasTimer(`notification_${id}`)) {
      window.UnifiedTimerManager.removeTimer(`notification_${id}`);
    }

    // 移除元素
    notification.element.style.animation = 'slideOut 0.3s ease-in forwards';
    window.UnifiedTimerManager.addTimer(
      `notification_remove_${id}`,
      () => {
        if (notification.element.parentNode) {
          notification.element.parentNode.removeChild(notification.element);
        }
        this.notifications.delete(id);
      },
      300,
      false
    );
  }

  /**
   * 清空所有通知
   */
  clear() {
    this.notifications.forEach((notification, id) => {
      this.hide(id);
    });
  }
}

// 添加slideOut动画样式
if (!document.getElementById('notification-styles')) {
  const style = document.createElement('style');
  style.id = 'notification-styles';
  style.textContent = `
    @keyframes slideOut {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    .notification-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-md);
    }

    .notification-message {
      flex: 1;
      font-size: 0.875rem;
      line-height: 1.4;
    }

    .notification-close {
      background: none;
      border: none;
      font-size: 1.25rem;
      cursor: pointer;
      color: var(--text-secondary);
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-sm);
      transition: background-color var(--transition-fast);
    }

    .notification-close:hover {
      background: var(--bg-secondary);
    }
  `;
  document.head.appendChild(style);
}

/**
 * 统一模块基类 - 所有模块必须继承此基类
 */
class BaseModule {
  constructor(eventBus, esp32, moduleName) {
    this.eventBus = eventBus;
    this.esp32 = esp32;
    this.moduleName = moduleName;
    this.isInitialized = false;
    this.isActive = false;

    // 统一的模块状态
    this.state = {
      loading: false,
      error: null,
      data: new Map()
    };

    // 统一的性能监控
    this.performance = {
      initTime: 0,
      operationCount: 0,
      errorCount: 0,
      lastOperation: null
    };

    // BaseModule不自动初始化，由调用者控制 - 符合控制反转原则
  }

  /**
   * 统一的初始化流程 - 所有模块必须遵循
   * 简单直接的初始化，由调用者控制时机
   */
  async init() {
    // 简单的重复初始化检查
    if (this.isInitialized) {
      console.log(`⚠️ ${this.moduleName} 模块已经初始化，跳过重复初始化`);
      return;
    }

    const startTime = performance.now();
    try {
      console.log(`🔧 开始初始化 ${this.moduleName} 模块...`);

      // 1. 初始化事件监听器
      try {
        await this.initEventListeners();
        console.log(`📡 ${this.moduleName} 事件监听器初始化完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} 事件监听器初始化失败:`, error);
        throw error;
      }

      // 2. 初始化UI组件
      try {
        await this.initUI();
        console.log(`🎨 ${this.moduleName} UI初始化完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} UI初始化失败:`, error);
        throw error;
      }

      // 3. 加载模块数据
      try {
        await this.loadData();
        console.log(`📊 ${this.moduleName} 数据加载完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} 数据加载失败:`, error);
        // 数据加载失败不应该阻止模块初始化
        console.warn(`⚠️ ${this.moduleName} 数据加载失败，但模块将继续初始化`);
      }

      // 4. 标记初始化完成
      this.isInitialized = true;
      this.isActive = true;
      this.performance.initTime = performance.now() - startTime;

      // 5. 发布模块就绪事件
      this.emitEvent('module.ready', {
        moduleName: this.moduleName,
        initTime: this.performance.initTime
      });

      console.log(`✅ ${this.moduleName} 模块初始化完成，耗时: ${this.performance.initTime.toFixed(2)}ms`);

    } catch (error) {
      this.performance.initTime = performance.now() - startTime;
      console.error(`❌ ${this.moduleName} 模块初始化失败:`, error);
      this.handleError(error, '模块初始化');
      throw error;
    }
  }

  /**
   * 统一的事件监听器初始化 - 子类必须实现
   */
  async initEventListeners() {
    // 监听系统级事件
    this.eventBus.on('system.refresh', () => {
      this.refresh();
    });

    // 子类实现具体的事件监听
    if (this.setupEventListeners) {
      await this.setupEventListeners();
    }
  }

  /**
   * 统一的UI初始化 - 子类必须实现
   */
  async initUI() {
    // 子类实现具体的UI初始化
    if (this.setupUI) {
      await this.setupUI();
    }
  }

  /**
   * 统一的数据加载 - 子类可选实现
   */
  async loadData() {
    // 子类实现具体的数据加载
    if (this.loadModuleData) {
      await this.loadModuleData();
    }
  }

  /**
   * 统一的事件发布方法
   * ✅ 根源修复：直接发送原始数据，不进行包装
   * 这样保持与直接使用 eventBus.emit() 的一致性
   */
  emitEvent(eventType, data = null) {
    // 如果数据中没有 source，自动添加
    if (data && typeof data === 'object' && !data.source) {
      data.source = this.moduleName;
    }

    // 直接发送原始数据，不包装
    this.eventBus.emit(eventType, data);
    this.performance.lastOperation = eventType;
    this.performance.operationCount++;
  }

  /**
   * 统一的错误处理方法
   */
  handleError(error, operation = '操作') {
    this.performance.errorCount++;
    this.state.error = {
      message: error.message,
      operation: operation,
      timestamp: Date.now()
    };

    console.error(`❌ ${this.moduleName} ${operation}失败:`, error);

    // 发布错误事件
    this.emitEvent('module.error', {
      moduleName: this.moduleName,
      operation: operation,
      error: error.message
    });

    // 显示用户通知
    if (window.R1System && window.R1System.notification) {
      window.R1System.notification.show(
        `${this.moduleName} ${operation}失败: ${error.message}`,
        'error'
      );
    }
  }

  /**
   * 统一的成功处理方法
   */
  handleSuccess(message, operation = '操作', data = null) {
    this.state.error = null;

    console.log(`✅ ${this.moduleName} ${operation}成功:`, message);

    // 发布成功事件
    this.emitEvent('module.success', {
      moduleName: this.moduleName,
      operation: operation,
      message: message,
      data: data
    });

    // 显示用户通知
    if (window.R1System && window.R1System.notification) {
      window.R1System.notification.show(
        `${message}`,
        'success'
      );
    }
  }

  /**
   * 统一的ESP32请求方法
   */
  async requestESP32(endpoint, options = {}) {
    try {
      this.state.loading = true;
      const response = await this.esp32.request(endpoint, options);
      this.state.loading = false;
      return response;
    } catch (error) {
      this.state.loading = false;
      throw error;
    }
  }



  /**
   * 统一的刷新方法
   */
  async refresh() {
    try {
      this.state.loading = true;

      // 子类实现具体的刷新逻辑
      if (this.onRefresh) {
        await this.onRefresh();
      }

      this.state.loading = false;
      this.handleSuccess('刷新完成', '数据刷新');

    } catch (error) {
      this.state.loading = false;
      this.handleError(error, '数据刷新');
    }
  }

  /**
   * 获取模块状态
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isInitialized: this.isInitialized,
      isActive: this.isActive,
      state: { ...this.state },
      performance: { ...this.performance }
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    this.isActive = false;
    this.isInitialized = false;

    // 清理事件监听器
    this.eventBus.off('system.refresh');

    // 子类实现具体的清理逻辑
    if (this.cleanup) {
      this.cleanup();
    }

    console.log(`🗑️ ${this.moduleName} 模块已销毁`);
  }
}

// 导出核心类
window.BaseModule = BaseModule;
window.EventBus = EventBus;
window.ESP32Communicator = ESP32Communicator;
window.NotificationSystem = NotificationSystem;
window.APICore = APICore;

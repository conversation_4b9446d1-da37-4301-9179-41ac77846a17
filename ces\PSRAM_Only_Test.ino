/*
 * 最简化的PSRAM测试 - 排除所有可能的干扰因素
 * 只测试PSRAM功能，不包含任何其他库或功能
 */

void setup() {
    // 初始化串口
    Serial.begin(115200);
    delay(5000);  // 更长的延迟确保串口稳定
    
    Serial.println();
    Serial.println("========================================");
    Serial.println("🚀 ESP32-S3 最简化PSRAM测试");
    Serial.println("========================================");
    
    // 立即检测PSRAM
    Serial.println("🔍 开始PSRAM检测...");
    
    if (!psramFound()) {
        Serial.println();
        Serial.println("❌ PSRAM NOT FOUND!");
        Serial.println("⚠️  检查配置:");
        Serial.println("   - Board: ESP32S3 Dev Module");
        Serial.println("   - PSRAM: OPI PSRAM");
        Serial.println("   - Flash Mode: QIO");
        Serial.println("========================================");
        return;
    }
    
    Serial.println();
    Serial.println("🎉 PSRAM检测成功!");
    Serial.println("✅ PSRAM is available!");
    Serial.printf("📊 Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("🧠 Free: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    
    // 简单的分配测试
    Serial.println();
    Serial.println("🧪 进行简单的内存分配测试...");
    
    void* testPtr = ps_malloc(1024 * 1024);  // 分配1MB
    if (testPtr) {
        Serial.println("✅ 1MB PSRAM分配成功!");
        free(testPtr);
        Serial.println("✅ 内存释放成功!");
    } else {
        Serial.println("❌ PSRAM分配失败");
    }
    
    Serial.println();
    Serial.println("🏁 PSRAM测试完成!");
    Serial.println("========================================");
}

void loop() {
    // 每10秒显示一次状态
    static unsigned long lastCheck = 0;
    if (millis() - lastCheck > 10000) {
        lastCheck = millis();
        
        Serial.println("💓 系统运行中...");
        if (psramFound()) {
            Serial.printf("📊 Free PSRAM: %d bytes\n", ESP.getFreePsram());
        }
        Serial.printf("⏰ 运行时间: %lu 秒\n", millis() / 1000);
    }
    
    delay(1000);
}

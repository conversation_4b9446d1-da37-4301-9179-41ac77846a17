/**
 * ESP32-S3 红外控制系统 - 主程序
 * 支持纯净前端的完整后端实现
 * 
 * 功能特性：
 * - 17个API端点支持
 * - 6个WebSocket事件
 * - 红外学习和发射
 * - 文件系统数据管理
 * - PSRAM优化支持
 */

#include <Arduino.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <AsyncWebSocket.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>

// 核心模块包含
#include "SignalManager.h"
#include "IRController.h"
#include "WebServerManager.h"
#include "FileSystemManager.h"
#include "WebSocketManager.h"

// 硬件配置
const int IR_SEND_PIN = 4;      // 红外发射引脚
const int IR_RECV_PIN = 15;     // 红外接收引脚
const int STATUS_LED_PIN = 2;   // 状态指示LED

// 网络配置
const int HTTP_PORT = 80;
const int WS_PORT = 81;
const char* AP_SSID = "ESP32-IR-System";
const char* AP_PASSWORD = "12345678";

// 全局对象
AsyncWebServer server(HTTP_PORT);
AsyncWebSocket ws("/ws");
SignalManager signalManager;
IRController irController(IR_SEND_PIN, IR_RECV_PIN);
WebServerManager webServerManager(&server, &signalManager, &irController);
FileSystemManager fileSystemManager;
WebSocketManager webSocketManager(&ws, &signalManager);

// 系统状态
bool systemInitialized = false;
unsigned long lastHeartbeat = 0;
const unsigned long HEARTBEAT_INTERVAL = 30000; // 30秒心跳

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== ESP32-S3 红外控制系统启动 ===");
  
  // 初始化状态LED
  pinMode(STATUS_LED_PIN, OUTPUT);
  digitalWrite(STATUS_LED_PIN, LOW);
  
  // 1. 初始化PSRAM（如果可用）
  if (!initPSRAM()) {
    Serial.println("⚠️ PSRAM初始化失败，使用普通RAM");
  }
  
  // 2. 初始化文件系统
  if (!initFileSystem()) {
    Serial.println("❌ 文件系统初始化失败");
    return;
  }
  
  // 3. 初始化红外硬件
  if (!initIRHardware()) {
    Serial.println("❌ 红外硬件初始化失败");
    return;
  }
  
  // 4. 初始化WiFi
  if (!initWiFi()) {
    Serial.println("❌ WiFi初始化失败");
    return;
  }
  
  // 5. 初始化Web服务器
  if (!initWebServer()) {
    Serial.println("❌ Web服务器初始化失败");
    return;
  }
  
  // 6. 设置红外控制器回调函数
  setupIRCallbacks();

  // 7. 加载系统数据
  if (!loadSystemData()) {
    Serial.println("⚠️ 系统数据加载失败，使用默认配置");
  }
  
  // 系统初始化完成
  systemInitialized = true;
  digitalWrite(STATUS_LED_PIN, HIGH);
  
  Serial.println("✅ ESP32-S3 红外控制系统启动完成");
  Serial.printf("📡 HTTP服务器: http://%s:%d\n", WiFi.localIP().toString().c_str(), HTTP_PORT);
  Serial.printf("🔌 WebSocket服务器: ws://%s/ws\n", WiFi.localIP().toString().c_str());
  Serial.println("🎯 系统就绪，等待前端连接...");
}

void loop() {
  // 处理WebSocket连接
  ws.cleanupClients();
  
  // 处理红外学习
  irController.handleLearning();
  
  // 系统心跳
  handleSystemHeartbeat();
  
  // 状态LED闪烁（表示系统运行）
  handleStatusLED();
  
  // 短暂延迟避免看门狗重置
  delay(10);
}

/**
 * 初始化PSRAM
 */
bool initPSRAM() {
  #ifdef BOARD_HAS_PSRAM
  if (psramInit()) {
    Serial.printf("✅ PSRAM初始化成功: %d bytes\n", ESP.getPsramSize());
    return true;
  }
  #endif
  return false;
}

/**
 * 初始化文件系统
 */
bool initFileSystem() {
  Serial.println("📁 初始化文件系统...");
  
  if (!SPIFFS.begin(true)) {
    Serial.println("❌ SPIFFS初始化失败");
    return false;
  }
  
  // 检查文件系统状态
  size_t totalBytes = SPIFFS.totalBytes();
  size_t usedBytes = SPIFFS.usedBytes();
  Serial.printf("✅ SPIFFS初始化成功: %d/%d bytes (%.1f%%)\n", 
                usedBytes, totalBytes, (float)usedBytes/totalBytes*100);
  
  // 初始化文件系统管理器
  return fileSystemManager.init();
}

/**
 * 初始化红外硬件
 */
bool initIRHardware() {
  Serial.println("📡 初始化红外硬件...");
  
  if (!irController.init()) {
    Serial.println("❌ 红外控制器初始化失败");
    return false;
  }
  
  Serial.println("✅ 红外硬件初始化成功");
  return true;
}

/**
 * 初始化WiFi
 */
bool initWiFi() {
  Serial.println("📶 初始化WiFi...");
  
  // 尝试连接已保存的WiFi
  WiFi.mode(WIFI_STA);
  WiFi.begin();
  
  // 等待连接
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.printf("\n✅ WiFi连接成功: %s\n", WiFi.localIP().toString().c_str());
    return true;
  }
  
  // 连接失败，启动AP模式
  Serial.println("\n⚠️ WiFi连接失败，启动AP模式");
  WiFi.mode(WIFI_AP);
  WiFi.softAP(AP_SSID, AP_PASSWORD);
  
  Serial.printf("✅ AP模式启动成功: %s\n", WiFi.softAPIP().toString().c_str());
  return true;
}

/**
 * 初始化Web服务器
 */
bool initWebServer() {
  Serial.println("🌐 初始化Web服务器...");
  
  // 初始化WebSocket管理器
  if (!webSocketManager.init()) {
    Serial.println("❌ WebSocket管理器初始化失败");
    return false;
  }
  
  // 初始化Web服务器管理器
  if (!webServerManager.init()) {
    Serial.println("❌ Web服务器管理器初始化失败");
    return false;
  }
  
  // 启动服务器
  server.begin();
  Serial.println("✅ Web服务器启动成功");
  
  return true;
}

/**
 * 加载系统数据
 */
bool loadSystemData() {
  Serial.println("📊 加载系统数据...");
  
  // 加载信号数据
  if (!signalManager.loadFromFile()) {
    Serial.println("⚠️ 信号数据加载失败");
  }
  
  Serial.printf("✅ 系统数据加载完成，共 %d 个信号\n", signalManager.getSignalCount());
  return true;
}

/**
 * 处理系统心跳
 */
void handleSystemHeartbeat() {
  unsigned long now = millis();
  if (now - lastHeartbeat >= HEARTBEAT_INTERVAL) {
    lastHeartbeat = now;
    
    // 发送系统状态到WebSocket客户端
    webSocketManager.broadcastSystemStatus();
    
    // 保存系统数据（定期备份）
    signalManager.saveToFile();
  }
}

/**
 * 处理状态LED
 */
void handleStatusLED() {
  static unsigned long lastBlink = 0;
  static bool ledState = false;

  unsigned long now = millis();
  if (now - lastBlink >= 1000) { // 1秒闪烁
    lastBlink = now;
    ledState = !ledState;
    digitalWrite(STATUS_LED_PIN, ledState ? HIGH : LOW);
  }
}

/**
 * 设置红外控制器回调函数
 */
void setupIRCallbacks() {
  Serial.println("🔗 设置红外控制器回调函数...");

  // 设置学习成功回调
  irController.setLearningCallback([](const IRSignalData& signalData) {
    Serial.println("🎯 学习成功回调被触发");

    // 创建信号数据JSON
    JsonDocument doc;
    JsonObject signalObj = signalData.toJson(doc);

    // 通过WebSocket广播学习成功事件
    webSocketManager.broadcastSignalLearned(signalObj);

    Serial.printf("✅ 学习成功: 协议=%s, 信号码=%s\n",
                 signalData.protocol.c_str(), signalData.signalCode.c_str());
  });

  // 设置学习错误回调
  irController.setErrorCallback([](const String& error) {
    Serial.printf("❌ 学习错误回调被触发: %s\n", error.c_str());

    // 通过WebSocket广播学习错误事件
    webSocketManager.broadcastLearningError(error);
  });

  Serial.println("✅ 红外控制器回调函数设置完成");
}

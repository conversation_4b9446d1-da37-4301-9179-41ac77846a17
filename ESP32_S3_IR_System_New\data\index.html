<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 R1智能红外控制系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/modules.css">
</head>
<body>
    <div id="app" class="r1-system">
        <!-- 系统头部 -->
        <header class="system-header">
            <div class="header-left">
                <h1 class="system-title">
                    <span class="logo">🏠</span>
                    R1智能红外控制系统
                </h1>
                <div class="system-version">v2.0.0</div>
            </div>
            <div class="header-right">
                <div class="connection-status" id="connectionStatus">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span class="status-text" id="statusText">连接中...</span>
                </div>
                <div class="system-time" id="systemTime"></div>
                <button class="settings-btn" id="settingsBtn" title="系统设置">⚙️</button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧边栏 -->
            <aside class="sidebar">
                <!-- 状态显示区域 (替换原快速操作区域) -->
                <div class="status-display-area">
                    <h3>📊 状态显示</h3>
                    <div id="statusDisplayArea">
                        <!-- 状态显示内容 - 纯显示功能 -->
                    </div>
                </div>

                <!-- 系统监控区域 (替换原系统概览区域) -->
                <div class="system-monitor-area">
                    <h3>🔍 系统监控</h3>
                    <div id="systemMonitorArea">
                        <!-- 系统监控内容 - 纯监控功能 -->
                    </div>
                </div>
            </aside>

            <!-- 主要模块区域 -->
            <div class="modules-container">
                <!-- 模块导航标签 -->
                <nav class="module-tabs">
                    <button class="tab-btn active" data-module="signal-manager">
                        <span class="tab-icon">📡</span>
                        <span class="tab-text">信号管理</span>
                    </button>
                    <button class="tab-btn" data-module="control-module">
                        <span class="tab-icon">🎮</span>
                        <span class="tab-text">控制面板</span>
                    </button>
                    <button class="tab-btn" data-module="timer-settings">
                        <span class="tab-icon">⏰</span>
                        <span class="tab-text">定时设置</span>
                    </button>
                </nav>

                <!-- 模块内容区域 -->
                <div class="module-content">
                    <!-- 1. 信号管理模块 -->
                    <div class="module-panel active" id="signal-manager">
                        <div class="module-header">
                            <h2>📡 信号管理</h2>
                            <div class="module-actions">
                                <button class="action-btn" id="signalViewToggle" data-action="toggle-view">
                                    <span class="btn-icon">📋</span>
                                    <span class="btn-text">列表视图</span>
                                </button>
                                <button class="action-btn" id="multiSelectToggleBtn" data-action="toggle-multiselect">
                                    <span class="btn-icon">☑️</span>
                                    <span class="btn-text">多选模式</span>
                                </button>
                                <button class="action-btn" id="importSignalsBtn" data-action="import-signals">
                                    <span class="btn-icon">📥</span>
                                    <span class="btn-text">导入信号</span>
                                </button>
                                <button class="action-btn" id="exportSignalsBtn" data-action="export-all-signals">
                                    <span class="btn-icon">📤</span>
                                    <span class="btn-text">导出信号</span>
                                </button>
                                <button class="action-btn" id="signalSearchToggle" data-action="toggle-search">
                                    <span class="btn-icon">🔍</span>
                                    <span class="btn-text">搜索</span>
                                </button>
                                <button class="action-btn primary" id="learnSignalBtn" data-action="toggle-learning">
                                    <span class="btn-icon" id="learnBtnIcon">🔍</span>
                                    <span class="btn-text" id="learnBtnText">学习信号</span>
                                </button>
                            </div>
                        </div>

                        <!-- 搜索和过滤区域 -->
                        <div class="search-filter-area" id="searchFilterArea" style="display: none;">
                            <div class="search-box">
                                <input type="text" id="signalSearchInput" placeholder="搜索信号名称..." class="search-input">
                                <button class="search-btn" id="signalSearchBtn" data-action="filter-signals">🔍</button>
                            </div>
                            <div class="filter-options">
                                <select id="signalTypeFilter" class="filter-select">
                                    <option value="">所有类型</option>
                                    <option value="tv">电视</option>
                                    <option value="ac">空调</option>
                                    <option value="fan">风扇</option>
                                    <option value="light">灯光</option>
                                    <option value="other">其他</option>
                                </select>
                                <select id="signalSortBy" class="filter-select">
                                    <option value="name">按名称排序</option>
                                    <option value="created">按创建时间</option>
                                    <option value="used">按使用频率</option>
                                </select>
                            </div>
                        </div>

                        <!-- 批量操作区域 -->
                        <div class="batch-operations multiselect-bar" id="batchOperations" style="display: none;">
                            <div class="batch-info multiselect-info">
                                已选择 <span id="selectedCount">0</span> 个信号
                            </div>
                            <div class="batch-actions multiselect-actions">
                                <button class="btn small secondary" id="selectAllBtn" data-action="select-all">全选</button>
                                <button class="btn small secondary" id="selectNoneBtn" data-action="select-none">全不选</button>
                                <button class="btn small primary" id="batchSendBtn" data-action="batch-send">批量发射</button>
                                <button class="btn small secondary" id="batchExportBtn" data-action="export-selected">导出选中</button>
                                <button class="btn small danger" id="batchDeleteBtn" data-action="delete-selected">删除选中</button>
                            </div>
                        </div>

                        <!-- 信号列表/网格区域 -->
                        <div class="signals-container">
                            <div class="signals-grid" id="signalsGrid">
                                <!-- 信号卡片将在这里动态生成 -->
                            </div>
                            <div class="signals-list" id="signalsList" style="display: none;">
                                <!-- 信号列表将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 学习状态指示器 -->
                        <div class="learning-indicator" id="learningIndicator" style="display: none;">
                            <div class="learning-content">
                                <div class="learning-animation">
                                    <div class="pulse-ring"></div>
                                    <div class="pulse-ring"></div>
                                    <div class="pulse-ring"></div>
                                </div>
                                <div class="learning-text">
                                    <h3>正在学习信号...</h3>
                                    <p>请对准设备按下遥控器按键</p>
                                    <div class="learning-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="learningProgress"></div>
                                        </div>
                                        <div class="progress-text" id="learningProgressText">0%</div>
                                    </div>
                                </div>
                                <button class="cancel-learning-btn" id="cancelLearningBtn" data-action="cancel-learning">取消学习</button>
                            </div>
                        </div>
                    </div>

                    <!-- 2. 控制面板模块 -->
                    <div class="module-panel" id="control-module">
                        <div class="module-header">
                            <h2>🎮 控制面板</h2>
                            <div class="module-actions">
                                <button class="action-btn" id="taskHistoryBtn" data-action="show-task-history">
                                    <span class="btn-icon">📋</span>
                                    <span class="btn-text">任务历史</span>
                                </button>
                                <button class="action-btn primary" id="createTaskBtn" data-action="create-task">
                                    <span class="btn-icon">➕</span>
                                    <span class="btn-text">创建任务</span>
                                </button>
                            </div>
                        </div>

                        <!-- 控制面板内容将在后续添加 -->
                        <div class="control-content">
                            <div class="control-placeholder">
                                <h3>🎮 控制面板模块</h3>
                                <p>正在开发中...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 3. 定时设置模块 -->
                    <div class="module-panel" id="timer-settings">
                        <div class="module-header">
                            <h2>⏰ 定时设置</h2>
                            <div class="module-actions">
                                <button class="action-btn" id="timerTemplatesBtn" data-action="show-timer-templates">
                                    <span class="btn-icon">📋</span>
                                    <span class="btn-text">模板</span>
                                </button>
                                <button class="action-btn primary" id="createTimerBtn" data-action="create-timer">
                                    <span class="btn-icon">➕</span>
                                    <span class="btn-text">创建定时</span>
                                </button>
                            </div>
                        </div>

                        <!-- 定时设置内容将在后续添加 -->
                        <div class="timer-content">
                            <div class="timer-placeholder">
                                <h3>⏰ 定时设置模块</h3>
                                <p>正在开发中...</p>
                            </div>
                        </div>
                    </div>



                </div>
            </div>
        </main>

        <!-- 全局通知系统 -->
        <div class="notification-container" id="notificationContainer"></div>

        <!-- 全局模态框 -->
        <div class="modal-overlay" id="modalOverlay" style="display: none;">
            <div class="modal-content" id="modalContent">
                <!-- 模态框内容将动态生成 -->
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">R1系统启动中...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/data-validator.js"></script>
    <script src="js/core.js"></script>
    <script src="js/hardware-config.js"></script>

    <!-- 性能优化组件 -->
    <script src="js/dom-update-manager.js"></script>
    <script src="js/optimized-signal-storage.js"></script>
    <script src="js/unified-timer-manager.js"></script>
    <script src="js/virtual-scroll-list.js"></script>
    <script src="js/signal-virtual-list.js"></script>

    <script src="js/signal-manager.js"></script>
    <script src="js/control-module.js"></script>
    <script src="js/timer-settings.js"></script>
    <script src="js/status-display.js"></script>
    <script src="js/system-monitor.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

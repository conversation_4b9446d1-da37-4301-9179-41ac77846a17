/**
 * WebSocket管理器 - 头文件
 * 负责WebSocket连接管理、事件推送、实时通信
 */

#ifndef WEBSOCKET_MANAGER_H
#define WEBSOCKET_MANAGER_H

#include <Arduino.h>
#include <AsyncWebSocket.h>
#include <ArduinoJson.h>
#include "SignalManager.h"

// WebSocket事件类型
enum WSEventType {
    WS_SIGNAL_LEARNED,      // 学习完成事件
    WS_LEARNING_ERROR,      // 学习错误事件
    WS_SIGNAL_SENT,         // 信号发射完成事件
    WS_SIGNAL_FAILED,       // 信号发射失败事件
    WS_SYSTEM_STATUS,       // 系统状态更新事件
    WS_ESP32_CONNECTED,     // ESP32连接事件
    WS_ESP32_DISCONNECTED   // ESP32断开事件
};

// WebSocket消息结构
struct WSMessage {
    String event;           // 事件名称
    JsonDocument data;      // 事件数据
    unsigned long timestamp; // 时间戳
    
    WSMessage(const String& event) : event(event), timestamp(millis()) {}
    
    // 转换为JSON字符串
    String toJsonString() const;
    
    // 创建特定类型的消息
    static WSMessage createSignalLearned(const JsonObject& signalData);
    static WSMessage createLearningError(const String& error);
    static WSMessage createSignalSent(const String& signalId);
    static WSMessage createSignalFailed(const String& signalId, const String& error);
    static WSMessage createSystemStatus(const JsonObject& status);
};

class WebSocketManager {
private:
    AsyncWebSocket* ws;
    SignalManager* signalManager;
    
    // 连接管理
    std::vector<uint32_t> connectedClients;
    unsigned long lastHeartbeat;
    unsigned long heartbeatInterval;
    
    // 消息队列
    std::vector<WSMessage> messageQueue;
    static const size_t MAX_QUEUE_SIZE = 50;
    
    // 统计信息
    unsigned long messagesSent;
    unsigned long messagesReceived;
    unsigned long connectionCount;
    
    // 内部方法
    void handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                             AwsEventType type, void* arg, uint8_t* data, size_t len);
    void handleClientConnect(AsyncWebSocketClient* client);
    void handleClientDisconnect(AsyncWebSocketClient* client);
    void handleClientMessage(AsyncWebSocketClient* client, const String& message);
    void processMessageQueue();
    void sendHeartbeat();
    void cleanupClients();
    
    // 消息处理
    bool parseClientMessage(const String& message, JsonDocument& doc);
    void handleClientRequest(AsyncWebSocketClient* client, const JsonObject& request);
    void sendResponse(AsyncWebSocketClient* client, const String& requestId, 
                     bool success, const JsonDocument& data = JsonDocument());
    
public:
    WebSocketManager(AsyncWebSocket* ws, SignalManager* signalManager);
    ~WebSocketManager();
    
    // 初始化和配置
    bool init();
    void setHeartbeatInterval(unsigned long interval) { heartbeatInterval = interval; }
    
    // 连接管理
    size_t getConnectedClientCount() const { return connectedClients.size(); }
    bool isClientConnected(uint32_t clientId) const;
    void disconnectClient(uint32_t clientId);
    void disconnectAllClients();
    
    // 消息广播
    void broadcast(const WSMessage& message);
    void broadcast(const String& event, const JsonDocument& data = JsonDocument());
    void sendToClient(uint32_t clientId, const WSMessage& message);
    void sendToClient(uint32_t clientId, const String& event, const JsonDocument& data = JsonDocument());
    
    // 特定事件广播
    void broadcastSignalLearned(const JsonObject& signalData);
    void broadcastLearningError(const String& error);
    void broadcastSignalSent(const String& signalId);
    void broadcastSignalFailed(const String& signalId, const String& error);
    void broadcastSystemStatus();
    
    // 消息队列管理
    void queueMessage(const WSMessage& message);
    void processQueue();
    void clearQueue();
    size_t getQueueSize() const { return messageQueue.size(); }
    
    // 心跳和维护
    void handleHeartbeat();
    void cleanup();
    
    // 统计信息
    unsigned long getMessagesSent() const { return messagesSent; }
    unsigned long getMessagesReceived() const { return messagesReceived; }
    unsigned long getConnectionCount() const { return connectionCount; }
    JsonObject getStatistics() const;
    
    // 调试和诊断
    void enableDebug(bool enable = true) { debugEnabled = enable; }
    void printConnectedClients() const;
    void printStatistics() const;
    
private:
    bool debugEnabled;
    bool initialized;
};

#endif // WEBSOCKET_MANAGER_H

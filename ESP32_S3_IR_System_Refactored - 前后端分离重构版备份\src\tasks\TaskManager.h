#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include "../../config/system-config.h"
#include "TaskQueue.h"

// Forward declarations
class DataManager;
class IRController;

/**
 * 任务管理器类
 * 
 * 负责任务的创建、执行、调度和监控
 * 
 * 核心功能：
 * - 任务执行管理
 * - 任务调度
 * - 任务状态监控
 * - 批量操作
 */
class TaskManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param capacity 系统容量配置
     */
    explicit TaskManager(const SystemCapacity& capacity);
    
    /**
     * 析构函数
     */
    ~TaskManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化任务管理器
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭任务管理器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 主循环处理
     */
    void handleLoop();
    
    /**
     * 设置数据管理器依赖
     * @param dataManager 数据管理器指针
     */
    void setDataManager(DataManager* dataManager);
    
    /**
     * 设置红外控制器依赖
     * @param irController 红外控制器指针
     */
    void setIRController(IRController* irController);
    
    // ==================== 任务执行 ====================
    
    /**
     * 执行任务
     * @param taskId 任务ID
     * @return bool 执行是否成功
     */
    bool executeTask(const String& taskId);
    
    /**
     * 停止任务
     * @param taskId 任务ID
     * @return bool 停止是否成功
     */
    bool stopTask(const String& taskId);
    
    /**
     * 暂停任务
     * @param taskId 任务ID
     * @return bool 暂停是否成功
     */
    bool pauseTask(const String& taskId);
    
    /**
     * 恢复任务
     * @param taskId 任务ID
     * @return bool 恢复是否成功
     */
    bool resumeTask(const String& taskId);
    
    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return DynamicJsonDocument 任务状态
     */
    DynamicJsonDocument getTaskStatus(const String& taskId);
    
    // ==================== 批量操作 ====================
    
    /**
     * 执行所有信号
     * @param loopMode 是否循环模式
     * @param interval 信号间隔（毫秒）
     * @return String 任务ID
     */
    String executeAllSignals(bool loopMode = false, int interval = 100);
    
    /**
     * 执行选定信号
     * @param signalIds 信号ID列表
     * @param loopMode 是否循环模式
     * @param interval 信号间隔（毫秒）
     * @return String 任务ID
     */
    String executeSelectedSignals(const std::vector<String>& signalIds, bool loopMode = false, int interval = 100);
    
    /**
     * 停止所有任务
     * @return int 停止的任务数量
     */
    int stopAllTasks();
    
    // ==================== 任务调度 ====================
    
    /**
     * 添加定时任务
     * @param taskData 任务数据
     * @return String 任务ID
     */
    String scheduleTask(const DynamicJsonDocument& taskData);
    
    /**
     * 取消定时任务
     * @param taskId 任务ID
     * @return bool 取消是否成功
     */
    bool cancelScheduledTask(const String& taskId);
    
    /**
     * 获取所有定时任务
     * @return DynamicJsonDocument 定时任务列表
     */
    DynamicJsonDocument getScheduledTasks();
    
    // ==================== 任务监控 ====================
    
    /**
     * 获取当前执行的任务
     * @return DynamicJsonDocument 当前任务信息
     */
    DynamicJsonDocument getCurrentTask();
    
    /**
     * 获取任务队列
     * @return DynamicJsonDocument 任务队列
     */
    DynamicJsonDocument getTaskQueue();
    
    /**
     * 获取任务历史
     * @param limit 限制数量
     * @return DynamicJsonDocument 任务历史
     */
    DynamicJsonDocument getTaskHistory(int limit = 10);
    
    /**
     * 获取任务统计
     * @return DynamicJsonDocument 任务统计
     */
    DynamicJsonDocument getTaskStatistics();

private:
    // ==================== 私有成员变量 ====================
    
    SystemCapacity m_capacity;           // 系统容量配置
    bool m_initialized;                  // 是否已初始化
    DataManager* m_dataManager;          // 数据管理器依赖
    IRController* m_irController;        // 红外控制器依赖
    
    // 任务执行状态
    String m_currentTaskId;              // 当前执行的任务ID
    bool m_isExecuting;                  // 是否正在执行任务
    bool m_isPaused;                     // 是否已暂停
    unsigned long m_taskStartTime;       // 任务开始时间
    
    // 任务队列和历史
    TaskQueue* m_taskQueue;              // 任务队列管理器
    void* m_taskHistory;                 // 任务历史存储
    size_t m_historySize;                // 历史大小
    
    // 统计信息
    unsigned long m_totalTasks;          // 总任务数
    unsigned long m_completedTasks;      // 完成任务数
    unsigned long m_failedTasks;         // 失败任务数
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化存储
     * @return bool 初始化是否成功
     */
    bool initializeStorage();
    
    /**
     * 清理存储
     */
    void cleanupStorage();
    
    /**
     * 执行单个信号
     * @param signalId 信号ID
     * @return bool 执行是否成功
     */
    bool executeSingleSignal(const String& signalId);
    
    /**
     * 处理任务完成
     * @param taskId 任务ID
     * @param success 是否成功
     */
    void handleTaskCompletion(const String& taskId, bool success);
    
    /**
     * 添加到历史记录
     * @param taskData 任务数据
     */
    void addToHistory(const DynamicJsonDocument& taskData);
    
    /**
     * 生成任务ID
     * @return String 任务ID
     */
    String generateTaskId();
    
    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);
    
    /**
     * 更新统计信息
     * @param success 是否成功
     */
    void updateStatistics(bool success);

    // ==================== 任务处理方法 ====================

    /**
     * 处理当前任务
     */
    void processCurrentTask();

    /**
     * 处理任务队列
     */
    void processTaskQueue();

    /**
     * 处理定时任务
     */
    void processScheduledTasks();

    /**
     * 清理已完成的任务
     */
    void cleanupCompletedTasks();

    /**
     * 保存任务状态
     */
    void saveTaskState();

    /**
     * 加载任务状态
     * @return bool 加载是否成功
     */
    bool loadTaskState();

    /**
     * 重置任务状态
     */
    void resetTaskState();

    // ==================== 任务队列管理 ====================

    /**
     * 添加任务到队列
     * @param taskId 任务ID
     * @param priority 任务优先级
     * @return bool 添加是否成功
     */
    bool enqueueTask(const String& taskId, TaskPriority priority = TaskPriority::NORMAL);

    /**
     * 添加定时任务到队列
     * @param taskId 任务ID
     * @param scheduleTime 计划执行时间
     * @param priority 任务优先级
     * @return bool 添加是否成功
     */
    bool enqueueScheduledTask(const String& taskId, unsigned long scheduleTime,
                             TaskPriority priority = TaskPriority::NORMAL);

    /**
     * 从队列中移除任务
     * @param taskId 任务ID
     * @return bool 移除是否成功
     */
    bool removeTaskFromQueue(const String& taskId);

    /**
     * 更新任务优先级
     * @param taskId 任务ID
     * @param newPriority 新优先级
     * @return bool 更新是否成功
     */
    bool updateTaskPriority(const String& taskId, TaskPriority newPriority);

    /**
     * 获取队列中任务的位置
     * @param taskId 任务ID
     * @return int 位置（-1表示不在队列中）
     */
    int getTaskQueuePosition(const String& taskId);

    /**
     * 清空任务队列
     */
    void clearTaskQueue();
};

#endif // TASK_MANAGER_H

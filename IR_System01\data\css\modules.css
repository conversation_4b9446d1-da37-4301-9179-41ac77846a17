/* R1系统 - 模块专用样式 */

/* 搜索和过滤区域 */
.search-filter-area {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
}

.search-box {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.search-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  background: var(--bg-primary);
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--primary-color);
  color: var(--text-white);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.search-btn:hover {
  background: var(--primary-hover);
}

.filter-options {
  display: flex;
  gap: var(--spacing-md);
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  font-size: 0.875rem;
  cursor: pointer;
}

/* ==================== 批量操作区域优化 ==================== */
.batch-operations {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.batch-info {
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.batch-info::before {
  content: "📋";
  font-size: 1rem;
}

.batch-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 批量操作按钮特殊样式 */
.batch-operations .btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  backdrop-filter: blur(10px);
}

.batch-operations .btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.batch-operations .btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border-color: rgba(255, 255, 255, 0.9);
}

.batch-operations .btn.primary:hover:not(:disabled) {
  background: white;
  color: var(--primary-hover);
  border-color: white;
}

.batch-operations .btn.danger {
  background: rgba(231, 76, 60, 0.8);
  color: white;
  border-color: rgba(231, 76, 60, 0.8);
}

.batch-operations .btn.danger:hover:not(:disabled) {
  background: var(--error-color);
  border-color: var(--error-color);
}

/* 兼容旧的batch-btn类 */
.batch-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.batch-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.batch-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
}

/* 信号容器 */
.signals-container {
  min-height: 400px;
}

/* 信号网格视图 */
.signals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.signals-list {
  display: block;
}

.signal-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.signal-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.signal-card.selected {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.signal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.signal-info {
  flex: 1;
}

.signal-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
}

.signal-type {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.signal-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.signal-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.signal-action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.signal-action-btn.send {
  background: var(--success-color);
  color: var(--text-white);
}

.signal-action-btn.send:hover {
  background: #059669;
}

.signal-action-btn.delete {
  background: var(--error-color);
  color: var(--text-white);
}

.signal-action-btn.delete:hover {
  background: #dc2626;
}

.signal-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.signal-frequency {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.signal-created {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 信号列表视图 */
.signals-list {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  margin: 0; /* 确保没有额外边距 */
}

.signal-list-header {
  background: var(--bg-secondary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: grid;
  grid-template-columns: 40px 1fr 100px 120px 100px 120px;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.signal-list-item {
  padding: var(--spacing-md) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: grid;
  grid-template-columns:
    32px          /* 图标 */
    minmax(120px, 1fr)  /* 名称 - 减小最小宽度 */
    100px         /* 信号码 - 减小宽度 */
    60px          /* 协议 - 增加宽度 */
    60px          /* 标签 - 增加宽度 */
    150px         /* 时间 - 增加宽度 */
    200px;        /* 操作按钮 - 适合4个紧凑按钮的宽度 */
  gap: var(--spacing-md);
  align-items: center;
  transition: background-color var(--transition-fast);
  cursor: pointer;
  min-height: 56px;
}

/* 多选模式下调整网格 */
.signal-list-item.multiselect-mode {
  grid-template-columns:
    20px          /* 复选框 */
    32px          /* 图标 */
    minmax(120px, 1fr)  /* 名称 - 减小最小宽度 */
    100px         /* 信号码 - 减小宽度 */
    60px          /* 协议 - 增加宽度 */
    60px          /* 标签 - 增加宽度 */
    150px         /* 时间 - 增加宽度 */
    200px;        /* 操作按钮 - 适合4个紧凑按钮的宽度 */
}

/* 各元素样式 */
.signal-list-item .signal-checkbox {
  justify-self: center;
  margin: 0;
  padding: 0;
}

.signal-list-item .signal-icon {
  justify-self: center;
  font-size: 1.2rem;
  margin: 0;
  padding: 0;
}

.signal-list-item .signal-name {
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.signal-list-item .signal-code {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: white; /* 蓝色背景使用白色文字 */
  background: var(--primary-color);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.signal-list-item .signal-protocol {
  font-size: 0.75rem;
  text-align: center;
  color: var(--text-primary);
  margin: 0;
  padding: 0;
}

.signal-list-item .signal-badge {
  font-size: 0.7rem;
  text-align: center;
  justify-self: center;
  margin: 0;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.signal-list-item .signal-created {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: right;
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.signal-list-item .signal-actions {
  display: flex;
  gap: 4px;                     /* 更紧凑的间距 */
  justify-content: flex-start;  /* 左对齐，避免按钮被拉伸 */
  align-items: center;
  margin: 0;
  padding: 0;
  width: 100%;                  /* 确保容器占满分配的空间 */
}

/* 按钮样式已统一到全局定义，移除重复样式 */

.signal-list-item:hover {
  background: var(--bg-secondary);
}

.signal-list-item.selected {
  background: var(--primary-light);
}

.signal-list-item:last-child {
  border-bottom: none;
}

/* 移除重复的signal-checkbox样式，统一使用ir-signal-checkbox */

.signal-list-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 学习状态指示器 */
.learning-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.learning-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.learning-animation {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto var(--spacing-xl);
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  /* 脉冲环动画 - 必要的学习状态指示 */
  animation: pulse-ring 2s infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse-ring {
  0% {
    width: 40px;
    height: 40px;
    opacity: 1;
  }
  100% {
    width: 120px;
    height: 120px;
    opacity: 0;
  }
}

.learning-text h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.learning-text p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.learning-progress {
  margin-bottom: var(--spacing-lg);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
  width: 0%;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.cancel-learning-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cancel-learning-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 占位符样式 */
.control-placeholder,
.timer-placeholder,
.status-placeholder,
.monitor-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--text-secondary);
}

.control-placeholder h3,
.timer-placeholder h3,
.status-placeholder h3,
.monitor-placeholder h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
}

/* 通知系统 */
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.notification {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  max-width: 400px;
  /* 🔧 暂时禁用动画，避免性能问题 */
  /* animation: slideIn 0.3s ease-out; */
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--error-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification.info {
  border-left: 4px solid var(--info-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ==================== 模态框样式优化 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  transition: opacity var(--transition-normal);
}

.modal-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.modal-overlay[style*="opacity: 1"] .modal-content {
  transform: scale(1);
}

/* 模态框头部 */
.modal-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* 模态框主体 */
.modal-body {
  padding: var(--spacing-xl);
  overflow-y: auto;
  max-height: calc(85vh - 200px);
  flex: 1;
}

/* 模态框底部 */
.modal-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  flex-shrink: 0;
}

/* 表单操作区域 */
.form-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  margin: 0;
}

.form-actions .btn {
  min-width: 80px;
}

/* 定时模块信号选择样式 */
.timer-signal-selection-modal {
  width: 100%;
}

.timer-signal-selection-modal .modal-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.signal-search-bar {
  margin-bottom: var(--spacing-md);
}

.signal-search-bar .search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.signal-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
}

.timer-signal-item {
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-fast);
  cursor: pointer;
  user-select: none;
}

.timer-signal-item:last-child {
  border-bottom: none;
}

.timer-signal-item:hover {
  background-color: var(--bg-secondary);
}

.timer-signal-item.selected {
  background-color: var(--primary-light);
  border-left: 3px solid var(--primary-color);
}

.timer-signal-item.selected:hover {
  background-color: var(--primary-lighter);
}

.signal-info-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  width: 100%;
  min-width: 0;
}



.signal-name {
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.signal-code {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.signal-protocol {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}



.selection-summary {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.selection-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.confirm-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 定时任务状态样式 */
.task-item {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  transition: all var(--transition-fast);
}

.task-item.active {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.task-name {
  font-weight: 600;
  color: var(--text-primary);
}

.task-status {
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.task-status.saved {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.task-status.active {
  background: var(--success-color);
  color: white;
}

.task-details {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.task-details span {
  padding: 2px 6px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

.task-actions {
  margin-top: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-xs);
}

.task-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
  border-radius: var(--radius-sm);
}

.deactivate-btn {
  background: var(--warning-color);
  color: white;
  border: 1px solid var(--warning-color);
}

.deactivate-btn:hover {
  background: var(--warning-dark);
  border-color: var(--warning-dark);
}

/* 间隔禁用提示样式 */
.interval-disabled-hint {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--radius-sm);
}

.interval-disabled-hint small {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 模态框内的表单样式 */
.modal-content .form-group {
  margin-bottom: var(--spacing-md);
}

.modal-content .form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.modal-content .form-input,
.modal-content .form-select,
.modal-content .form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color var(--transition-fast);
}

.modal-content .form-input:focus,
.modal-content .form-select:focus,
.modal-content .form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modal-content .form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 学习信号对话框特定样式 */
.learning-countdown {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.countdown-text {
  font-size: 0.875rem;
  color: var(--info-color);
  font-weight: 500;
  display: block;
  margin-bottom: var(--spacing-xs);
}

.countdown-bar {
  height: 4px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.countdown-fill {
  height: 100%;
  background: var(--info-color);
  width: 0%;
  transition: width 1s linear;
}

.signal-preview {
  margin-bottom: var(--spacing-lg);
}

.signal-preview h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.signal-data {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.data-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.data-item label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-family: var(--font-mono);
  word-break: break-all;
}

.save-info {
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.save-info p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 详情显示样式 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-item label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: var(--text-primary);
  font-size: 0.875rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

/* 导入对话框样式 */
.import-dialog h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.import-options {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.import-options .btn {
  flex: 1;
  justify-content: center;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--bg-secondary);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  /* 🔧 暂时禁用动画，避免性能问题 */
  /* animation: spin 1s linear infinite; */
  margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .signals-grid {
    grid-template-columns: 1fr;
  }

  /* 平板尺寸优化 */
  .signal-list-item {
    grid-template-columns:
      28px          /* 图标 */
      minmax(120px, 1fr)  /* 名称 */
      100px         /* 信号码 */
      40px          /* 标签 */
      100px         /* 时间 */
      auto;         /* 操作 */
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .signal-list-item.multiselect-mode {
    grid-template-columns:
      18px          /* 复选框 */
      28px          /* 图标 */
      minmax(120px, 1fr)  /* 名称 */
      100px         /* 信号码 */
      40px          /* 标签 */
      100px         /* 时间 */
      auto;         /* 操作 */
  }

  .signal-list-item .signal-created {
    font-size: 0.7rem;
  }

  /* 平板端按钮样式已统一，移除重复定义 */
}

/* 手机端优化 */
@media (max-width: 480px) {
  .signal-list-item {
    grid-template-columns:
      32px          /* 图标 */
      1fr           /* 名称 */
      90px          /* 时间 */
      auto;         /* 操作 */
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
  }

  .signal-list-item.multiselect-mode {
    grid-template-columns:
      20px          /* 复选框 */
      28px          /* 图标 */
      1fr           /* 名称 */
      90px          /* 时间 */
      auto;         /* 操作 */
  }

  /* 隐藏次要信息 */
  .signal-list-item .signal-code,
  .signal-list-item .signal-badge {
    display: none;
  }

  .signal-list-item .signal-created {
    font-size: 0.65rem;
  }

  .signal-list-item .signal-actions {
    gap: 2px;
  }

  /* 手机端按钮样式 - 保持紧凑但统一 */
  .signal-card .signal-actions .btn.small,
  .signal-list-item .signal-actions .btn.small {
    padding: 2px 6px;
    font-size: 0.7rem;
    min-width: 40px;
    height: 24px;
  }

  /* 手机端按钮图标样式 */
  .signal-card .signal-actions .btn.small .btn-icon,
  .signal-list-item .signal-actions .btn.small .btn-icon {
    font-size: 0.75rem;  /* 手机端稍小的图标 */
  }

  .notification-container {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
  }

  .notification {
    min-width: auto;
    max-width: none;
  }

  .status-overview-cards {
    grid-template-columns: 1fr;
  }

  .performance-charts {
    grid-template-columns: 1fr;
  }
}

/* ===== 侧边栏状态显示样式 ===== */
.sidebar-status-display,
.sidebar-system-monitor {
}

/* 状态点的状态样式 - 通过JavaScript动态设置 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 合并后的当前任务样式 */
.current-task-status {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
}

.current-task-status h4 {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 600;
}

/* 任务基本信息 */
.task-info {
  margin-bottom: var(--spacing-sm);
}

.task-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-details {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 当前信号信息 */
.current-signal-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  font-size: 0.75rem;
}

.signal-label {
  color: var(--text-secondary);
  flex-shrink: 0;
}

.signal-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
}

.signal-content .signal-name {
  font-weight: 600;
  color: var(--text-primary);
}

.signal-content .signal-type {
  color: var(--text-secondary);
  font-style: italic;
}

.signal-content .signal-state {
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 500;
}

.signal-state.learning {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.signal-state.transmitting {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.signal-state.paused {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.signal-state.ready {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.signal-state.idle {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-secondary);
}

/* 进度条样式 */
.task-progress {
}

.progress-bar {
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: center;
}

/* 待发射列表 */
.pending-signals {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-xs);
}

.pending-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.pending-list {
  font-size: 0.75rem;
  color: var(--text-primary);
  line-height: 1.4;
  word-break: break-word;
}

.sidebar-status-display h3,
.sidebar-system-monitor h3 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-color);
}

.sidebar-status-content,
.sidebar-monitor-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 系统状态指示器 */
.system-status-indicator {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.status-dot.idle {
  background: var(--text-secondary);
}

.status-dot.active {
  background: var(--success-color);
}

.status-dot.connected {
  background: var(--success-color);
}

.status-dot.disconnected {
  background: var(--error-color);
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.status-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

/* 快速统计网格 */
.quick-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: var(--hover-bg);
  transform: translateY(-1px);
}

.stat-icon {
  font-size: 1rem;
}

.stat-content {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  line-height: 1;
}

/* 信号状态区域 */
.signal-status-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.signal-status-info {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.current-signal {
  margin-bottom: var(--spacing-sm);
}

.signal-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.signal-state {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.signal-state.learning {
  color: var(--warning-color);
}

.signal-state.sending {
  color: var(--primary-color);
}

.signal-state.idle {
  color: var(--text-secondary);
}

.signal-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.signal-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.signal-stats .stat-label {
  color: var(--text-secondary);
}

.signal-stats .stat-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* 快速操作区域 */
.quick-actions-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.quick-action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  background: var(--hover-bg);
  transform: translateY(-1px);
}



.quick-action-btn.primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.quick-action-btn.primary:hover {
  background: var(--primary-color);
  color: white;
}

.quick-action-btn.secondary {
  border-color: var(--text-secondary);
  color: var(--text-secondary);
}

.quick-action-btn.secondary:hover {
  background: var(--text-secondary);
  color: white;
}

/* 使用全局按钮样式，移除重复定义 */

/* 运行时间区域 */
.uptime-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  text-align: center;
}

.uptime-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.uptime-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
}

/* ===== 侧边栏系统监控样式 ===== */

/* 系统健康状态 */
.system-health-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.health-indicators {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.health-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.health-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: background-color 0.2s ease;
}

.health-dot.success {
  background: var(--success-color);
}

.health-dot.warning {
  background: var(--warning-color);
}

.health-dot.error {
  background: var(--error-color);
}

.health-info {
  flex: 1;
}

.health-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.health-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

/* 性能指标区域 */
.performance-metrics-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.metric-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
  text-align: center;
}

.metric-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.metric-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  line-height: 1;
}

/* 最新日志区域 */
.recent-logs-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.recent-logs-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  max-height: 120px;
  overflow-y: auto;
}

.recent-log-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
  font-size: 0.75rem;
}

.recent-log-item.error {
  border-left: 3px solid var(--error-color);
}

.recent-log-item.warn {
  border-left: 3px solid var(--warning-color);
}

.recent-log-item.success {
  border-left: 3px solid var(--success-color);
}

.recent-log-item.info {
  border-left: 3px solid var(--info-color);
}

.log-time {
  color: var(--text-secondary);
  font-size: 0.7rem;
  margin-bottom: var(--spacing-xs);
}

.log-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.log-icon {
  font-size: 0.8rem;
}

.log-message {
  flex: 1;
  color: var(--text-primary);
  line-height: 1.2;
}

.no-logs {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.75rem;
  padding: var(--spacing-md);
}

/* 日志统计区域 */
.log-stats-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.log-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xs);
}

.log-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
}

.log-stat-item.error {
  border-left: 3px solid var(--error-color);
}

.log-stat-item.warning {
  border-left: 3px solid var(--warning-color);
}

.log-stat-item.success {
  border-left: 3px solid var(--success-color);
}

.log-stat-item .stat-icon {
  font-size: 0.9rem;
}

.log-stat-item .stat-content {
  flex: 1;
  text-align: center;
}

.log-stat-item .stat-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.log-stat-item .stat-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  line-height: 1;
}

/* 监控操作区域 */
.monitor-actions-section {
  margin-top: var(--spacing-sm);
}

.monitor-action-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.monitor-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.monitor-action-btn:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}

/* 使用全局按钮样式，移除重复定义 */

/* ===== 新的状态显示模块样式 ===== */
.status-display-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
}



/* 系统状态显示 */
.system-status-display {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: var(--spacing-xs);
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: background-color 0.2s ease;
}

.status-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

.status-dot.status-idle {
  background: var(--text-secondary);
}

.status-dot.status-transmitting {
  background: var(--primary-color);
  /* 发射状态动画 - 必要的状态指示 */
  animation: pulse 1s infinite;
}

.status-dot.status-paused {
  background: var(--warning-color);
}

.status-dot.status-learning {
  background: var(--info-color);
  /* 学习状态动画 - 必要的状态指示 */
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}



.status-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.status-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

/* 当前任务状态 */
.current-task-status {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.current-task-status h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.task-info {
  margin-bottom: var(--spacing-sm);
}

.task-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.task-details {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.task-progress {
  margin-top: var(--spacing-sm);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-align: center;
}

/* 当前信号状态 */
.current-signal-status {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.current-signal-status h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.signal-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.signal-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.signal-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.signal-type {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.signal-state {
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
}

.signal-state.idle {
  color: var(--text-secondary);
}

.signal-state.learning {
  color: var(--info-color);
}

.signal-state.transmitting {
  color: var(--primary-color);
}

.signal-state.paused {
  color: var(--warning-color);
}

.signal-state.ready {
  color: var(--success-color);
}

/* 信号队列状态 */
.signal-queue-status {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.signal-queue-status h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.queue-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.queue-count,
.queue-next {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.queue-label,
.next-label {
  color: var(--text-secondary);
}

.queue-value,
.next-signal {
  font-weight: 600;
  color: var(--text-primary);
}

/* 学习状态 */
.learning-status {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.learning-status h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.learning-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
}

.learning-state {
  font-size: 0.875rem;
  font-weight: 600;
  padding: var(--spacing-xs);
  border-radius: var(--radius-xs);
}

.learning-state.idle {
  color: var(--text-secondary);
  background: var(--bg-color);
}

.learning-state.active {
  color: var(--info-color);
  background: rgba(59, 130, 246, 0.1);
  /* 学习状态文字动画 - 必要的状态指示 */
  animation: pulse 1s infinite;
}

.learning-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.learned-label {
  color: var(--text-secondary);
}

.learned-count {
  font-weight: 600;
  color: var(--primary-color);
}

/* 统计信息显示 */
.statistics-display {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.statistics-display h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-xs);
  background: var(--bg-color);
  border-radius: var(--radius-xs);
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  line-height: 1;
}

/* 网络状态显示 */
.network-status-display {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.network-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.network-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: background-color 0.2s ease;
}

.network-dot.connected {
  background: var(--success-color);
}

.network-dot.disconnected {
  background: var(--error-color);
}

.network-text {
  flex: 1;
}

.network-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.network-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

/* ===== 新的系统监控模块样式 ===== */
.system-monitor-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
}

/* 日志统计和过滤器 */
.log-statistics {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.log-stats-bar {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: flex-start;
  min-height: 20px;
}

.log-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: var(--spacing-xs);
  border-left: 3px solid transparent;
}

.log-stat-item.all {
  border-left-color: var(--text-secondary);
}

.log-stat-item.system {
  border-left-color: var(--info-color);
}

.log-stat-item.error {
  border-left-color: var(--error-color);
}

.log-stat-item.warning {
  border-left-color: var(--warning-color);
}

.log-stat-item.success {
  border-left-color: var(--success-color);
}

.log-stat-item:hover .stat-count,
.log-stat-item:hover .stat-label {
  color: var(--primary-color);
}

.log-stat-item.active .stat-count,
.log-stat-item.active .stat-label {
  color: var(--primary-color);
  font-weight: 700;
}

.stat-info {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.stat-count {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

/* 日志显示区域 */
.logs-display {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  flex: 1;
  min-height: 0;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  height: 199px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.logs-list::-webkit-scrollbar {
  width: 6px;
}

.logs-list::-webkit-scrollbar-track {
  background: var(--bg-color);
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.log-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs);
  border-left: 3px solid transparent;
  background: var(--bg-color);
  margin-bottom: 1px;
  height: 24px;
  box-sizing: border-box;
}

.log-item * {
  font-size: 0.75rem;
  line-height: 1;
  vertical-align: middle;
  margin: 0;
  padding: 0;
}

.log-item.error {
  border-left-color: var(--error-color);
}

.log-item.warn {
  border-left-color: var(--warning-color);
}

.log-item.success {
  border-left-color: var(--success-color);
}

.log-item.info {
  border-left-color: var(--info-color);
}

.log-time {
  color: var(--text-secondary);
  margin-right: var(--spacing-sm) !important;
  min-width: 50px;
  flex-shrink: 0;
}

.log-icon {
  margin-right: var(--spacing-xs) !important;
  flex-shrink: 0;
  width: 16px;
  text-align: center;
}

.log-text {
  flex: 1;
  color: var(--text-primary);
}

.no-logs {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.7rem;
  padding: var(--spacing-md);
}

/* 系统资源监控 - 横向两行布局 */
.system-runtime-info {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.runtime-info-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.runtime-labels-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.runtime-values-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.runtime-labels-row .info-label {
  font-size: 0.65rem;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.runtime-values-row .info-value {
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  flex: 1;
  text-align: center;
  transition: color 0.3s ease;
}

/* 资源使用率颜色状态 */
.info-value.success {
  color: var(--success-color);
}

.info-value.warning {
  color: var(--warning-color);
}

.info-value.danger {
  color: var(--error-color);
}



/* 最新消息列表 */
.recent-messages-list {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.recent-messages-list h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  max-height: 100px;
  overflow-y: auto;
}

.message-item {
  background: var(--bg-color);
  border-radius: var(--radius-xs);
  padding: var(--spacing-xs);
  font-size: 0.75rem;
}

.message-item.error {
  border-left: 3px solid var(--error-color);
}

.message-item.warn {
  border-left: 3px solid var(--warning-color);
}

.message-item.success {
  border-left: 3px solid var(--success-color);
}

.message-item.info {
  border-left: 3px solid var(--info-color);
}

.message-time {
  color: var(--text-secondary);
  font-size: 0.7rem;
  margin-bottom: var(--spacing-xs);
}

.message-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.message-icon {
  font-size: 0.8rem;
}

.message-text {
  flex: 1;
  color: var(--text-primary);
  line-height: 1.2;
}

.no-messages {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.75rem;
  padding: var(--spacing-md);
}

/* 性能监控 */
.performance-monitor {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.performance-monitor h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.perf-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.perf-stat-item .stat-label {
  color: var(--text-secondary);
}

.perf-stat-item .stat-value {
  font-weight: 600;
  color: var(--primary-color);
}

/* 运行时监控 */
.runtime-monitor {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.runtime-monitor h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.runtime-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.runtime-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.runtime-stat-item .stat-label {
  color: var(--text-secondary);
}

.runtime-stat-item .stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* 错误追踪 */
.error-tracking {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.error-tracking h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.error-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.error-count-display {
  text-align: center;
  padding: var(--spacing-sm);
  background: var(--bg-color);
  border-radius: var(--radius-xs);
}

.error-count {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--error-color);
  line-height: 1;
}

.error-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  line-height: 1;
}

.last-error-info {
  text-align: center;
}

.last-error-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.last-error-message {
  font-size: 0.75rem;
  color: var(--text-primary);
  line-height: 1.2;
}



/* ===== 状态显示主面板样式 ===== */
.status-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.status-overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.status-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.card-icon {
  font-size: 1.5rem;
}

.card-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.card-content {
  text-align: center;
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.card-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.performance-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.chart-container {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.chart-container h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.progress-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.progress-fill.success {
  background: var(--success-color);
}

.progress-fill.warning {
  background: var(--warning-color);
}

.progress-fill.danger {
  background: var(--error-color);
}

.uptime-display {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  text-align: center;
  font-family: 'Courier New', monospace;
}

.realtime-status {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.realtime-status h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}



/* ===== 系统监控主面板样式 ===== */
.monitor-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.metric-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.metric-icon {
  font-size: 1.5rem;
}

.metric-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.log-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.filter-select,
.filter-input {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.filter-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-left: auto;
}

.filter-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: var(--primary-hover);
}

.logs-container {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.logs-header {
  display: grid;
  grid-template-columns: 100px 80px 120px 1fr;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.logs-list {
  max-height: 400px;
  overflow-y: auto;
}

.log-entry {
  display: grid;
  grid-template-columns: 100px 80px 120px 1fr;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.log-entry:hover {
  background: var(--hover-bg);
}

.log-entry.error {
  border-left: 3px solid var(--error-color);
}

.log-entry.warn {
  border-left: 3px solid var(--warning-color);
}

.log-entry.success {
  border-left: 3px solid var(--success-color);
}

.log-entry.info {
  border-left: 3px solid var(--info-color);
}

.log-entry.debug {
  border-left: 3px solid var(--text-secondary);
}

.log-col-time {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

.log-col-level {
  display: flex;
  align-items: center;
}

.log-level-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
}

.log-col-category {
  color: var(--text-primary);
  font-weight: 500;
}

.log-col-message {
  color: var(--text-primary);
  word-break: break-word;
}

/* 旧系统R_1风格的信号项样式 - 类似Windows文件夹对齐 */
.ir-signal-item {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-md);
  background: var(--bg-primary);
  transition: all var(--transition-fast);
  min-height: 60px;
  gap: var(--spacing-sm);
}

/* 多选模式下调整网格布局 */
.ir-signal-item.multiselect-mode {
  grid-template-columns: 24px 1fr auto auto;
}

/* 非多选模式下隐藏复选框列 */
.ir-signal-item:not(.multiselect-mode) .ir-signal-checkbox {
  display: none;
}

.ir-signal-item:last-child {
  border-bottom: none;
}

.ir-signal-item:hover {
  background: var(--bg-secondary);
}

.ir-signal-item.selected {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.ir-signal-checkbox {
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.ir-signal-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.ir-signal-info {
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ir-signal-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  font-size: 0.9rem;
  line-height: 1.2;
}

.ir-signal-details {
  display: flex;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.2;
}

.ir-signal-frequency {
  min-width: 80px;
  text-align: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.ir-signal-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
  min-width: 120px;
}

/* 信号列表容器 */
.ir-signal-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
}

/* 列表视图和网格视图的区别 */
.signals-list .ir-signal-item {
  /* 列表视图：紧凑的行式布局 */
  padding: var(--spacing-sm) var(--spacing-md);
  min-height: 50px;
  border-bottom: 1px solid var(--border-color);
}

.signals-grid .signal-card {
  /* 网格视图：卡片式布局 */
  padding: var(--spacing-lg);
  min-height: 160px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
}

/* 确保边距一致性 */
.signals-list .ir-signal-item:first-child,
.signals-grid .signal-card:first-child {
  margin-top: 0;
}

.signals-list .ir-signal-item:last-child {
  border-bottom: none;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.empty-hint {
  margin-bottom: var(--spacing-lg);
  font-size: 0.9rem;
}

/* ==================== 多选操作栏样式优化 ==================== */
.multiselect-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  color: white;
}

.multiselect-info {
  font-weight: 600;
  color: white;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.multiselect-info::before {
  content: "✓";
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multiselect-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 多选操作栏中的按钮样式 */
.multiselect-bar .btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  backdrop-filter: blur(10px);
}

.multiselect-bar .btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.multiselect-bar .btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border-color: rgba(255, 255, 255, 0.9);
}

.multiselect-bar .btn.primary:hover:not(:disabled) {
  background: white;
  color: var(--primary-hover);
  border-color: white;
}

.multiselect-bar .btn.danger {
  background: rgba(231, 76, 60, 0.8);
  color: white;
  border-color: rgba(231, 76, 60, 0.8);
}

.multiselect-bar .btn.danger:hover:not(:disabled) {
  background: var(--error-color);
  border-color: var(--error-color);
}

/* 导入对话框样式 */
.import-dialog {
  max-width: 600px;
  width: 100%;
}

.import-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.import-method {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
}

.import-method h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.import-method .form-input {
  margin-bottom: var(--spacing-md);
}

/* 文件导入区域样式 */
.import-file-area {
  padding: var(--spacing-md);
}

.import-file-area .form-help {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 文本导入区域样式 */
.import-text-area {
  padding: var(--spacing-md);
}

.import-text-area textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
}

.import-text-area .form-help {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 导入预览样式 */
.import-preview {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.import-preview h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.import-summary {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-sm);
  text-align: center;
}

.import-signals-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
}

.import-signal-item {
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import-signal-item:last-child {
  border-bottom: none;
}

.signal-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.signal-code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  color: var(--primary-color);
  font-weight: 600;
}

.signal-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.signal-name {
  font-weight: 600;
  color: var(--text-primary);
}

.signal-protocol {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
}

.signal-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表单帮助文本样式 */
.form-help {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

/* 只读输入框样式 */
.form-input[readonly] {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.form-input[disabled] {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* 信号卡片新增样式 */
.signal-code-info {
  display: flex;
  gap: var(--spacing-sm);
  margin: var(--spacing-sm) 0;
  align-items: center;
}

.signal-code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.75rem;
  background: var(--primary-color);
  color: white;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signal-protocol {
  font-size: 0.7rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  font-weight: 500;
}

.signal-badge {
  font-size: 0.65rem;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signal-badge.learned {
  background: var(--success-color);
  color: white;
}

.signal-badge.manual {
  background: var(--warning-color);
  color: white;
}

/* 信号详情显示样式 */
.signal-code-display {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  background: var(--bg-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  color: var(--primary-color);
  font-weight: 600;
}

.signal-protocol-display {
  font-size: 0.875rem;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  font-weight: 500;
}

.signal-source {
  font-weight: 500;
}

/* 信号列表项新增样式 */
.signal-list-item .signal-code {
  font-size: 0.75rem;
  min-width: 80px;
}

.signal-list-item .signal-protocol {
  font-size: 0.7rem;
  min-width: 40px;
  text-align: center;
}

.signal-list-item .signal-badge {
  font-size: 0.65rem;
  min-width: 35px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .signal-code-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .signal-list-item .signal-code,
  .signal-list-item .signal-protocol {
    min-width: auto;
    font-size: 0.7rem;
  }
}

/* 小按钮样式 */
.btn.small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.8rem;
  line-height: 1.2;
}

/* 网格视图的信号卡片样式 */
.signal-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  min-height: 160px;
}

.signal-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.signal-card.selected {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.signal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.signal-card .signal-info {
  flex: 1;
}

.signal-card .signal-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.signal-card .signal-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.signal-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.signal-meta > div {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.signal-card .signal-actions {
  display: flex;
  gap: 4px;                     /* 与列表视图保持一致的间距 */
  justify-content: flex-end;
  margin-top: auto;
}

/* 统一网格和列表视图的按钮样式 - 继承全局.btn.small样式 */
.signal-card .signal-actions .btn.small,
.signal-list-item .signal-actions .btn.small {
  /* 保持全局.btn.small的基础样式，只调整必要的属性 */
  min-width: 44px;      /* 最小宽度，允许根据内容调整 */
  height: 28px;         /* 保持标准高度 */
  margin: 0;
  flex-shrink: 0;       /* 防止按钮被压缩 */
  white-space: nowrap;  /* 防止文字换行 */
}

/* 确保按钮图标大小一致 */
.signal-card .signal-actions .btn.small .btn-icon,
.signal-list-item .signal-actions .btn.small .btn-icon {
  font-size: 0.875rem;  /* 与全局.btn.small .btn-icon保持一致 */
}

/* 网格卡片的复选框样式 */
.signal-card .signal-checkbox {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-sm);
  padding: 2px;
}

.signal-card.multiselect-mode {
  padding-left: calc(var(--spacing-lg) + 24px); /* 为复选框让出空间 */
}

/* ==================== 控制面板模块样式 ==================== */

.control-panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.control-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.control-section h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 控制按钮样式 */
.control-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--button-bg);
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.primary-btn {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.control-btn.primary-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.control-btn.secondary-btn {
  background: var(--button-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.control-btn.secondary-btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
}

.control-btn.danger-btn {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.control-btn.danger-btn:hover:not(:disabled) {
  background: #dc2626;
}

.control-btn.active {
  background: var(--info-color);
  color: white;
  border-color: var(--info-color);
}

/* 重复的按钮样式已移除，使用main.css中的全局定义 */

/* 速率控制样式 */
.rate-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rate-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.rate-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.rate-slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
}

.rate-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
}

.rate-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
}

/* 模式控制样式 */
.mode-controls {
  display: flex;
  gap: 16px;
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-secondary);
}

.radio-group input[type="radio"] {
  margin: 0;
  accent-color: var(--primary-color);
}

.radio-group:hover {
  color: var(--text-primary);
}

/* 状态显示样式 */
.status-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.status-value {
  color: var(--text-primary);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  background: var(--bg-secondary);
}

.status-value.status-idle {
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

.status-value.status-transmitting {
  color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
  /* 发射状态动画 - 必要的状态指示 */
  animation: pulse 1s infinite;
}

.status-value.status-paused {
  color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
}

.status-value.status-learning {
  color: var(--info-color);
  background: rgba(59, 130, 246, 0.1);
  /* 学习状态动画 - 必要的状态指示 */
  animation: pulse 1s infinite;
}

/* 进度条样式 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: var(--text-secondary);
  min-width: 30px;
  text-align: right;
}

/* ==================== 定时器设置模块样式 ==================== */

.timer-panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.timer-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.timer-section h3,
.timer-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 定时器头部和主开关 */
.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.timer-master-switch {
  display: flex;
  align-items: center;
}

.switch-group {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-secondary);
}

.switch-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background: var(--border-color);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.switch-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.switch-group input[type="checkbox"]:checked + .switch-slider {
  background: var(--primary-color);
}

.switch-group input[type="checkbox"]:checked + .switch-slider::before {
  transform: translateX(20px);
}

.switch-group input[type="checkbox"] {
  display: none;
}

.switch-label {
  font-weight: 500;
  color: var(--text-primary);
}

/* 基础设置样式 */
.timer-basic-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.time-group,
.interval-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.time-group label,
.interval-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.time-input,
.interval-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 12px;
}

.time-input:focus,
.interval-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.checkbox-group {
  display: flex;
  align-items: center;
  grid-column: span 2;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  accent-color: var(--primary-color);
}

.checkbox-label:hover {
  color: var(--text-primary);
}

/* 信号选择样式 */
.signal-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selected-signals-display {
  min-height: 60px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-secondary);
}

.hint-text {
  color: var(--text-secondary);
  font-size: 12px;
  margin: 0;
  text-align: center;
  line-height: 36px;
}

.selected-signals-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-count {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.signal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.signal-tag {
  padding: 4px 8px;
  background: var(--primary-color);
  color: white;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

/* 重复的样式定义已移除，使用统一的signal-actions样式 */

/* 定时器按钮样式 */
.timer-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.timer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--button-bg);
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timer-btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
}

.timer-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.timer-btn.primary-btn {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.timer-btn.primary-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.timer-btn.secondary-btn {
  background: var(--button-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.timer-btn.secondary-btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
}

/* 保存的任务样式 */
.saved-tasks {
  max-height: 300px;
  overflow-y: auto;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-bg);
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.task-details {
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 6px;
}

.task-btn {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--button-bg);
  color: var(--text-primary);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-btn:hover {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
}

.task-btn.edit-btn {
  background: var(--info-color);
  color: white;
  border-color: var(--info-color);
}

.task-btn.activate-btn {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.task-btn.delete-btn {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

/* 执行状态样式 */
.timer-status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.status-value {
  color: var(--text-primary);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  background: var(--bg-secondary);
}

/* 定时器禁用状态样式 */
.timer-section.disabled {
  opacity: 0.5;
  pointer-events: none;
  background: var(--bg-secondary);
}

.timer-section.disabled * {
  color: var(--text-secondary) !important;
}

.task-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.task-item.disabled .task-btn {
  background: var(--bg-secondary) !important;
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
  cursor: not-allowed !important;
}

.timer-btn.disabled,
.timer-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-secondary) !important;
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.timer-btn.disabled:hover,
.timer-btn:disabled:hover {
  background: var(--bg-secondary) !important;
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 输入控件禁用状态 */
.timer-basic-settings input:disabled,
.timer-basic-settings select:disabled {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 保存格式选择样式 */
.save-format-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-label:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
}

.radio-label input[type="radio"] {
  margin: 0;
}

.radio-label input[type="radio"]:checked + span {
  color: var(--primary-color);
  font-weight: 500;
}

.radio-label:has(input[type="radio"]:checked) {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--primary-color);
}

/* 学习指示器旋转动画 */
.learning-spinner {
  margin: 20px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-left: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 增强版信号学习样式 */
.learning-countdown {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 4px;
}

.countdown-text {
  font-size: 12px;
  color: #856404;
  font-weight: 500;
}

.countdown-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.countdown-fill {
  height: 100%;
  background: #ffc107;
  width: 0%;
  transition: width 1s linear;
}

.signal-preview {
  margin-bottom: 20px;
  padding: 15px;
  background: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.signal-preview h4 {
  margin: 0 0 12px 0;
  color: var(--primary-color);
  font-size: 14px;
}

.signal-data {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.data-item:last-child {
  border-bottom: none;
}

.data-item label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 12px;
}

.data-value {
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
}

.recovery-dialog {
  text-align: center;
}

.recovery-dialog p {
  margin-bottom: 15px;
  color: var(--text-secondary);
}

.signal-preview {
  margin: 15px 0;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 6px;
  text-align: left;
  font-size: 12px;
  line-height: 1.4;
}

.recovery-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

/* 学习状态指示器增强 */
.learning-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.learning-content {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.learning-content h3 {
  margin: 0 0 10px 0;
  color: var(--primary-color);
}

.learning-content p {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
}

.cancel-learning-btn {
  margin-top: 20px;
  padding: 8px 16px;
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.cancel-learning-btn:hover {
  background: #dc2626;
}

/* 学习按钮状态切换样式 */
#learnSignalBtn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

#learnSignalBtn.danger {
  background: var(--error-color);
  border-color: var(--error-color);
  color: white;
  animation: pulse-danger 2s infinite;
}

#learnSignalBtn.danger:hover {
  background: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

#learnSignalBtn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

#learnSignalBtn.primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 学习按钮图标动画 */
#learnBtnIcon {
  transition: transform 0.3s ease;
  display: inline-block;
}

#learnSignalBtn.danger #learnBtnIcon {
  animation: stop-icon-pulse 1.5s infinite;
}

#learnSignalBtn.primary #learnBtnIcon {
  animation: search-icon-bounce 2s infinite;
}

/* 按钮动画效果 */
@keyframes pulse-danger {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
}

@keyframes stop-icon-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes search-icon-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timer-basic-settings {
    grid-template-columns: 1fr;
  }

  .checkbox-group {
    grid-column: span 1;
  }

  .timer-controls {
    grid-template-columns: 1fr;
  }

  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .task-actions {
    align-self: stretch;
    justify-content: flex-end;
  }

  /* 设置窗口响应式 */
  .setting-grid {
    grid-template-columns: 1fr;
  }

  .system-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .setting-option {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .option-control {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* ==================== 系统设置窗口样式 ==================== */

/* 设置内容区域 */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 设置组 */
.setting-group {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-primary);
}

.setting-group-header {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.setting-group-header h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.setting-group-desc {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 设置网格布局 */
.setting-grid {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* 设置选项列表 */
.setting-options {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.setting-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  transition: all var(--transition-fast);
}

.setting-option:hover {
  border-color: var(--primary-color);
  background: var(--bg-primary);
}

.option-info {
  flex: 1;
}

.option-info label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
}

.option-info small {
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.3;
}

.option-control {
  flex-shrink: 0;
  margin-left: var(--spacing-md);
}

/* 设置窗口专用开关样式 */
.settings-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.settings-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.settings-switch-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--border-color);
  border-radius: 24px;
  transition: all var(--transition-fast);
}

.settings-switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: white;
  border-radius: 50%;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.settings-switch input:checked + .settings-switch-slider {
  background: var(--primary-color);
}

.settings-switch input:checked + .settings-switch-slider:before {
  transform: translateX(24px);
}

.settings-switch:hover .settings-switch-slider {
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* 系统信息网格 */
.system-info-grid {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  text-align: center;
  transition: all var(--transition-fast);
}

.info-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.info-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.info-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: var(--font-mono);
}

/* 表单帮助文本 */
.form-help {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--text-muted);
  line-height: 1.3;
}

/* 移除重复的signal-checkbox样式 */

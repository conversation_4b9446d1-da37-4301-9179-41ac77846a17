/*
 * WebSocket Manager Header - ESP32-S3 IR Control System
 * Fully compatible with ESPAsyncWebServer 3.7.8 API
 * Handles real-time communication between frontend and backend
 */

#ifndef WEBSOCKET_MANAGER_H
#define WEBSOCKET_MANAGER_H

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <vector>
#include <functional>

// Client Information Structure
struct WSClient {
    uint32_t id;
    String ip;
    unsigned long connectedAt;
    unsigned long lastPing;
    bool isActive;
    
    WSClient() : id(0), connectedAt(0), lastPing(0), isActive(false) {}
};

// Message Queue Structure
struct QueuedMessage {
    String type;
    String payload;
    uint32_t targetClientId;  // 0 = broadcast
    unsigned long timestamp;
    
    QueuedMessage() : targetClientId(0), timestamp(0) {}
};

class WebSocketManager {
public:
    // Constructor and Destructor
    WebSocketManager();
    ~WebSocketManager();
    
    // Core Methods
    bool initialize(AsyncWebSocket* webSocket);
    void handleLoop();
    
    // Message Sending (Frontend API Matching)
    bool broadcastMessage(const String& type, const DynamicJsonDocument& payload);
    bool sendToClient(uint32_t clientId, const String& type, const DynamicJsonDocument& payload);
    bool queueMessage(const String& type, const DynamicJsonDocument& payload, uint32_t clientId = 0);
    
    // Client Management
    int getClientCount() const;
    std::vector<WSClient> getActiveClients() const;
    bool isClientConnected(uint32_t clientId) const;
    void disconnectClient(uint32_t clientId);
    void disconnectAllClients();
    
    // Utility Methods
    void pingAllClients();
    void pingClient(uint32_t clientId);
    DynamicJsonDocument getStatistics() const;
    
    // System Status Broadcasting (Frontend Matching)
    void sendSystemStatus();
    void sendClientList();
    void sendErrorMessage(uint32_t clientId, const String& error);
    void sendNotification(const String& message, const String& level = "info");
    
    // Event Callbacks
    void setOnClientConnected(std::function<void(uint32_t, String)> callback);
    void setOnClientDisconnected(std::function<void(uint32_t)> callback);
    void setOnTextMessage(std::function<void(uint32_t, String)> callback);
    void setOnJsonMessage(std::function<void(uint32_t, DynamicJsonDocument)> callback);

private:
    // WebSocket Instance
    AsyncWebSocket* ws;
    
    // Client Management
    std::vector<WSClient> clients;
    std::vector<QueuedMessage> messageQueue;
    
    // Statistics
    unsigned long totalConnections;
    unsigned long totalMessages;
    unsigned long totalErrors;
    
    // Constants
    static const size_t MAX_CLIENTS = 10;
    static const size_t MAX_QUEUE_SIZE = 50;
    static const unsigned long CLIENT_TIMEOUT = 300000; // 5 minutes
    
    // Event Callbacks
    std::function<void(uint32_t, String)> onClientConnected;
    std::function<void(uint32_t)> onClientDisconnected;
    std::function<void(uint32_t, String)> onTextMessage;
    std::function<void(uint32_t, DynamicJsonDocument)> onJsonMessage;
    
    // WebSocket Event Handling (ESPAsyncWebServer 3.7.8 Compatible)
    void handleWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, 
                             AwsEventType type, void *arg, uint8_t *data, size_t len);
    
    // Event Handlers
    void onClientConnect(AsyncWebSocketClient* client);
    void onClientDisconnect(AsyncWebSocketClient* client);
    void onClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    void onClientError(AsyncWebSocketClient* client, const String& error);
    
    // Client Management
    bool addClient(uint32_t id, const String& ip);
    bool removeClient(uint32_t id);
    WSClient* findClient(uint32_t id);
    void cleanupInactiveClients();
    
    // Message Processing
    String createWebSocketMessage(const String& type, const DynamicJsonDocument& payload);
    bool validateMessage(const DynamicJsonDocument& message);
    void processIncomingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    // Message Handlers
    void handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload);
    void handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload);
    void handleUnsubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload);
};

#endif // WEBSOCKET_MANAGER_H

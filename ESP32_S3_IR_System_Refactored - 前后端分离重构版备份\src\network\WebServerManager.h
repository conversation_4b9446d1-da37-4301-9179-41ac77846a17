#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "../../config/system-config.h"
#include "../../config/network-config.h"

// Forward declarations
class DataManager;
class IRController;
class TaskManager;
class WSManager;
class NetworkSecurity;

/**
 * Web服务器管理器类
 * 
 * 负责HTTP服务器的管理和API路由
 * 
 * 核心功能：
 * - HTTP服务器管理
 * - API路由处理
 * - 静态文件服务
 * - 请求处理
 */
class WebServerManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     */
    WebServerManager();
    
    /**
     * 析构函数
     */
    ~WebServerManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化Web服务器
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭Web服务器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 主循环处理
     */
    void handleLoop();
    
    // ==================== 依赖设置 ====================
    
    /**
     * 设置数据管理器依赖
     * @param dataManager 数据管理器指针
     */
    void setDataManager(DataManager* dataManager);
    
    /**
     * 设置红外控制器依赖
     * @param irController 红外控制器指针
     */
    void setIRController(IRController* irController);
    
    /**
     * 设置任务管理器依赖
     * @param taskManager 任务管理器指针
     */
    void setTaskManager(TaskManager* taskManager);
    
    /**
     * 设置WebSocket管理器依赖
     * @param wsManager WebSocket管理器指针
     */
    void setWSManager(WSManager* wsManager);

    /**
     * 设置网络安全管理器依赖
     * @param networkSecurity 网络安全管理器指针
     */
    void setNetworkSecurity(NetworkSecurity* networkSecurity);
    
    // ==================== 服务器控制 ====================
    
    /**
     * 启动服务器
     * @return bool 启动是否成功
     */
    bool startServer();
    
    /**
     * 停止服务器
     */
    void stopServer();
    
    /**
     * 重启服务器
     * @return bool 重启是否成功
     */
    bool restartServer();
    
    /**
     * 获取服务器状态
     * @return DynamicJsonDocument 服务器状态
     */
    DynamicJsonDocument getServerStatus();

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                  // 是否已初始化
    bool m_serverRunning;                // 服务器是否运行中
    AsyncWebServer* m_server;            // Web服务器实例
    
    // 依赖组件
    DataManager* m_dataManager;
    IRController* m_irController;
    TaskManager* m_taskManager;
    WSManager* m_wsManager;
    NetworkSecurity* m_networkSecurity;
    
    // 统计信息
    unsigned long m_totalRequests;       // 总请求数
    unsigned long m_successfulRequests;  // 成功请求数
    unsigned long m_failedRequests;      // 失败请求数
    
    // ==================== 私有方法 ====================
    
    /**
     * 设置路由
     */
    void setupRoutes();
    
    /**
     * 设置静态文件服务
     */
    void setupStaticFiles();
    
    /**
     * 设置API路由
     */
    void setupAPIRoutes();
    
    /**
     * 设置CORS
     */
    void setupCORS();
    
    // ==================== API处理方法 ====================
    
    /**
     * 处理系统API
     */
    void handleSystemAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理信号API
     */
    void handleSignalAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理任务API
     */
    void handleTaskAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理定时器API
     */
    void handleTimerAPI(AsyncWebServerRequest* request);

    // ==================== 具体API处理方法 ====================

    // 系统API
    void handleSystemInfoAPI(AsyncWebServerRequest* request);
    void handleSystemRestartAPI(AsyncWebServerRequest* request);

    // 信号API
    void handleGetAllSignalsAPI(AsyncWebServerRequest* request);
    void handleGetSignalAPI(AsyncWebServerRequest* request);
    void handleCreateSignalAPI(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleUpdateSignalAPI(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeleteSignalAPI(AsyncWebServerRequest* request);
    void handleSendSignalAPI(AsyncWebServerRequest* request);

    // 任务API
    void handleGetAllTasksAPI(AsyncWebServerRequest* request);
    void handleGetCurrentTaskAPI(AsyncWebServerRequest* request);
    void handleCreateTaskAPI(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleExecuteAllSignalsAPI(AsyncWebServerRequest* request);
    void handleStopTaskAPI(AsyncWebServerRequest* request);
    void handlePauseTaskAPI(AsyncWebServerRequest* request);
    void handleResumeTaskAPI(AsyncWebServerRequest* request);

    // 定时器API
    void handleGetAllTimersAPI(AsyncWebServerRequest* request);
    void handleCreateTimerAPI(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeleteTimerAPI(AsyncWebServerRequest* request);

    // 学习API
    void handleStartLearningAPI(AsyncWebServerRequest* request);
    void handleStopLearningAPI(AsyncWebServerRequest* request);
    void handleGetLearningStatusAPI(AsyncWebServerRequest* request);
    void handleGetLearnedSignalAPI(AsyncWebServerRequest* request);
    
    // ==================== 响应处理 ====================
    
    /**
     * 发送JSON响应
     * @param request 请求对象
     * @param response 响应数据
     * @param statusCode HTTP状态码
     */
    void sendJSONResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& response, int statusCode = 200);
    
    /**
     * 发送错误响应
     * @param request 请求对象
     * @param error 错误信息
     * @param statusCode HTTP状态码
     */
    void sendErrorResponse(AsyncWebServerRequest* request, const String& error, int statusCode = 400);
    
    /**
     * 发送成功响应
     * @param request 请求对象
     * @param message 成功信息
     * @param data 附加数据
     */
    void sendSuccessResponse(AsyncWebServerRequest* request, const String& message, const DynamicJsonDocument& data = DynamicJsonDocument(0));
    
    // ==================== 工具方法 ====================
    
    /**
     * 验证请求参数
     * @param request 请求对象
     * @param requiredParams 必需参数列表
     * @return bool 验证是否通过
     */
    bool validateRequestParams(AsyncWebServerRequest* request, const std::vector<String>& requiredParams);
    
    /**
     * 解析请求体
     * @param request 请求对象
     * @param body 请求体
     * @return DynamicJsonDocument 解析后的JSON
     */
    DynamicJsonDocument parseRequestBody(AsyncWebServerRequest* request, const String& body);
    
    /**
     * 记录请求
     * @param request 请求对象
     * @param success 是否成功
     */
    void logRequest(AsyncWebServerRequest* request, bool success);
    
    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);
    
    /**
     * 更新统计信息
     * @param success 是否成功
     */
    void updateStatistics(bool success);

    // ==================== 安全中间件 ====================

    /**
     * 验证请求安全性
     * @param request 请求对象
     * @return bool 验证是否通过
     */
    bool validateRequestSecurity(AsyncWebServerRequest* request);

    /**
     * 检查API认证
     * @param request 请求对象
     * @return bool 认证是否通过
     */
    bool checkAPIAuthentication(AsyncWebServerRequest* request);

    /**
     * 检查速率限制
     * @param request 请求对象
     * @return bool 是否允许请求
     */
    bool checkRateLimit(AsyncWebServerRequest* request);

    /**
     * 应用CORS头部
     * @param request 请求对象
     */
    void applyCORSHeaders(AsyncWebServerRequest* request);
};

#endif // WEB_SERVER_MANAGER_H

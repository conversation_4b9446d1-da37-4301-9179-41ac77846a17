#include "WebServerManager.h"

WebServerManager::WebServerManager() 
    : m_initialized(false)
    , m_server(nullptr)
    , m_webSocket(nullptr)
{
    Serial.println("WebServerManager created (no memory allocated)");
}

WebServerManager::~WebServerManager() {
    // Server and WebSocket are managed by SystemManager
}

bool WebServerManager::initialize(AsyncWebServer* server, AsyncWebSocket* webSocket) {
    Serial.println("🌐 Initializing WebServerManager...");
    
    if (m_initialized) {
        Serial.println("WebServerManager already initialized");
        return true;
    }
    
    if (!server || !webSocket) {
        Serial.println("❌ Invalid server or websocket pointer");
        return false;
    }
    
    m_server = server;
    m_webSocket = webSocket;
    
    // Setup routes and handlers
    setupRoutes();
    setupWebSocketHandlers();
    
    m_initialized = true;
    Serial.println("✅ WebServerManager initialized successfully");
    
    return true;
}

void WebServerManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // WebSocket cleanup is handled automatically by ESPAsyncWebServer
    // No additional loop processing needed
}

void WebServerManager::setupRoutes() {
    if (!m_server) {
        return;
    }
    
    Serial.println("🛣️  Setting up web server routes...");
    
    // CORS headers for all requests
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type");
    
    // API Routes
    m_server->on("/api/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetStatus(request);
    });
    
    m_server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignals(request);
    });
    
    m_server->on("/api/signals", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleAddSignal(request);
    });
    
    m_server->on("/api/signals/send", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSendSignal(request);
    });
    
    // Handle OPTIONS requests for CORS
    m_server->onRequestBody([](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
        // Handle request body if needed
    });
    
    // Serve static files from SPIFFS
    m_server->serveStatic("/", SPIFFS, "/www/").setDefaultFile("index.html");
    
    // Handle 404
    m_server->onNotFound([this](AsyncWebServerRequest* request) {
        if (request->method() == HTTP_OPTIONS) {
            request->send(200);
        } else {
            sendErrorResponse(request, 404, "Not Found");
        }
    });
    
    Serial.println("✅ Web server routes configured");
}

void WebServerManager::setupWebSocketHandlers() {
    if (!m_webSocket) {
        return;
    }
    
    Serial.println("🔌 Setting up WebSocket handlers...");
    
    m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                               AwsEventType type, void* arg, uint8_t* data, size_t len) {
        onWebSocketEvent(server, client, type, arg, data, len);
    });
    
    // Add WebSocket to server
    m_server->addHandler(m_webSocket);
    
    Serial.println("✅ WebSocket handlers configured");
}

void WebServerManager::handleGetStatus(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(1024);
    
    doc["system"]["uptime"] = millis();
    doc["system"]["free_heap"] = ESP.getFreeHeap();
    doc["system"]["free_psram"] = psramFound() ? ESP.getFreePsram() : 0;
    doc["system"]["chip_model"] = ESP.getChipModel();
    doc["system"]["cpu_freq"] = ESP.getCpuFreqMHz();
    
    doc["websocket"]["clients"] = getConnectedClients();
    
    doc["timestamp"] = millis();
    
    sendJsonResponse(request, doc);
}

void WebServerManager::handleGetSignals(AsyncWebServerRequest* request) {
    // This would typically get data from DataManager
    DynamicJsonDocument doc(2048);
    JsonArray signals = doc.createNestedArray("signals");
    
    // Placeholder data
    DynamicJsonDocument signal(256);
    signal["id"] = "signal_001";
    signal["name"] = "TV Power";
    signal["type"] = "tv";
    signal["protocol"] = "NEC";
    signals.add(signal);
    
    doc["count"] = signals.size();
    doc["timestamp"] = millis();
    
    sendJsonResponse(request, doc);
}

void WebServerManager::handleAddSignal(AsyncWebServerRequest* request) {
    // This would typically add signal via DataManager
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signal added successfully";
    response["timestamp"] = millis();
    
    sendJsonResponse(request, response);
}

void WebServerManager::handleSendSignal(AsyncWebServerRequest* request) {
    // This would typically send signal via IRController
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Signal sent successfully";
    response["timestamp"] = millis();
    
    sendJsonResponse(request, response);
}

void WebServerManager::onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                       AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            Serial.printf("🔌 WebSocket client #%u connected from %s\n", 
                         client->id(), client->remoteIP().toString().c_str());
            
            // Send welcome message
            {
                DynamicJsonDocument welcome(256);
                welcome["type"] = "welcome";
                welcome["message"] = "Connected to ESP32-S3 IR Controller";
                welcome["client_id"] = client->id();
                welcome["timestamp"] = millis();
                
                String welcomeStr;
                serializeJson(welcome, welcomeStr);
                client->text(welcomeStr);
            }
            break;
            
        case WS_EVT_DISCONNECT:
            Serial.printf("🔌 WebSocket client #%u disconnected\n", client->id());
            break;
            
        case WS_EVT_DATA:
            {
                AwsFrameInfo* info = (AwsFrameInfo*)arg;
                if (info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
                    data[len] = 0;
                    String message = (char*)data;
                    Serial.printf("🔌 WebSocket message from #%u: %s\n", client->id(), message.c_str());
                    
                    handleWebSocketMessage(client, message);
                }
            }
            break;
            
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}

void WebServerManager::handleWebSocketMessage(AsyncWebSocketClient* client, const String& message) {
    DynamicJsonDocument doc(512);
    DeserializationError error = deserializeJson(doc, message);
    
    if (error) {
        Serial.printf("❌ Failed to parse WebSocket message: %s\n", error.c_str());
        return;
    }
    
    String type = doc["type"] | "";
    
    if (type == "ping") {
        DynamicJsonDocument response(128);
        response["type"] = "pong";
        response["timestamp"] = millis();
        
        String responseStr;
        serializeJson(response, responseStr);
        client->text(responseStr);
        
    } else if (type == "get_status") {
        DynamicJsonDocument response(512);
        response["type"] = "status";
        response["data"]["uptime"] = millis();
        response["data"]["free_heap"] = ESP.getFreeHeap();
        response["data"]["free_psram"] = psramFound() ? ESP.getFreePsram() : 0;
        response["timestamp"] = millis();
        
        String responseStr;
        serializeJson(response, responseStr);
        client->text(responseStr);
        
    } else {
        Serial.printf("❌ Unknown WebSocket message type: %s\n", type.c_str());
    }
}

void WebServerManager::broadcastMessage(const String& type, const DynamicJsonDocument& data) {
    if (!m_initialized || !m_webSocket) {
        return;
    }
    
    DynamicJsonDocument message(1024);
    message["type"] = type;
    message["data"] = data;
    message["timestamp"] = millis();
    
    String messageStr;
    serializeJson(message, messageStr);
    
    m_webSocket->textAll(messageStr);
}

void WebServerManager::sendMessage(uint32_t clientId, const String& type, const DynamicJsonDocument& data) {
    if (!m_initialized || !m_webSocket) {
        return;
    }
    
    DynamicJsonDocument message(1024);
    message["type"] = type;
    message["data"] = data;
    message["timestamp"] = millis();
    
    String messageStr;
    serializeJson(message, messageStr);
    
    m_webSocket->text(clientId, messageStr);
}

int WebServerManager::getConnectedClients() const {
    return m_webSocket ? m_webSocket->count() : 0;
}

DynamicJsonDocument WebServerManager::getStatus() const {
    DynamicJsonDocument doc(256);
    
    doc["initialized"] = m_initialized;
    doc["connected_clients"] = getConnectedClients();
    doc["timestamp"] = millis();
    
    return doc;
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    DynamicJsonDocument doc(256);
    doc["error"] = true;
    doc["code"] = code;
    doc["message"] = message;
    doc["timestamp"] = millis();
    
    String response;
    serializeJson(doc, response);
    
    request->send(code, "application/json", response);
}

void WebServerManager::sendJsonResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& doc) {
    String response;
    serializeJson(doc, response);
    
    request->send(200, "application/json", response);
}

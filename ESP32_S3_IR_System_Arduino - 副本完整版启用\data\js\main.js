/**
 * R1系统 - 主应用程序
 * 系统启动和模块管理
 */

class R1System {
  constructor() {
    this.startTime = performance.now();
    this.eventBus = new EventBus();

    // 初始化API核心
    this.apiCore = new APICore(this.eventBus);

    this.esp32 = new ESP32Communicator(this.eventBus);
    this.notification = new NotificationSystem();
    this.modules = {};
    this.currentModule = 'signal-manager';
    this.isInitialized = false;

    // 性能优化组件
    this.domUpdater = window.DOMUpdateManager;
    this.timerManager = window.UnifiedTimerManager;

    
    // 性能监控
    this.performance = {
      startTime: this.startTime,
      initTime: 0,
      moduleLoadTime: 0,
      memoryUsage: 0
    };
  }

  /**
   * 初始化系统
   */
  async init() {
    try {
      console.log('🚀 R1系统启动中...');

      // 显示加载界面
      this.showLoadingOverlay();

      // 设置系统状态响应器
      this.setupSystemStatusResponders();

      // 并行初始化核心组件
      await Promise.all([
        this.initCoreComponents(),
        this.initEventListeners(),
        this.initUI(),
        this.preloadModules()
      ]);

      // 初始化API核心
      await this.initAPICore();

      // 初始化ESP32通信
      await this.initESP32Communication();
      
      // 初始化模块
      await this.initModules();

      // 侧边栏现在由状态显示和系统监控模块管理

      // 标记初始化完成
      this.isInitialized = true;
      this.performance.initTime = performance.now() - this.startTime;

      // 隐藏加载界面
      this.hideLoadingOverlay();

      console.log(`✅ R1系统启动完成，耗时: ${this.performance.initTime.toFixed(2)}ms`);

      // 发布系统就绪事件
      this.eventBus.emit('system.ready', {
        initTime: this.performance.initTime,
        timestamp: Date.now()
      });

      // 显示欢迎通知
      this.notification.show('R1智能红外控制系统启动成功', 'success');
      
    } catch (error) {
      console.error('❌ R1系统启动失败:', error);
      this.handleStartupError(error);
    }
  }

  /**
   * 初始化核心组件
   */
  async initCoreComponents() {
    // 生成会话ID
    this.sessionId = R1Utils.generateId('session');

    // 初始化错误统计
    this.errorStats = {};

    // 初始化通知系统
    this.notification.init();

    // 设置全局错误处理
    this.setupGlobalErrorHandling();

    // 初始化控制台收集器
    this.initConsoleCollector();

    // 设置全局调试工具
    this.setupGlobalDebugTools();

    // 初始化本地存储
    this.initLocalStorage();

    console.log(`✅ 核心组件初始化完成 (会话ID: ${this.sessionId})`);
  }

  /**
   * 初始化事件监听器 - 使用统一的事件处理模式
   */
  async initEventListeners() {
    // 系统级事件监听
    this.eventBus.on('system.error', (data) => {
      this.handleSystemError(data);
    });

    // ESP32连接状态事件监听 - 主系统负责UI状态更新
    this.eventBus.on('esp32.connected', (data) => {
      this.updateConnectionStatus('connected', 'ESP32已连接');
    });

    this.eventBus.on('esp32.disconnected', (data) => {
      this.updateConnectionStatus('disconnected', 'ESP32连接断开');
    });

    this.eventBus.on('esp32.error', (data) => {
      this.updateConnectionStatus('error', `连接错误: ${data.error || '未知错误'}`);
    });

    // 模块级事件监听
    this.eventBus.on('module.ready', (data) => {
      console.log(`✅ 模块就绪: ${data.moduleName}`);
    });

    this.eventBus.on('module.error', (data) => {
      console.error(`❌ 模块错误: ${data.moduleName} - ${data.error}`);
    });

    this.eventBus.on('module.success', (data) => {
      console.log(`✅ 模块操作成功: ${data.moduleName} - ${data.message}`);
    });

    // 模块切换事件
    this.eventBus.on('module.switch', (data) => {
      this.switchModule(data.module);
    });

    // 系统模块初始化完成事件
    this.eventBus.on('system.modules.initialized', (data) => {
      console.log(`🎉 所有模块初始化完成: 成功 ${data.successCount} 个`);
    });

    // 模态框事件监听
    this.eventBus.on('system.modal.show', (data) => {
      if (data.content) {
        this.showModal(data.content);
      } else {
        console.error('模态框内容为空!');
      }
    });

    this.eventBus.on('system.modal.hide', (data) => {
      this.hideModal();
    });

    // 测试事件监听器
    this.eventBus.on('test.event', (data) => {
      console.log('🧪 收到测试事件:', data);
    });

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  /**
   * 初始化UI
   */
  async initUI() {
    // 初始化模块标签切换
    this.initModuleTabs();
    
    // 快速操作现在由状态显示模块管理
    
    // 技术约束例外：实时时间显示轮询 - Web环境下唯一可行方案
    this.initSystemTime();
    
    // 初始化设置按钮
    this.initSettingsButton();
    
    // 初始化模态框
    this.initModal();
  }

  /**
   * 预加载模块
   */
  async preloadModules() {
    // 这里可以预加载一些关键模块的资源
    // 目前所有模块都已经通过script标签加载
  }

  /**
   * 初始化API核心
   */
  async initAPICore() {
    try {
      console.log('🔗 初始化API核心...');
      await this.apiCore.init();

      // 暴露R1Core到全局，供前端模块使用
      window.R1Core = this.apiCore;

      console.log('✅ API核心初始化完成');
    } catch (error) {
      console.error('❌ API核心初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化ESP32通信
   */
  async initESP32Communication() {
    try {
      this.updateConnectionStatus('connecting', '正在连接ESP32...');
      await this.esp32.init();
      // ESP32初始化成功后，状态会通过WebSocket消息或API查询更新
    } catch (error) {
      console.warn('ESP32连接失败，将在后台重试:', error);
      this.updateConnectionStatus('disconnected', 'ESP32连接断开');

      // 不阻止系统启动，继续以离线模式运行
    }
  }

  /**
   * 初始化模块 - 使用统一的模块管理
   */
  async initModules() {
    const moduleStartTime = performance.now();

    try {
      console.log('📦 开始初始化所有模块...');

      // 验证所有模块类是否已加载
      const moduleClasses = {
        SignalManager: window.SignalManager,
        ControlModule: window.ControlModule,
        TimerSettings: window.TimerSettings,
        StatusDisplay: window.StatusDisplay,
        SystemMonitor: window.SystemMonitor
      };

      // 检查缺失的模块类
      const missingClasses = Object.entries(moduleClasses)
        .filter(([name, cls]) => typeof cls !== 'function')
        .map(([name]) => name);

      if (missingClasses.length > 0) {
        throw new Error(`以下模块类未定义: ${missingClasses.join(', ')}`);
      }

      // 使用统一的模块初始化方式
      const moduleConfigs = [
        { name: 'signalManager', class: SignalManager },
        { name: 'controlModule', class: ControlModule },
        { name: 'timerSettings', class: TimerSettings },
        { name: 'statusDisplay', class: StatusDisplay },
        { name: 'systemMonitor', class: SystemMonitor }
      ];

      // 并行初始化所有模块
      const modulePromises = moduleConfigs.map(async (config) => {
        try {
          console.log(`🔧 初始化 ${config.name} 模块...`);
          const moduleInstance = new config.class(this.eventBus, this.esp32);
          this.modules[config.name] = moduleInstance;

          // 关键修复：调用模块的init方法来完成完整初始化
          await moduleInstance.init();

          return { name: config.name, success: true, instance: moduleInstance };
        } catch (error) {
          console.error(`❌ ${config.name} 模块初始化失败:`, error);
          return { name: config.name, success: false, error };
        }
      });

      const results = await Promise.allSettled(modulePromises);

      // 统计初始化结果
      let successCount = 0;
      let failureCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successCount++;
          console.log(`✅ ${result.value.name} 模块初始化成功`);
        } else {
          failureCount++;
          const moduleName = moduleConfigs[index].name;
          console.error(`❌ ${moduleName} 模块初始化失败`);
        }
      });

      this.performance.moduleLoadTime = performance.now() - moduleStartTime;

      console.log(`📦 模块初始化完成: 成功 ${successCount} 个，失败 ${failureCount} 个，耗时: ${this.performance.moduleLoadTime.toFixed(2)}ms`);

      // 发布模块初始化完成事件
      this.eventBus.emit('system.modules.initialized', {
        successCount,
        failureCount,
        totalTime: this.performance.moduleLoadTime
      });

    } catch (error) {
      console.error('❌ 模块系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化模块标签
   */
  initModuleTabs() {
    const tabContainer = $('.module-tabs');

    if (tabContainer) {
      // 简单直接的事件绑定 - 使用标准addEventListener
      tabContainer.addEventListener('click', (e) => {
        const tabButton = e.target.closest('.tab-btn');
        if (tabButton) {
          const moduleKey = tabButton.dataset.module;

          // 模块名称映射：HTML使用kebab-case，JS使用camelCase
          const moduleNameMap = {
            'signal-manager': 'signalManager',
            'control-module': 'controlModule',
            'timer-settings': 'timerSettings'
          };

          const module = moduleNameMap[moduleKey];

          if (module && this.modules[module] && !this._switching) {
            this._switching = true;

            try {
              this.switchModule(module);
            } catch (error) {
              console.error('模块切换失败:', error);
            } finally {
              this._switching = false;
            }
          }
        }
      });


    }
  }





  /**
   * 初始化系统时间 - 使用统一定时器管理器
   *
   * 🚨 技术约束例外：实时时间显示轮询
   * Web环境下无法监听系统时间变化事件，1秒轮询是唯一可行方案
   * 性能影响：CPU占用 < 0.005%，内存占用 < 100字节，完全可忽略
   */
  initSystemTime() {
    const updateTime = () => {
      const timeEl = $('#systemTime');
      if (timeEl) {
        timeEl.textContent = R1Utils.formatTime(Date.now(), 'HH:mm:ss');
      }
    };

    // 立即更新一次
    updateTime();

    // 技术约束例外：实时时间显示轮询 - Web环境下唯一可行方案
    this.timerManager.addTimer(
      'system_time_update',
      updateTime,
      1000,
      true // 重复执行
    );

    console.log('⏰ 系统时间显示已启动');
  }

  /**
   * 停止系统时间显示
   */
  stopSystemTime() {
    if (this.timerManager.hasTimer('system_time_update')) {
      this.timerManager.removeTimer('system_time_update');
      console.log('⏰ 系统时间显示已停止');
    }
  }

  /**
   * 显示通知 - 为集成测试提供的方法
   */
  showNotification(message, type = 'info', duration = 5000) {
    if (this.notification && typeof this.notification.show === 'function') {
      return this.notification.show(message, type, duration);
    } else {
      console.warn('通知系统未初始化或不可用');
      return null;
    }
  }

  /**
   * 清理系统资源
   */
  cleanup() {
    try {
      // 停止系统时间显示
      this.stopSystemTime();

      // 清理模块资源
      Object.values(this.modules).forEach(module => {
        if (module && typeof module.destroy === 'function') {
          module.destroy();
        }
      });

      console.log('🧹 系统资源清理完成');
    } catch (error) {
      console.error('❌ 系统资源清理失败:', error);
    }
  }

  /**
   * 初始化设置按钮 - 使用标准addEventListener
   */
  initSettingsButton() {
    const header = $('.system-header');
    if (header) {
      header.addEventListener('click', (e) => {
        if (e.target.id === 'settingsBtn') {
          this.showSettings();
        }
      });


    }
  }

  /**
   * 初始化模态框 - 使用标准addEventListener
   */
  initModal() {
    const modalOverlay = $('#modalOverlay');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          this.hideModal();
        }

        // 处理模态框内的按钮事件
        const actionElement = e.target.closest('[data-action]');
        if (actionElement) {
          const action = actionElement.getAttribute('data-action');
          if (action) {
            this.handleModalAction(action, actionElement);
          }
        }
      });


    }
  }

  /**
   * 处理模态框内的动作
   * @param {string} action - 动作类型
   * @param {Element} target - 目标元素
   */
  handleModalAction(action, target) {
    // 获取信号ID - 从目标元素或其最近的父元素
    let signalId = target.getAttribute('data-signal-id');
    if (!signalId) {
      const signalElement = target.closest('[data-signal-id]');
      signalId = signalElement?.getAttribute('data-signal-id');
    }

    switch (action) {
      case 'close-modal':
        this.hideModal();
        break;
      case 'save-settings':
        this.saveSettings();
        break;
      case 'reload-page':
        location.reload();
        break;
      case 'show-error-details':
        const errorStack = target.getAttribute('data-error-stack');
        this.showErrorDetails(errorStack);
        break;
      case 'copy-error-details':
        const errorStackToCopy = target.getAttribute('data-error-stack');
        this.copyErrorDetails(errorStackToCopy);
        break;

      // 信号管理模块的模态框事件
      case 'send-and-close':
        if (signalId) {
          // 通过事件系统发送信号
          this.eventBus.emit('signal.send.request', { signalId: signalId });
          this.hideModal();
        }
        break;

      case 'edit-and-close':
        if (signalId) {
          this.hideModal();
          // 通过事件系统请求编辑信号
          this.eventBus.emit('signal.edit.request', { signalId: signalId });
        }
        break;

      case 'import-from-file':
        this.eventBus.emit('signal.import.file.request');
        break;

      case 'import-from-text':
        this.eventBus.emit('signal.import.text.request');
        break;

      case 'submit-learn-form':
        this.eventBus.emit('signal.learn.form.submit');
        break;

      case 'submit-edit-form':
        const editSignalId = target.getAttribute('data-signal-id');
        if (editSignalId) {
          this.eventBus.emit('signal.edit.form.submit', { signalId: editSignalId });
        }
        break;

      // 控制模块的模态框事件
      case 'create-task-submit':
        this.eventBus.emit('control.task.create.submit');
        break;

      // 定时器模块的模态框事件
      case 'create-timer-submit':
        this.eventBus.emit('timer.create.submit');
        break;

      case 'edit-timer-submit':
        const timerId = target.getAttribute('data-timer-id');
        if (timerId) {
          this.eventBus.emit('timer.edit.submit', { timerId: timerId });
        }
        break;
    }
  }

  /**
   * 切换模块 - 精准追踪版本
   * @param {string} moduleName - 模块名称（camelCase）
   * @param {string} traceId - 追踪ID
   */
  switchModule(moduleName, traceId = 'direct') {
    const switchStartTime = performance.now();
    console.log(`🔄 [${traceId}] switchModule开始: ${this.currentModule} -> ${moduleName} (${switchStartTime.toFixed(3)}ms)`);

    if (this.currentModule === moduleName) {
      console.log(`🔄 [${traceId}] 相同模块，跳过切换`);
      return;
    }

    const previousModule = this.currentModule;

    // 🎯 步骤1: 状态更新
    const step1Start = performance.now();
    this.currentModule = moduleName;
    const step1Time = performance.now() - step1Start;
    console.log(`🔄 [${traceId}] 步骤1-状态更新: ${step1Time.toFixed(3)}ms`);

    // 🔧 创建camelCase到kebab-case的映射
    const camelToKebabMap = {
      'signalManager': 'signal-manager',
      'controlModule': 'control-module',
      'timerSettings': 'timer-settings'
    };

    const previousModuleId = camelToKebabMap[previousModule] || previousModule;
    const newModuleId = camelToKebabMap[moduleName] || moduleName;

    console.log(`🔧 [${traceId}] 模块ID映射: ${previousModule} -> ${previousModuleId}, ${moduleName} -> ${newModuleId}`);

    // 🎯 步骤2: DOM查询
    const step2Start = performance.now();
    const currentPanel = $(`#${previousModuleId}`);
    const newPanel = $(`#${newModuleId}`);
    const allTabs = $$('.tab-btn');
    const activeTab = $(`.tab-btn[data-module="${newModuleId}"]`);
    const step2Time = performance.now() - step2Start;
    console.log(`🔄 [${traceId}] 步骤2-DOM查询: ${step2Time.toFixed(3)}ms`, {
      currentPanel: !!currentPanel,
      newPanel: !!newPanel,
      allTabsCount: allTabs.length,
      activeTab: !!activeTab
    });

    // 🎯 步骤3: 隐藏当前面板
    const step3Start = performance.now();
    if (currentPanel) {
      console.log(`🔄 [${traceId}] 隐藏面板: ${previousModule} (ID: ${previousModuleId})`);

      // 🎯 监控CSS变化
      const beforeHide = {
        opacity: getComputedStyle(currentPanel).opacity,
        visibility: getComputedStyle(currentPanel).visibility,
        display: getComputedStyle(currentPanel).display
      };
      console.log(`🎯 [${traceId}] 隐藏前CSS状态:`, beforeHide);

      currentPanel.classList.remove('active');

      // 强制重排重绘检测
      const afterHide = {
        opacity: getComputedStyle(currentPanel).opacity,
        visibility: getComputedStyle(currentPanel).visibility,
        display: getComputedStyle(currentPanel).display
      };
      console.log(`🎯 [${traceId}] 隐藏后CSS状态:`, afterHide);
    } else {
      console.warn(`🔄 [${traceId}] ⚠️ 当前面板不存在: ${previousModule} (ID: ${previousModuleId})`);
    }
    const step3Time = performance.now() - step3Start;
    console.log(`🔄 [${traceId}] 步骤3-隐藏面板: ${step3Time.toFixed(3)}ms`);

    // 🎯 步骤4: 显示新面板
    const step4Start = performance.now();
    if (newPanel) {
      console.log(`🔄 [${traceId}] 显示面板: ${moduleName} (ID: ${newModuleId})`);

      // 🎯 监控CSS变化
      const beforeShow = {
        opacity: getComputedStyle(newPanel).opacity,
        visibility: getComputedStyle(newPanel).visibility,
        display: getComputedStyle(newPanel).display
      };
      console.log(`🎯 [${traceId}] 显示前CSS状态:`, beforeShow);

      newPanel.classList.add('active');

      // 强制重排重绘检测
      const afterShow = {
        opacity: getComputedStyle(newPanel).opacity,
        visibility: getComputedStyle(newPanel).visibility,
        display: getComputedStyle(newPanel).display
      };
      console.log(`🎯 [${traceId}] 显示后CSS状态:`, afterShow);
    } else {
      console.error(`🔄 [${traceId}] ❌ 新面板不存在: ${moduleName} (ID: ${newModuleId})`);
    }
    const step4Time = performance.now() - step4Start;
    console.log(`🔄 [${traceId}] 步骤4-显示面板: ${step4Time.toFixed(3)}ms`);

    // 🎯 步骤5: 更新标签状态
    const step5Start = performance.now();
    console.log(`🔄 [${traceId}] 更新${allTabs.length}个标签状态`);
    allTabs.forEach((btn, index) => {
      btn.classList.remove('active');
    });
    const step5Time = performance.now() - step5Start;
    console.log(`🔄 [${traceId}] 步骤5-清除标签: ${step5Time.toFixed(3)}ms`);

    // 🎯 步骤6: 激活目标标签
    const step6Start = performance.now();
    if (activeTab) {
      console.log(`🔄 [${traceId}] 激活标签: ${moduleName} (data-module: ${newModuleId})`);
      activeTab.classList.add('active');
    } else {
      console.error(`🔄 [${traceId}] ❌ 目标标签不存在: ${moduleName} (data-module: ${newModuleId})`);
    }
    const step6Time = performance.now() - step6Start;
    console.log(`🔄 [${traceId}] 步骤6-激活标签: ${step6Time.toFixed(3)}ms`);

    const totalTime = performance.now() - switchStartTime;
    console.log(`🏁 [${traceId}] switchModule完成: ${totalTime.toFixed(3)}ms`);
    console.log(`📊 [${traceId}] 性能分解:`, {
      状态更新: `${step1Time.toFixed(3)}ms`,
      DOM查询: `${step2Time.toFixed(3)}ms`,
      隐藏面板: `${step3Time.toFixed(3)}ms`,
      显示面板: `${step4Time.toFixed(3)}ms`,
      清除标签: `${step5Time.toFixed(3)}ms`,
      激活标签: `${step6Time.toFixed(3)}ms`,
      总计: `${totalTime.toFixed(3)}ms`
    });

    if (totalTime > 16) {
      console.warn(`⚠️ [${traceId}] switchModule耗时过长: ${totalTime.toFixed(3)}ms`);
    }
  }



  /**
   * 刷新全部 - 使用统一的模块访问方式
   */
  async refreshAll() {
    try {
      this.notification.show('正在刷新系统数据...', 'info');

      // 统一刷新所有模块
      const moduleNames = Object.keys(this.modules);
      const refreshPromises = moduleNames.map(async (moduleName) => {
        const module = this.modules[moduleName];
        if (module && module.refresh && typeof module.refresh === 'function') {
          try {
            await module.refresh();
            return { module: moduleName, success: true };
          } catch (error) {
            console.error(`${moduleName} 刷新失败:`, error);
            return { module: moduleName, success: false, error };
          }
        }
        return { module: moduleName, success: true, skipped: true };
      });

      const results = await Promise.allSettled(refreshPromises);

      let successCount = 0;
      let failureCount = 0;

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            successCount++;
          } else {
            failureCount++;
          }
        } else {
          failureCount++;
        }
      });

      if (failureCount === 0) {
        this.notification.show('系统数据刷新完成', 'success');
      } else {
        this.notification.show(`刷新完成: 成功 ${successCount} 个，失败 ${failureCount} 个`, 'warning');
      }

    } catch (error) {
      console.error('系统刷新失败:', error);
      this.notification.show(`刷新失败: ${error.message}`, 'error');
    }
  }

  /**
   * 显示设置 - 符合系统设计风格
   */
  showSettings() {
    const modalContent = `
      <div class="modal-header">
        <h3>⚙️ 系统设置</h3>
      </div>

      <div class="modal-body">
        <div class="settings-content">
          <!-- 连接设置 -->
          <div class="setting-group">
            <div class="setting-group-header">
              <h4>🔗 连接设置</h4>
              <p class="setting-group-desc">配置ESP32设备连接参数</p>
            </div>
            <div class="setting-grid">
              <div class="form-group">
                <label for="esp32IP">ESP32 IP地址</label>
                <input type="text" id="esp32IP" class="form-input" value="${this.esp32.baseURL.replace('http://', '')}" placeholder="*************">
                <small class="form-help">设备在局域网中的IP地址</small>
              </div>
              <div class="form-group">
                <label for="wsPort">WebSocket端口</label>
                <input type="number" id="wsPort" class="form-input" value="80" placeholder="80" min="1" max="65535">
                <small class="form-help">WebSocket通信端口</small>
              </div>
            </div>
          </div>

          <!-- 界面设置 -->
          <div class="setting-group">
            <div class="setting-group-header">
              <h4>🎨 界面设置</h4>
              <p class="setting-group-desc">个性化界面显示选项</p>
            </div>
            <div class="setting-options">
              <div class="setting-option">
                <div class="option-info">
                  <label for="darkMode">深色模式</label>
                  <small>启用深色主题界面</small>
                </div>
                <div class="option-control">
                  <label class="settings-switch">
                    <input type="checkbox" id="darkMode">
                    <span class="settings-switch-slider"></span>
                  </label>
                </div>
              </div>
              <div class="setting-option">
                <div class="option-info">
                  <label for="autoRefresh">自动刷新数据</label>
                  <small>定期更新系统状态信息</small>
                </div>
                <div class="option-control">
                  <label class="settings-switch">
                    <input type="checkbox" id="autoRefresh" checked>
                    <span class="settings-switch-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="setting-group">
            <div class="setting-group-header">
              <h4>📊 系统信息</h4>
              <p class="setting-group-desc">当前系统运行状态</p>
            </div>
            <div class="system-info-grid">
              <div class="info-item">
                <div class="info-label">系统版本</div>
                <div class="info-value">v2.1.0</div>
              </div>
              <div class="info-item">
                <div class="info-label">启动时间</div>
                <div class="info-value">${this.performance.initTime.toFixed(2)}ms</div>
              </div>
              <div class="info-item">
                <div class="info-label">运行时间</div>
                <div class="info-value">${((Date.now() - this.performance.startTime) / 1000).toFixed(1)}s</div>
              </div>
              <div class="info-item">
                <div class="info-label">活动模块</div>
                <div class="info-value">${Object.keys(this.modules).length}个</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">
            <span class="btn-icon">✕</span>
            <span class="btn-text">取消</span>
          </button>
          <button type="button" class="btn primary" data-action="save-settings">
            <span class="btn-icon">💾</span>
            <span class="btn-text">保存设置</span>
          </button>
        </div>
      </div>
    `;

    this.showModal(modalContent);
  }

  /**
   * 保存设置
   */
  saveSettings() {
    const esp32IP = $('#esp32IP')?.value;
    const wsPort = $('#wsPort')?.value;
    const darkMode = $('#darkMode')?.checked;
    const autoRefresh = $('#autoRefresh')?.checked;

    // 保存设置到本地存储
    const settings = {
      esp32IP,
      wsPort,
      darkMode,
      autoRefresh
    };

    R1Utils.storage.set('settings', settings);

    // 应用设置
    if (esp32IP && esp32IP !== this.esp32.baseURL.replace('http://', '')) {
      this.esp32.baseURL = `http://${esp32IP}`;
      this.esp32.wsURL = `ws://${esp32IP}:${wsPort || 80}/ws`;
      
      // 重新连接
      this.esp32.close();
      this.esp32.init().catch(console.error);
    }

    if (darkMode) {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }

    this.hideModal();
    this.notification.show('设置已保存', 'success');
  }

  /**
   * 显示模态框
   * @param {string} content - 模态框内容
   */
  showModal(content) {
    const modalOverlay = $('#modalOverlay');
    const modalContent = $('#modalContent');

    if (modalOverlay && modalContent) {
      modalContent.innerHTML = content;
      modalOverlay.style.display = 'flex';

      // 🔧 移除动画setTimeout，直接设置样式
      modalOverlay.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }
  }

  /**
   * 隐藏模态框
   */
  hideModal() {
    const modalOverlay = $('#modalOverlay');

    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      const modalContent = $('#modalContent');
      if (modalContent) {
        modalContent.style.transform = 'scale(0.9)';
      }

      // 等待CSS动画完成后隐藏
      setTimeout(() => {
        modalOverlay.style.display = 'none';
      }, 300); // 与CSS transition时间匹配（--transition-normal: 0.3s）
    }
  }

  /**
   * 显示加载界面
   */
  showLoadingOverlay() {
    const loadingOverlay = $('#loadingOverlay');
    if (loadingOverlay) {
      loadingOverlay.style.display = 'flex';
    }
  }

  /**
   * 隐藏加载界面
   */
  hideLoadingOverlay() {
    const loadingOverlay = $('#loadingOverlay');
    if (loadingOverlay) {
      loadingOverlay.style.opacity = '0';
      // 🔧 移除动画setTimeout，直接隐藏
      loadingOverlay.style.display = 'none';
    }
  }

  /**
   * 更新连接状态
   * @param {string} status - 状态 (connected, disconnected, connecting, error)
   * @param {string} text - 状态文本
   */
  updateConnectionStatus(status, text) {
    const statusIndicator = $('#statusIndicator');
    const statusText = $('#statusText');

    if (statusIndicator) {
      statusIndicator.className = `status-indicator ${status}`;
    }

    if (statusText) {
      statusText.textContent = text;
    }
  }

  /**
   * 启动科学的性能分析 - 找到真正的问题源
   */
  startSystemMonitoring() {
    console.log('🔍 启动科学的性能分析，找到真正的问题源...');

    // 🔧 移除长任务监控 - 避免性能开销
    // 长任务监控本身会消耗资源，而且会产生大量日志输出
    console.log('🔧 长任务监控已禁用，避免性能开销');

    // 🔧 移除DOM监控代理，避免性能开销
    // this.startLightweightDOMMonitoring();

    // 🔧 移除模块性能监控，避免无意义的事件监听
    // this.startModulePerformanceMonitoring();

    console.log('✅ 科学性能分析已启动');
  }

  /**
   * 移除长任务分析 - 避免性能开销
   */
  analyzeLongTask(entry) {
    // 完全移除长任务分析，避免额外的日志输出和调用栈捕获
  }

  /**
   * 移除DOM监控代理 - 避免性能开销
   */
  startLightweightDOMMonitoring() {
    // 完全移除DOM监控代理
    // 代理本身会增加每次DOM查询的开销，这是不必要的性能负担
    console.log('🔧 DOM监控代理已禁用，避免性能开销');
  }

  /**
   * 移除模块性能监控 - 避免无意义的事件监听
   */
  startModulePerformanceMonitoring() {
    // 完全移除性能监控相关的事件监听器
    // 这些监听器会在每次点击时执行，造成不必要的性能开销
    console.log('🔧 模块性能监控已禁用，避免无意义的事件监听');
  }





  /**
   * 🔍 手动触发性能分析
   */
  triggerPerformanceAnalysis() {
    console.log('🔍 手动触发性能分析...');

    // 强制垃圾回收（如果支持）
    if (window.gc) {
      console.log('🗑️ 执行垃圾回收...');
      window.gc();
    }

    // 分析当前状态
    this.analyzeCurrentState();

    // 检查内存泄漏
    this.checkMemoryLeaks();
  }

  /**
   * 移除系统状态分析 - 避免大量DOM查询
   */
  analyzeCurrentState() {
    console.log('🔧 系统状态分析已禁用，避免DOM查询开销');

    // 移除所有DOM查询，这些查询会导致性能问题：
    // - document.querySelectorAll('*') 查询所有元素
    // - document.querySelectorAll(':not([style*="display: none"])') 复杂选择器
    // - document.querySelectorAll('[style*="animation"]') 属性选择器

    // 只保留基本的模块状态检查，不涉及DOM查询
    const moduleStats = {};
    Object.keys(this.modules).forEach(name => {
      moduleStats[name] = {
        exists: !!this.modules[name],
        initialized: this.modules[name]?.isInitialized || false
      };
    });
    console.log('📊 模块状态:', moduleStats);

    // 内存状态检查保留，因为不涉及DOM操作
    if (performance.memory) {
      const memoryStats = {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      };
      console.log('📊 内存状态:', memoryStats);
    }
  }

  /**
   * 检查内存泄漏
   */
  checkMemoryLeaks() {
    if (!performance.memory) {
      console.log('⚠️ 浏览器不支持内存监控');
      return;
    }

    const initialMemory = performance.memory.usedJSHeapSize;
    console.log(`🔍 内存泄漏检测已禁用，避免定时器循环`);
    console.log(`📊 当前内存: ${Math.round(initialMemory / 1024 / 1024)}MB`);

    // 移除内存检测定时器，避免无意义的循环
    // 内存检测应该在需要时手动触发，而不是自动定时检测
  }

  /**
   * 移除动画元素检查 - 避免大量DOM查询
   */
  checkAnimatingElements() {
    // 完全移除动画元素检查，避免大量DOM查询和getComputedStyle调用

  }

  /**
   * 移除性能指标更新 - 避免无意义的计算
   */
  updatePerformanceMetrics() {
    // 完全移除性能指标的持续更新
    // 性能数据应该在需要显示时按需获取，而不是持续计算
    // 这避免了无意义的CPU消耗、内存分配和DOM查询
  }

  /**
   * 检查内存使用
   */
  checkMemoryUsage() {
    if (performance.memory) {
      const memoryUsage = performance.memory.usedJSHeapSize;
      const memoryLimit = performance.memory.jsHeapSizeLimit;
      const usagePercent = (memoryUsage / memoryLimit) * 100;

      if (usagePercent > 80) {
        console.warn(`内存使用率过高: ${usagePercent.toFixed(1)}%`);
        this.notification.show(
          `内存使用率过高: ${usagePercent.toFixed(1)}%`,
          'warning'
        );
      }
    }
  }

  /**
   * 保存系统状态
   */
  saveSystemState() {
    const systemState = {
      currentModule: this.currentModule,
      performance: this.performance,
      timestamp: Date.now()
    };

    R1Utils.storage.set('systemState', systemState);
  }

  /**
   * 恢复系统状态
   */
  restoreSystemState() {
    const savedState = R1Utils.storage.get('systemState');
    if (savedState) {
      // 恢复当前模块
      if (savedState.currentModule) {
        this.switchModule(savedState.currentModule);
      }
    }
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandling() {
    console.log('🔧 正在设置全局错误处理器...');

    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 未处理的Promise错误:', event.reason);

      this.eventBus.emit('system.error', {
        type: 'unhandled_promise_rejection',
        error: event.reason?.message || '未知Promise错误',
        stack: event.reason?.stack || '',
        timestamp: Date.now()
      });

      // 显示用户友好的错误提示
      this.notification.show('系统出现异步错误，请检查控制台', 'error');

      event.preventDefault();
    });

    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      console.error('🚨 JavaScript错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });

      this.eventBus.emit('system.error', {
        type: 'javascript_error',
        error: event.error?.message || event.message || '未知JavaScript错误',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack || '',
        timestamp: Date.now()
      });

      // 显示用户友好的错误提示
      this.notification.show(`JavaScript错误: ${event.message}`, 'error');
    });

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        const resource = event.target.src || event.target.href || event.target.tagName;
        console.error('🚨 资源加载错误:', resource);

        this.eventBus.emit('system.error', {
          type: 'resource_load_error',
          resource: resource,
          tagName: event.target.tagName,
          timestamp: Date.now()
        });

        // 显示用户友好的错误提示
        this.notification.show(`资源加载失败: ${resource}`, 'warning');
      }
    }, true);

    console.log('✅ 全局错误处理器设置完成');
  }

  /**
   * 处理系统错误
   * @param {object} error - 错误信息
   */
  handleSystemError(error) {
    console.error('🚨 系统错误详情:', error);

    // 增强错误信息显示
    let errorMessage = error.error || '未知错误';
    if (error.filename && error.lineno) {
      errorMessage += ` (${error.filename}:${error.lineno})`;
    }

    // 显示错误通知
    this.notification.show(
      `系统错误: ${errorMessage}`,
      'error'
    );

    // 记录错误日志
    this.logError(error);

    // 如果是关键错误，显示详细信息
    if (error.type === 'javascript_error' && error.stack) {
      console.group('🔍 错误堆栈详情');
      console.error(error.stack);
      console.groupEnd();
    }

    // 统计错误频率
    this.trackErrorFrequency(error);
  }

  /**
   * 处理启动错误
   * @param {Error} error - 错误对象
   */
  handleStartupError(error) {
    // 隐藏加载界面
    this.hideLoadingOverlay();

    // 显示错误页面
    const errorHTML = `
      <div class="startup-error">
        <div class="error-icon">⚠️</div>
        <h1>系统启动失败</h1>
        <p class="error-message">${error.message}</p>
        <div class="error-actions">
          <button class="btn primary" data-action="reload-page">重新加载</button>
          <button class="btn secondary" data-action="show-error-details" data-error-stack="${error.stack}">查看详情</button>
        </div>
      </div>
    `;

    document.body.innerHTML = errorHTML;
  }

  /**
   * 显示错误详情
   * @param {string} errorStack - 错误堆栈
   */
  showErrorDetails(errorStack) {
    const modalContent = `
      <div class="error-details">
        <h3>错误详情</h3>
        <pre class="error-stack">${errorStack}</pre>
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">关闭</button>
          <button type="button" class="btn primary" data-action="copy-error-details" data-error-stack="${errorStack}">复制错误信息</button>
        </div>
      </div>
    `;

    this.showModal(modalContent);
  }

  /**
   * 复制错误详情
   * @param {string} errorStack - 错误堆栈
   */
  copyErrorDetails(errorStack) {
    navigator.clipboard.writeText(errorStack).then(() => {
      this.notification.show('错误信息已复制到剪贴板', 'success');
    }).catch(() => {
      this.notification.show('复制失败', 'error');
    });
  }

  /**
   * 记录错误日志
   * @param {object} error - 错误信息
   */
  logError(error) {
    const errorLog = R1Utils.storage.get('errorLogs', []);
    const enhancedError = {
      ...error,
      id: R1Utils.generateId('error'),
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.sessionId || 'unknown'
    };

    errorLog.push(enhancedError);

    // 限制日志数量
    if (errorLog.length > 100) {
      errorLog.splice(0, errorLog.length - 100);
    }

    R1Utils.storage.set('errorLogs', errorLog);

    console.log(`📝 错误已记录到本地存储 (ID: ${enhancedError.id})`);
  }

  /**
   * 跟踪错误频率
   * @param {object} error - 错误信息
   */
  trackErrorFrequency(error) {
    if (!this.errorStats) {
      this.errorStats = {};
    }

    const errorKey = `${error.type}_${error.error}`;
    if (!this.errorStats[errorKey]) {
      this.errorStats[errorKey] = {
        count: 0,
        firstSeen: Date.now(),
        lastSeen: Date.now(),
        type: error.type,
        message: error.error
      };
    }

    this.errorStats[errorKey].count++;
    this.errorStats[errorKey].lastSeen = Date.now();

    // 如果同一错误频繁出现，发出警告
    if (this.errorStats[errorKey].count >= 5) {
      console.warn(`⚠️ 频繁错误警告: "${error.error}" 已出现 ${this.errorStats[errorKey].count} 次`);

      if (this.errorStats[errorKey].count === 5) {
        this.notification.show(`检测到频繁错误: ${error.error}`, 'warning');
      }
    }
  }

  /**
   * 初始化本地存储
   */
  initLocalStorage() {
    // 检查本地存储可用性
    try {
      const testKey = 'r1_storage_test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
    } catch (error) {
      console.warn('本地存储不可用:', error);
      this.notification.show('本地存储不可用，数据将不会保存', 'warning');
    }

    // 加载保存的设置
    const savedSettings = R1Utils.storage.get('settings');
    if (savedSettings) {
      this.applySettings(savedSettings);
    }
  }

  /**
   * 应用设置
   * @param {object} settings - 设置对象
   */
  applySettings(settings) {
    if (settings.esp32IP) {
      this.esp32.baseURL = `http://${settings.esp32IP}`;
      this.esp32.wsURL = `ws://${settings.esp32IP}:${settings.wsPort || 80}/ws`;
    }

    if (settings.darkMode) {
      document.body.classList.add('dark-mode');
    }

    // 应用其他设置...
  }

  /**
   * 统一的模块访问方法
   * @param {string} moduleName - 模块名称
   * @returns {object|null} 模块实例
   */
  getModule(moduleName) {
    const module = this.modules[moduleName];
    if (!module) {
      console.warn(`模块 ${moduleName} 不存在`);
      return null;
    }

    if (!module.isInitialized) {
      console.warn(`模块 ${moduleName} 未初始化`);
      return null;
    }

    return module;
  }

  /**
   * 检查模块是否可用
   * @param {string} moduleName - 模块名称
   * @returns {boolean} 是否可用
   */
  isModuleReady(moduleName) {
    const module = this.modules[moduleName];
    return module && module.isInitialized && module.isActive;
  }

  /**
   * 获取所有模块状态
   * @returns {object} 模块状态信息
   */
  getModulesStatus() {
    const status = {};
    Object.keys(this.modules).forEach(moduleName => {
      const module = this.modules[moduleName];
      if (module && typeof module.getStatus === 'function') {
        status[moduleName] = module.getStatus();
      } else {
        status[moduleName] = {
          moduleName,
          isInitialized: false,
          isActive: false,
          error: 'Module not properly initialized'
        };
      }
    });
    return status;
  }

  /**
   * 获取系统信息
   * @returns {object} 系统信息
   */
  getSystemInfo() {
    return {
      version: '2.0.0',
      startTime: this.performance.startTime,
      initTime: this.performance.initTime,
      uptime: Date.now() - this.performance.startTime,
      memoryUsage: this.performance.memoryUsage,
      isInitialized: this.isInitialized,
      currentModule: this.currentModule,
      esp32Connected: this.esp32.connected,
      modulesLoaded: Object.keys(this.modules).length,
      modulesStatus: this.getModulesStatus()
    };
  }

  /**
   * 设置全局调试工具
   */
  setupGlobalDebugTools() {
    // 初始化控制台收集器
    this.initConsoleCollector();

    // 添加下载按钮到页面
    this.addDownloadButton();

    // 在全局添加调试工具
    window.R1Debug = {
      // 分析当前性能
      analyze: () => {
        this.triggerPerformanceAnalysis();
      },

      // 模块隔离测试
      isolateModules: () => {
        this.startModuleIsolationTest();
      },

      // 内存泄漏检测
      checkMemory: () => {
        this.checkMemoryLeaks();
      },

      // 获取系统状态
      getSystemState: () => {
        return {
          currentModule: this.currentModule,
          modules: Object.keys(this.modules),
          performance: this.performance,
          isInitialized: this.isInitialized
        };
      },

      // 强制垃圾回收
      gc: () => {
        if (window.gc) {
          window.gc();
          console.log('✅ 垃圾回收已执行');
        } else {
          console.warn('⚠️ 浏览器不支持手动垃圾回收');
        }
      },

      // 查看事件监听器
      getEventListeners: () => {
        return {
          eventBusSize: this.eventBus.events.size,
          eventBusEvents: Array.from(this.eventBus.events.keys())
        };
      },

      // 下载错误报告
      downloadReport: () => {
        this.downloadErrorReport();
      },

      // 获取收集的日志
      getLogs: () => {
        return this.consoleCollector.logs;
      },

      // 清空日志
      clearLogs: () => {
        this.consoleCollector.logs = [];
        console.log('✅ 控制台日志已清空');
      },

      // 测试错误收集器
      testError: () => {
        console.log('🧪 测试错误收集器...');

        // 测试JavaScript错误
        setTimeout(() => {
          throw new Error('这是一个测试错误 - JavaScript Error');
        }, 100);

        // 测试Promise错误
        setTimeout(() => {
          Promise.reject(new Error('这是一个测试错误 - Promise Rejection'));
        }, 200);

        console.log('✅ 错误测试已触发，请查看控制台和通知');
      },

      // 获取错误统计
      getErrorStats: () => {
        return this.errorStats || {};
      },

      // 清除错误日志
      clearErrorLogs: () => {
        R1Utils.storage.remove('errorLogs');
        this.errorStats = {};
        console.log('🧹 错误日志已清除');
      },

      // 验证信号数据格式
      // ⚠️ 错误收集器例外：允许直接模块访问以获取最高效、直接、准确的错误信息
      validateSignalFormats: () => {
        if (this.modules.signalManager) {
          const signals = Array.from(this.modules.signalManager.signals.values());
          const invalidSignals = signals.filter(signal => !this.modules.signalManager.validateSignalFormat(signal));
          console.log('🔧 信号格式验证完成:', {
            total: signals.length,
            valid: signals.length - invalidSignals.length,
            invalid: invalidSignals.length,
            invalidSignals: invalidSignals
          });
          return { total: signals.length, invalid: invalidSignals.length, invalidSignals };
        } else {
          console.error('❌ SignalManager模块未加载');
          return { success: false, error: 'Module not loaded' };
        }
      }
    };

    console.log('✅ 错误收集器已启动，点击右下角按钮下载报告');
  }

  /**
   * 初始化控制台收集器
   */
  initConsoleCollector() {
    this.consoleCollector = {
      logs: [],
      startTime: Date.now()
    };

    // 保存原始的控制台方法
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // 重写控制台方法来收集日志
    const collectLog = (level, originalMethod) => {
      return (...args) => {
        // 调用原始方法
        originalMethod.apply(console, args);

        // 收集日志
        const timestamp = Date.now();
        const timeStr = new Date(timestamp).toISOString();
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg, null, 2);
            } catch (e) {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        this.consoleCollector.logs.push({
          level,
          timestamp,
          timeStr,
          message,
          args: args.map(arg => {
            if (typeof arg === 'object') {
              try {
                return JSON.parse(JSON.stringify(arg));
              } catch (e) {
                return String(arg);
              }
            }
            return arg;
          })
        });

        // 限制日志数量，避免内存溢出
        if (this.consoleCollector.logs.length > 5000) {
          this.consoleCollector.logs = this.consoleCollector.logs.slice(-4000);
        }

        // 更新下载按钮的日志计数
        this.updateLogCount();
      };
    };

    // 重写所有控制台方法
    console.log = collectLog('LOG', originalConsole.log);
    console.warn = collectLog('WARN', originalConsole.warn);
    console.error = collectLog('ERROR', originalConsole.error);
    console.info = collectLog('INFO', originalConsole.info);
    console.debug = collectLog('DEBUG', originalConsole.debug);

    // 收集未捕获的错误
    window.addEventListener('error', (event) => {
      this.consoleCollector.logs.push({
        level: 'UNCAUGHT_ERROR',
        timestamp: Date.now(),
        timeStr: new Date().toISOString(),
        message: `未捕获错误: ${event.message}`,
        args: [{
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error ? event.error.stack : null
        }]
      });
    });

    // 收集未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.consoleCollector.logs.push({
        level: 'UNHANDLED_REJECTION',
        timestamp: Date.now(),
        timeStr: new Date().toISOString(),
        message: `未处理的Promise拒绝: ${event.reason}`,
        args: [{
          reason: event.reason,
          promise: event.promise
        }]
      });
    });


  }

  /**
   * 更新日志计数显示
   */
  updateLogCount() {
    const logCountElement = $('#logCount');
    if (logCountElement && this.consoleCollector) {
      logCountElement.textContent = this.consoleCollector.logs.length;
    }
  }

  /**
   * 添加下载按钮到页面
   */
  addDownloadButton() {
    // 检查是否已存在下载按钮，避免重复创建 - 使用封装的DOM操作
    if ($('#debugDownloadBtn')) {
      console.log('⚠️ 下载按钮已存在，跳过创建');
      return;
    }

    // 创建下载按钮 - 使用封装的DOM操作
    const downloadBtn = R1Utils.dom.create('button');
    downloadBtn.id = 'debugDownloadBtn';
    downloadBtn.innerHTML = `
      <span style="margin-right: 8px;">📥</span>
      <span>下载报告</span>
      <span id="logCount" style="margin-left: 8px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px; font-size: 12px;">0</span>
    `;

    // 设置按钮样式
    Object.assign(downloadBtn.style, {
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      zIndex: '10000',
      padding: '12px 16px',
      backgroundColor: '#2563eb',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
      display: 'flex',
      alignItems: 'center',
      transition: 'all 0.2s ease',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    });

    // 添加悬停效果
    downloadBtn.addEventListener('mouseenter', () => {
      downloadBtn.style.backgroundColor = '#1d4ed8';
      downloadBtn.style.transform = 'translateY(-2px)';
      downloadBtn.style.boxShadow = '0 6px 16px rgba(37, 99, 235, 0.4)';
    });

    downloadBtn.addEventListener('mouseleave', () => {
      downloadBtn.style.backgroundColor = '#2563eb';
      downloadBtn.style.transform = 'translateY(0)';
      downloadBtn.style.boxShadow = '0 4px 12px rgba(37, 99, 235, 0.3)';
    });

    // 绑定点击事件
    downloadBtn.addEventListener('click', () => {
      this.downloadErrorReport();
    });

    // 添加到页面 - 使用封装的DOM操作
    document.body.appendChild(downloadBtn);

    // 移除定期更新日志计数的定时器，改为事件驱动更新
    // 在日志添加时直接更新计数，避免无意义的定时器循环


  }

  /**
   * 下载错误报告
   */
  downloadErrorReport() {
    try {
      // 生成报告内容
      const report = this.generateErrorReport();

      // 创建下载链接 - 使用封装的DOM操作
      const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = R1Utils.dom.create('a');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      link.download = `R1-Debug-Report-${timestamp}.txt`;
      link.href = url;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL
      URL.revokeObjectURL(url);

      console.log(`✅ 错误报告已下载: ${link.download}`);
      console.log(`📊 报告包含 ${this.consoleCollector.logs.length} 条日志记录`);

    } catch (error) {
      console.error('❌ 下载报告失败:', error);
    }
  }

  /**
   * 生成错误报告
   */
  generateErrorReport() {
    const now = new Date();
    const sessionDuration = now.getTime() - this.consoleCollector.startTime;

    let report = '';

    // 报告头部
    report += '='.repeat(80) + '\n';
    report += 'R1智能红外控制系统 - 调试报告\n';
    report += '='.repeat(80) + '\n';
    report += `生成时间: ${now.toISOString()}\n`;
    report += `会话时长: ${Math.round(sessionDuration / 1000)}秒\n`;
    report += `日志总数: ${this.consoleCollector.logs.length}\n`;
    report += `用户代理: ${navigator.userAgent}\n`;
    report += `页面URL: ${window.location.href}\n`;
    report += '\n';

    // 系统状态
    report += '-'.repeat(40) + ' 系统状态 ' + '-'.repeat(40) + '\n';
    try {
      const systemState = {
        currentModule: this.currentModule,
        isInitialized: this.isInitialized,
        modules: Object.keys(this.modules || {}),
        performance: this.performance,
        esp32Connected: this.esp32?.connected || false
      };
      report += JSON.stringify(systemState, null, 2) + '\n';
    } catch (e) {
      report += `系统状态获取失败: ${e.message}\n`;
    }
    report += '\n';

    // 内存信息
    report += '-'.repeat(40) + ' 内存信息 ' + '-'.repeat(40) + '\n';
    if (performance.memory) {
      report += `已用内存: ${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB\n`;
      report += `总内存: ${Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)}MB\n`;
      report += `内存限制: ${Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)}MB\n`;
    } else {
      report += '浏览器不支持内存监控\n';
    }
    report += '\n';

    // 错误统计
    report += '-'.repeat(40) + ' 错误统计 ' + '-'.repeat(40) + '\n';
    const logStats = {};
    this.consoleCollector.logs.forEach(log => {
      logStats[log.level] = (logStats[log.level] || 0) + 1;
    });
    Object.entries(logStats).forEach(([level, count]) => {
      report += `${level}: ${count}条\n`;
    });
    report += '\n';

    // 详细日志
    report += '-'.repeat(40) + ' 详细日志 ' + '-'.repeat(40) + '\n';
    this.consoleCollector.logs.forEach((log, index) => {
      const relativeTime = log.timestamp - this.consoleCollector.startTime;
      report += `[${index + 1}] [+${relativeTime}ms] [${log.level}] ${log.timeStr}\n`;
      report += `${log.message}\n`;

      // 如果有复杂对象，添加详细信息
      if (log.args && log.args.length > 0) {
        log.args.forEach((arg, argIndex) => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              report += `  参数${argIndex + 1}: ${JSON.stringify(arg, null, 2)}\n`;
            } catch (e) {
              report += `  参数${argIndex + 1}: [无法序列化]\n`;
            }
          }
        });
      }
      report += '\n';
    });

    return report;
  }

  /**
   * 设置系统状态查询事件响应 - 符合架构标准的模块间通信
   */
  setupSystemStatusResponders() {
    // 响应系统运行时间查询
    this.eventBus.on('system.uptime.request', (data) => {
      const uptime = this.performance ? Date.now() - this.performance.startTime : 0;
      if (data.callback) {
        data.callback({ uptime });
      }
    });

    // 响应活动模块数量查询
    this.eventBus.on('system.modules.count.request', (data) => {
      const count = Object.keys(this.modules || {}).length;
      if (data.callback) {
        data.callback({ count });
      }
    });

    // 响应ESP32状态查询
    this.eventBus.on('esp32.status.request', (data) => {
      const isConnected = this.esp32?.isConnected || false;
      if (data.callback) {
        data.callback({ isConnected });
      }
    });

    // 响应系统初始化状态查询
    this.eventBus.on('system.initialization.status.request', (data) => {
      const isInitialized = this.isInitialized || false;
      if (data.callback) {
        data.callback({ isInitialized });
      }
    });

    // 移除复杂的UI事件注册系统 - 模块直接使用addEventListener


  }


}

// 创建全局R1系统实例
window.R1System = new R1System();

// 页面加载完成后启动系统
document.addEventListener('DOMContentLoaded', () => {
  window.R1System.init().catch(error => {
    console.error('系统启动失败:', error);
  });
});

// 页面卸载前保存状态
window.addEventListener('beforeunload', () => {
  if (window.R1System.isInitialized) {
    window.R1System.saveSystemState();
  }
});

// 导出主系统类
window.R1SystemClass = R1System;

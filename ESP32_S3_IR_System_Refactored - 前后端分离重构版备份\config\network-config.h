#ifndef NETWORK_CONFIG_H
#define NETWORK_CONFIG_H

#include <WiFi.h>
#include <IPAddress.h>

// ==================== WiFi配置 ====================

// 主要WiFi网络配置 - 请修改为您的网络信息
#define WIFI_SSID "Your_WiFi_SSID"
#define WIFI_PASSWORD "Your_WiFi_Password"

// 备用WiFi网络配置（可选）
#define BACKUP_WIFI_SSID "Backup_WiFi_SSID"
#define BACKUP_WIFI_PASSWORD "Backup_WiFi_Password"

// WiFi连接配置
#define WIFI_CONNECTION_TIMEOUT 30000   // WiFi连接超时 (30秒)
#define MAX_CONNECTION_ATTEMPTS 3       // 最大连接尝试次数
#define CONNECTION_RETRY_DELAY 5000     // 连接重试延迟 (5秒)
#define WIFI_RECONNECT_INTERVAL 60000   // WiFi重连检查间隔 (1分钟)

// WiFi功率和性能配置
#define WIFI_POWER_SAVE_MODE WIFI_PS_NONE  // 禁用WiFi省电模式以获得最佳性能
#define WIFI_CHANNEL 0                     // 自动选择信道 (0 = 自动)
#define WIFI_MAX_TX_POWER 20               // 最大发射功率 (dBm)

// 主机名配置
#define WIFI_HOSTNAME "ESP32-S3-IR-Controller"
#define MDNS_HOSTNAME "esp32-ir"           // mDNS主机名

// ==================== AP模式配置 ====================

// AP模式基本配置
#define AP_SSID_PREFIX "ESP32-S3-IR-"      // AP SSID前缀
#define AP_PASSWORD "12345678"             // AP密码 (至少8位)
#define AP_CHANNEL 6                       // AP信道
#define AP_MAX_CONNECTIONS 4               // AP最大连接数
#define AP_HIDDEN false                    // AP是否隐藏

// AP模式网络配置
#define AP_IP_ADDRESS IPAddress(192, 168, 4, 1)      // AP IP地址
#define AP_GATEWAY IPAddress(192, 168, 4, 1)         // AP网关
#define AP_SUBNET IPAddress(255, 255, 255, 0)        // AP子网掩码

// AP模式超时配置
#define AP_MODE_TIMEOUT 300000             // AP模式超时 (5分钟)
#define AP_CLIENT_TIMEOUT 180000           // AP客户端超时 (3分钟)

// ==================== 网络服务配置 ====================

// Web服务器配置
#define WEB_SERVER_PORT 80
#define WEB_SERVER_MAX_CLIENTS 5
#define WEB_SERVER_TIMEOUT 30000           // 30秒超时

// WebSocket配置
#define WEBSOCKET_PATH "/ws"
#define WEBSOCKET_PORT 81                  // 可选的独立WebSocket端口
#define MAX_WEBSOCKET_CLIENTS 5
#define WEBSOCKET_PING_INTERVAL 30000      // WebSocket ping间隔 (30秒)
#define WEBSOCKET_PONG_TIMEOUT 10000       // WebSocket pong超时 (10秒)

// HTTP配置
#define HTTP_MAX_REQUEST_SIZE 8192         // 最大HTTP请求大小 (8KB)
#define HTTP_MAX_RESPONSE_SIZE 16384       // 最大HTTP响应大小 (16KB)
#define HTTP_KEEP_ALIVE_TIMEOUT 5000       // HTTP Keep-Alive超时 (5秒)

// ==================== 安全配置 ====================

// CORS配置
#define ENABLE_CORS true
#define CORS_ORIGIN "*"                    // 允许的源 (* = 所有源)
#define CORS_METHODS "GET,POST,PUT,DELETE,OPTIONS"
#define CORS_HEADERS "Content-Type,Authorization,X-Requested-With"

// 速率限制配置
#define ENABLE_RATE_LIMITING true
#define MAX_REQUESTS_PER_MINUTE 100        // 每分钟最大请求数
#define RATE_LIMIT_WINDOW 60000            // 速率限制窗口 (1分钟)
#define RATE_LIMIT_BLOCK_TIME 300000       // 限制阻止时间 (5分钟)

// 认证配置（可选）
#define ENABLE_AUTHENTICATION false        // 是否启用认证
#define API_KEY "your-api-key-here"        // API密钥
#define SESSION_TIMEOUT 3600000            // 会话超时 (1小时)

// ==================== DNS配置 ====================

// DNS服务器配置
#define PRIMARY_DNS IPAddress(8, 8, 8, 8)      // Google DNS
#define SECONDARY_DNS IPAddress(8, 8, 4, 4)    // Google DNS备用
#define DNS_TIMEOUT 5000                       // DNS查询超时 (5秒)

// mDNS配置
#define ENABLE_MDNS true
#define MDNS_SERVICE_NAME "esp32-ir"
#define MDNS_SERVICE_PROTOCOL "tcp"
#define MDNS_SERVICE_PORT 80

// ==================== 网络监控配置 ====================

// 连接监控
#define NETWORK_MONITOR_INTERVAL 10000     // 网络监控间隔 (10秒)
#define CONNECTION_CHECK_TIMEOUT 5000      // 连接检查超时 (5秒)
#define PING_HOST "*******"               // 用于连接测试的主机

// 网络统计
#define ENABLE_NETWORK_STATS true
#define STATS_RESET_INTERVAL 86400000      // 统计重置间隔 (24小时)

// ==================== 错误处理配置 ====================

// 网络错误处理
#define AUTO_RECONNECT true                // 自动重连
#define MAX_RECONNECT_ATTEMPTS 10          // 最大重连尝试次数
#define RECONNECT_BACKOFF_MULTIPLIER 2     // 重连退避倍数
#define MAX_RECONNECT_DELAY 300000         // 最大重连延迟 (5分钟)

// 故障转移配置
#define ENABLE_FAILOVER true               // 启用故障转移
#define FAILOVER_TO_AP_TIMEOUT 180000      // 故障转移到AP模式的超时 (3分钟)

// ==================== 网络工具函数 ====================

/**
 * 网络配置结构体
 */
struct NetworkConfig {
    // WiFi配置
    String ssid;
    String password;
    String hostname;
    
    // AP配置
    String apSSID;
    String apPassword;
    IPAddress apIP;
    
    // 服务配置
    int webServerPort;
    int maxClients;
    bool enableCORS;
    
    // 安全配置
    bool enableAuth;
    String apiKey;
    
    // 获取默认配置
    static NetworkConfig getDefault() {
        NetworkConfig config;
        config.ssid = WIFI_SSID;
        config.password = WIFI_PASSWORD;
        config.hostname = WIFI_HOSTNAME;
        config.apSSID = String(AP_SSID_PREFIX) + String(ESP.getChipId(), HEX);
        config.apPassword = AP_PASSWORD;
        config.apIP = AP_IP_ADDRESS;
        config.webServerPort = WEB_SERVER_PORT;
        config.maxClients = WEB_SERVER_MAX_CLIENTS;
        config.enableCORS = ENABLE_CORS;
        config.enableAuth = ENABLE_AUTHENTICATION;
        config.apiKey = API_KEY;
        return config;
    }
};

/**
 * 网络状态枚举
 */
enum class NetworkStatus {
    DISCONNECTED,      // 未连接
    CONNECTING,        // 连接中
    CONNECTED,         // 已连接
    AP_MODE,          // AP模式
    ERROR,            // 错误状态
    RECONNECTING      // 重连中
};

/**
 * 网络统计结构体
 */
struct NetworkStats {
    unsigned long connectTime;          // 连接时间
    unsigned long totalConnections;     // 总连接次数
    unsigned long failedConnections;    // 失败连接次数
    unsigned long bytesReceived;        // 接收字节数
    unsigned long bytesSent;           // 发送字节数
    unsigned long packetsReceived;      // 接收包数
    unsigned long packetsSent;         // 发送包数
    int signalStrength;                // 信号强度 (dBm)
    
    void reset() {
        connectTime = 0;
        totalConnections = 0;
        failedConnections = 0;
        bytesReceived = 0;
        bytesSent = 0;
        packetsReceived = 0;
        packetsSent = 0;
        signalStrength = 0;
    }
};

// ==================== 网络工具宏 ====================

// 检查WiFi连接状态
#define IS_WIFI_CONNECTED() (WiFi.status() == WL_CONNECTED)

// 获取WiFi信号强度
#define GET_WIFI_RSSI() WiFi.RSSI()

// 获取本地IP地址
#define GET_LOCAL_IP() WiFi.localIP()

// 检查是否为AP模式
#define IS_AP_MODE() (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA)

// ==================== 调试配置 ====================

#ifdef DEBUG_MODE
    #define DEBUG_NETWORK true
    #define NETWORK_DEBUG_PRINT(x) Serial.print(x)
    #define NETWORK_DEBUG_PRINTLN(x) Serial.println(x)
    #define NETWORK_DEBUG_PRINTF(format, ...) Serial.printf(format, ##__VA_ARGS__)
#else
    #define DEBUG_NETWORK false
    #define NETWORK_DEBUG_PRINT(x)
    #define NETWORK_DEBUG_PRINTLN(x)
    #define NETWORK_DEBUG_PRINTF(format, ...)
#endif

// ==================== 网络事件回调 ====================

/**
 * 网络事件类型
 */
enum class NetworkEvent {
    WIFI_CONNECTED,
    WIFI_DISCONNECTED,
    WIFI_GOT_IP,
    AP_STARTED,
    AP_STOPPED,
    CLIENT_CONNECTED,
    CLIENT_DISCONNECTED
};

/**
 * 网络事件回调函数类型
 */
typedef void (*NetworkEventCallback)(NetworkEvent event, void* data);

// ==================== 兼容性检查 ====================

#if !defined(ESP32)
    #error "This network configuration is designed for ESP32 only"
#endif

#if !defined(ARDUINO_ARCH_ESP32)
    #warning "Arduino ESP32 core not detected. Some features may not work correctly."
#endif

// ==================== 版本信息 ====================

#define NETWORK_CONFIG_VERSION "1.0.0"
#define NETWORK_CONFIG_DESCRIPTION "ESP32-S3 IR System Network Configuration"

#endif // NETWORK_CONFIG_H

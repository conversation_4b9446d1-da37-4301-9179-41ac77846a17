#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>

#include "PSRAMManager.h"
#include "MemoryAllocator.h"
#include "../../config/system-config.h"

// Forward declarations
class DataManager;
class IRController;
class WebServerManager;
class TaskManager;
class WSManager;
class NetworkSecurity;

/**
 * 系统管理器类
 * 
 * 负责整个ESP32-S3 IR系统的核心管理
 * 协调各个组件的初始化、运行和销毁
 * 
 * 核心功能：
 * - 系统初始化和启动
 * - 组件生命周期管理
 * - 系统模式管理（高性能/标准）
 * - 错误处理和恢复
 * - 内存监控和管理
 * - 系统状态监控
 */
class SystemManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param mode 系统运行模式
     */
    explicit SystemManager(SystemMode mode);
    
    /**
     * 析构函数
     */
    ~SystemManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化系统
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 主循环处理
     */
    void handleLoop();
    
    /**
     * 关闭系统
     */
    void shutdown();
    
    /**
     * 工厂重置
     */
    void factoryReset();
    
    // ==================== 系统状态管理 ====================
    
    /**
     * 获取系统模式
     * @return SystemMode 当前系统模式
     */
    SystemMode getSystemMode() const { return m_systemMode; }
    
    /**
     * 获取系统容量配置
     * @return SystemCapacity 系统容量配置
     */
    SystemCapacity getSystemCapacity() const { return m_systemCapacity; }
    
    /**
     * 获取系统模式字符串
     * @return const char* 系统模式字符串
     */
    const char* getSystemModeString() const;
    
    /**
     * 检查系统是否已初始化
     * @return bool 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    /**
     * 获取系统运行时间
     * @return unsigned long 运行时间（毫秒）
     */
    unsigned long getUptime() const;
    
    // ==================== 组件管理 ====================
    
    /**
     * 获取数据管理器
     * @return DataManager* 数据管理器指针
     */
    DataManager* getDataManager() const { return m_dataManager; }
    
    /**
     * 获取红外控制器
     * @return IRController* 红外控制器指针
     */
    IRController* getIRController() const { return m_irController; }
    
    /**
     * 获取Web服务器管理器
     * @return WebServerManager* Web服务器管理器指针
     */
    WebServerManager* getWebServerManager() const { return m_webServerManager; }
    
    /**
     * 获取任务管理器
     * @return TaskManager* 任务管理器指针
     */
    TaskManager* getTaskManager() const { return m_taskManager; }
    
    /**
     * 获取WebSocket管理器
     * @return WSManager* WebSocket管理器指针
     */
    WSManager* getWSManager() const { return m_wsManager; }

    /**
     * 获取网络安全管理器
     * @return NetworkSecurity* 网络安全管理器指针
     */
    NetworkSecurity* getNetworkSecurity() const { return m_networkSecurity; }
    
    // ==================== 内存管理 ====================
    
    /**
     * 打印内存状态
     */
    void printMemoryStatus();
    
    /**
     * 打印详细内存状态
     */
    void printDetailedMemoryStatus();
    
    /**
     * 获取空闲PSRAM大小
     * @return size_t 空闲PSRAM大小
     */
    size_t getFreePSRAM();
    
    /**
     * 获取空闲堆内存大小
     * @return size_t 空闲堆内存大小
     */
    size_t getFreeHeap();
    
    /**
     * 检查内存健康状态
     * @return bool 内存是否健康
     */
    bool checkMemoryHealth();
    
    // ==================== 错误处理 ====================
    
    /**
     * 记录错误日志
     * @param error 错误信息
     */
    void logError(const String& error);
    
    /**
     * 获取最后的错误信息
     * @return String 最后的错误信息
     */
    String getLastError() const { return m_lastError; }
    
    /**
     * 清除错误状态
     */
    void clearError();
    
    /**
     * 处理严重错误
     * @param error 错误信息
     * @param shouldRestart 是否应该重启
     */
    void handleCriticalError(const String& error, bool shouldRestart = true);
    
    // ==================== 系统监控 ====================
    
    /**
     * 获取系统统计信息
     * @return DynamicJsonDocument 系统统计信息
     */
    DynamicJsonDocument getSystemStats();
    
    /**
     * 获取系统状态
     * @return DynamicJsonDocument 系统状态
     */
    DynamicJsonDocument getSystemStatus();
    
    /**
     * 更新系统统计
     */
    void updateSystemStats();
    
    // ==================== 调试功能 ====================
    
#ifdef DEBUG_MODE
    /**
     * 打印所有任务信息
     */
    void debugPrintAllTasks();
    
    /**
     * 打印所有信号信息
     */
    void debugPrintAllSignals();
    
    /**
     * 打印系统状态
     */
    void debugPrintSystemState();
    
    /**
     * 启用调试模式
     */
    void enableDebugMode();
    
    /**
     * 禁用调试模式
     */
    void disableDebugMode();
#endif

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                    // 是否已初始化
    SystemMode m_systemMode;               // 系统运行模式
    SystemCapacity m_systemCapacity;       // 系统容量配置
    unsigned long m_startTime;             // 系统启动时间
    String m_lastError;                    // 最后的错误信息
    unsigned long m_lastStatsUpdate;       // 最后统计更新时间
    
    // 组件指针
    DataManager* m_dataManager;
    IRController* m_irController;
    WebServerManager* m_webServerManager;
    TaskManager* m_taskManager;
    WSManager* m_wsManager;
    NetworkSecurity* m_networkSecurity;
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化组件
     * @return bool 初始化是否成功
     */
    bool initializeComponents();
    
    /**
     * 创建数据管理器
     * @return bool 创建是否成功
     */
    bool createDataManager();
    
    /**
     * 创建红外控制器
     * @return bool 创建是否成功
     */
    bool createIRController();
    
    /**
     * 创建Web服务器管理器
     * @return bool 创建是否成功
     */
    bool createWebServerManager();
    
    /**
     * 创建任务管理器
     * @return bool 创建是否成功
     */
    bool createTaskManager();
    
    /**
     * 创建WebSocket管理器
     * @return bool 创建是否成功
     */
    bool createWSManager();

    /**
     * 创建网络安全管理器
     * @return bool 创建是否成功
     */
    bool createNetworkSecurity();

    // ==================== 系统集成方法 ====================

    /**
     * 初始化PSRAM和内存管理
     * @return bool 初始化是否成功
     */
    bool initializePSRAM();

    /**
     * 创建所有组件
     * @return bool 创建是否成功
     */
    bool createComponents();

    /**
     * 设置组件依赖关系
     * @return bool 设置是否成功
     */
    bool setupDependencies();

    /**
     * 启动系统服务
     * @return bool 启动是否成功
     */
    bool startServices();

    /**
     * 执行系统自检
     * @return bool 自检是否通过
     */
    bool performSystemSelfTest();

    /**
     * 打印系统信息
     */
    void printSystemInfo();
    
    /**
     * 设置组件依赖关系
     */
    void setupComponentDependencies();
    
    /**
     * 清理所有组件
     */
    void cleanupComponents();
    
    /**
     * 检查组件健康状态
     * @return bool 组件是否健康
     */
    bool checkComponentHealth();
    
    /**
     * 执行定期维护任务
     */
    void performMaintenance();
    
    /**
     * 记录系统日志
     * @param message 日志信息
     * @param level 日志级别
     */
    void logMessage(const String& message, int level = LOG_LEVEL_INFO);
};

#endif // SYSTEM_MANAGER_H

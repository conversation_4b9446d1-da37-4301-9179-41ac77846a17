#ifndef NETWORK_SECURITY_H
#define NETWORK_SECURITY_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <map>
#include "system-config.h"

/**
 * 网络安全管理器类
 *
 * 负责网络安全相关功能
 * 包括认证、授权、速率限制、CORS等
 */
class NetworkSecurity {
public:
    // ==================== 构造函数和析构函数 ====================

    /**
     * 构造函数
     */
    NetworkSecurity();

    /**
     * 析构函数
     */
    ~NetworkSecurity();

    // ==================== 系统生命周期 ====================

    /**
     * 初始化网络安全
     * @return bool 初始化是否成功
     */
    bool initialize();

    /**
     * 关闭网络安全
     */
    void shutdown();

    // ==================== 认证功能 ====================

    /**
     * 验证API密钥
     * @param apiKey API密钥
     * @return bool 验证是否通过
     */
    bool validateAPIKey(const String& apiKey);

    /**
     * 生成会话令牌
     * @param clientId 客户端ID
     * @return String 会话令牌
     */
    String generateSessionToken(const String& clientId);

    /**
     * 验证会话令牌
     * @param token 会话令牌
     * @return bool 验证是否通过
     */
    bool validateSessionToken(const String& token);

    /**
     * 撤销会话令牌
     * @param token 会话令牌
     * @return bool 撤销是否成功
     */
    bool revokeSessionToken(const String& token);

    /**
     * 清理过期令牌
     * @return int 清理的令牌数量
     */
    int cleanupExpiredTokens();

    // ==================== 速率限制 ====================

    /**
     * 检查速率限制
     * @param clientIP 客户端IP
     * @param endpoint 端点
     * @return bool 是否允许请求
     */
    bool checkRateLimit(const String& clientIP, const String& endpoint);

    /**
     * 记录请求
     * @param clientIP 客户端IP
     * @param endpoint 端点
     */
    void recordRequest(const String& clientIP, const String& endpoint);

    /**
     * 重置速率限制
     * @param clientIP 客户端IP
     */
    void resetRateLimit(const String& clientIP);

    /**
     * 获取速率限制状态
     * @param clientIP 客户端IP
     * @return DynamicJsonDocument 速率限制状态
     */
    DynamicJsonDocument getRateLimitStatus(const String& clientIP);

    // ==================== CORS处理 ====================

    /**
     * 验证CORS请求
     * @param origin 请求来源
     * @param method 请求方法
     * @return bool 是否允许
     */
    bool validateCORSRequest(const String& origin, const String& method);

    /**
     * 获取CORS头部
     * @param origin 请求来源
     * @return std::map<String, String> CORS头部映射
     */
    std::map<String, String> getCORSHeaders(const String& origin);

    // ==================== IP过滤 ====================

    /**
     * 检查IP是否被阻止
     * @param clientIP 客户端IP
     * @return bool 是否被阻止
     */
    bool isIPBlocked(const String& clientIP);

    /**
     * 阻止IP地址
     * @param clientIP 客户端IP
     * @param reason 阻止原因
     * @param duration 阻止时长（毫秒，0表示永久）
     */
    void blockIP(const String& clientIP, const String& reason, unsigned long duration = 0);

    /**
     * 解除IP阻止
     * @param clientIP 客户端IP
     * @return bool 解除是否成功
     */
    bool unblockIP(const String& clientIP);

    /**
     * 获取阻止的IP列表
     * @return DynamicJsonDocument 阻止的IP列表
     */
    DynamicJsonDocument getBlockedIPs();

    // ==================== 安全统计 ====================

    /**
     * 获取安全统计信息
     * @return DynamicJsonDocument 安全统计
     */
    DynamicJsonDocument getSecurityStats();

    /**
     * 重置安全统计
     */
    void resetSecurityStats();

    /**
     * 记录安全事件
     * @param eventType 事件类型
     * @param clientIP 客户端IP
     * @param details 事件详情
     */
    void logSecurityEvent(const String& eventType, const String& clientIP, const String& details);

private:
    // ==================== 私有成员变量 ====================

    bool m_initialized;                          // 是否已初始化

    // 会话管理
    struct SessionInfo {
        String clientId;
        unsigned long createdTime;
        unsigned long lastAccessTime;
        bool isValid;
    };
    std::map<String, SessionInfo> m_sessions;    // 会话映射表

    // 速率限制
    struct RateLimitInfo {
        unsigned long windowStart;
        int requestCount;
        bool isBlocked;
        unsigned long blockUntil;
    };
    std::map<String, RateLimitInfo> m_rateLimits; // 速率限制映射表

    // IP阻止
    struct BlockedIPInfo {
        String reason;
        unsigned long blockedTime;
        unsigned long blockDuration;
        bool isPermanent;
    };
    std::map<String, BlockedIPInfo> m_blockedIPs; // 阻止IP映射表

    // 安全统计
    unsigned long m_totalRequests;               // 总请求数
    unsigned long m_blockedRequests;             // 被阻止的请求数
    unsigned long m_rateLimitedRequests;         // 被速率限制的请求数
    unsigned long m_invalidTokenRequests;        // 无效令牌请求数
    unsigned long m_corsViolations;              // CORS违规数

    // ==================== 私有方法 ====================

    /**
     * 生成随机令牌
     * @param length 令牌长度
     * @return String 随机令牌
     */
    String generateRandomToken(int length = 32);

    /**
     * 计算令牌哈希
     * @param token 令牌
     * @return String 令牌哈希
     */
    String calculateTokenHash(const String& token);

    /**
     * 检查会话是否过期
     * @param session 会话信息
     * @return bool 是否过期
     */
    bool isSessionExpired(const SessionInfo& session);

    /**
     * 检查速率限制窗口
     * @param rateLimitInfo 速率限制信息
     * @return bool 是否需要重置窗口
     */
    bool shouldResetRateLimitWindow(const RateLimitInfo& rateLimitInfo);

    /**
     * 检查IP阻止是否过期
     * @param blockedInfo 阻止信息
     * @return bool 是否过期
     */
    bool isIPBlockExpired(const BlockedIPInfo& blockedInfo);

    /**
     * 清理过期数据
     */
    void performCleanup();

    /**
     * 更新安全统计
     * @param eventType 事件类型
     */
    void updateSecurityStats(const String& eventType);

    // ==================== 访问控制 ====================
    
    /**
     * 检查IP是否被允许访问
     * @param ipAddress IP地址
     * @return bool 是否允许
     */
    bool isIPAllowed(const String& ipAddress);
    
    /**
     * 添加IP到白名单
     * @param ipAddress IP地址
     * @return bool 添加是否成功
     */
    bool addToWhitelist(const String& ipAddress);
    
    /**
     * 从白名单移除IP
     * @param ipAddress IP地址
     * @return bool 移除是否成功
     */
    bool removeFromWhitelist(const String& ipAddress);
    
    /**
     * 添加IP到黑名单
     * @param ipAddress IP地址
     * @return bool 添加是否成功
     */
    bool addToBlacklist(const String& ipAddress);
    
    /**
     * 从黑名单移除IP
     * @param ipAddress IP地址
     * @return bool 移除是否成功
     */
    bool removeFromBlacklist(const String& ipAddress);
    
    /**
     * 清空白名单
     */
    void clearWhitelist();
    
    /**
     * 清空黑名单
     */
    void clearBlacklist();
    
    // ==================== 频率限制 ====================
    
    /**
     * 检查请求频率是否超限
     * @param ipAddress IP地址
     * @param endpoint 端点
     * @return bool 是否超限
     */
    bool isRateLimited(const String& ipAddress, const String& endpoint = "");
    
    /**
     * 记录请求
     * @param ipAddress IP地址
     * @param endpoint 端点
     */
    void recordRequest(const String& ipAddress, const String& endpoint = "");
    
    /**
     * 设置频率限制
     * @param endpoint 端点（空字符串表示全局）
     * @param maxRequests 最大请求数
     * @param timeWindow 时间窗口（毫秒）
     * @return bool 设置是否成功
     */
    bool setRateLimit(const String& endpoint, int maxRequests, unsigned long timeWindow);
    
    /**
     * 移除频率限制
     * @param endpoint 端点
     * @return bool 移除是否成功
     */
    bool removeRateLimit(const String& endpoint);
    
    /**
     * 清理过期的请求记录
     */
    void cleanupExpiredRequests();
    
    // ==================== API密钥验证 ====================
    
    /**
     * 验证API密钥
     * @param apiKey API密钥
     * @return bool 密钥是否有效
     */
    bool validateAPIKey(const String& apiKey);
    
    /**
     * 生成新的API密钥
     * @param description 密钥描述
     * @return String 新生成的API密钥
     */
    String generateAPIKey(const String& description = "");
    
    /**
     * 撤销API密钥
     * @param apiKey API密钥
     * @return bool 撤销是否成功
     */
    bool revokeAPIKey(const String& apiKey);
    
    /**
     * 获取所有API密钥信息
     * @return DynamicJsonDocument API密钥列表
     */
    DynamicJsonDocument getAPIKeys();
    
    /**
     * 清空所有API密钥
     */
    void clearAPIKeys();
    
    // ==================== 安全事件 ====================
    
    /**
     * 记录安全事件
     * @param eventType 事件类型
     * @param ipAddress IP地址
     * @param description 事件描述
     */
    void logSecurityEvent(const String& eventType, const String& ipAddress, const String& description);
    
    /**
     * 获取安全事件日志
     * @param limit 返回数量限制
     * @return DynamicJsonDocument 安全事件列表
     */
    DynamicJsonDocument getSecurityEvents(int limit = 100);
    
    /**
     * 清空安全事件日志
     */
    void clearSecurityEvents();
    
    /**
     * 检查是否存在可疑活动
     * @param ipAddress IP地址
     * @return bool 是否存在可疑活动
     */
    bool hasSuspiciousActivity(const String& ipAddress);
    
    // ==================== 配置管理 ====================
    
    /**
     * 获取安全配置
     * @return DynamicJsonDocument 安全配置
     */
    DynamicJsonDocument getSecurityConfig();
    
    /**
     * 更新安全配置
     * @param config 新配置
     * @return bool 更新是否成功
     */
    bool updateSecurityConfig(const DynamicJsonDocument& config);
    
    /**
     * 重置为默认配置
     */
    void resetToDefaultConfig();
    
    /**
     * 保存配置到文件
     * @return bool 保存是否成功
     */
    bool saveConfigToFile();
    
    /**
     * 从文件加载配置
     * @return bool 加载是否成功
     */
    bool loadConfigFromFile();
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取安全统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getSecurityStatistics();
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    // ==================== 调试功能 ====================
    
    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    void enableDebugMode(bool enable);
    
    /**
     * 获取调试信息
     * @return String 调试信息
     */
    String getDebugInfo() const;

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                 // 是否已初始化
    bool m_debugMode;                   // 调试模式
    bool m_whitelistEnabled;            // 是否启用白名单
    bool m_blacklistEnabled;            // 是否启用黑名单
    bool m_rateLimitEnabled;            // 是否启用频率限制
    bool m_apiKeyRequired;              // 是否需要API密钥
    
    // IP列表
    std::vector<String> m_whitelist;    // IP白名单
    std::vector<String> m_blacklist;    // IP黑名单
    
    // 频率限制
    struct RateLimitRule {
        int maxRequests;
        unsigned long timeWindow;
    };
    std::map<String, RateLimitRule> m_rateLimits;
    
    struct RequestRecord {
        unsigned long timestamp;
        String endpoint;
    };
    std::map<String, std::vector<RequestRecord>> m_requestHistory;
    
    // API密钥
    struct APIKeyInfo {
        String key;
        String description;
        unsigned long createdTime;
        unsigned long lastUsedTime;
        int usageCount;
        bool active;
    };
    std::vector<APIKeyInfo> m_apiKeys;
    
    // 安全事件
    struct SecurityEvent {
        String eventType;
        String ipAddress;
        String description;
        unsigned long timestamp;
    };
    std::vector<SecurityEvent> m_securityEvents;
    
    // 统计信息
    unsigned long m_totalRequests;      // 总请求数
    unsigned long m_blockedRequests;    // 被阻止的请求数
    unsigned long m_rateLimitedRequests; // 频率限制的请求数
    unsigned long m_invalidAPIKeyRequests; // 无效API密钥请求数
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化默认配置
     */
    void initializeDefaultConfig();
    
    /**
     * 检查IP格式是否有效
     * @param ipAddress IP地址
     * @return bool 格式是否有效
     */
    bool isValidIPAddress(const String& ipAddress);
    
    /**
     * 生成随机字符串
     * @param length 字符串长度
     * @return String 随机字符串
     */
    String generateRandomString(int length);
    
    /**
     * 清理过期的安全事件
     */
    void cleanupExpiredSecurityEvents();
    
    /**
     * 记录调试信息
     * @param message 调试信息
     */
    void debugLog(const String& message);
};

#endif // NETWORK_SECURITY_H

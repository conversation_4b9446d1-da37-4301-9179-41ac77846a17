#ifndef SYSTEM_DIAGNOSTICS_H
#define SYSTEM_DIAGNOSTICS_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include "../../config/system-config.h"

// Forward declaration
class SystemManager;

/**
 * 系统诊断类
 * 
 * 负责系统监控、诊断和性能分析
 * 提供详细的系统健康报告和故障诊断
 */
class SystemDiagnostics {
public:
    // ==================== 诊断级别 ====================
    enum class DiagnosticLevel {
        BASIC = 0,      // 基本诊断
        DETAILED = 1,   // 详细诊断
        COMPREHENSIVE = 2  // 全面诊断
    };
    
    // ==================== 诊断结果状态 ====================
    enum class DiagnosticStatus {
        PASS = 0,       // 通过
        WARNING = 1,    // 警告
        FAIL = 2,       // 失败
        CRITICAL = 3    // 严重
    };
    
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param systemManager 系统管理器指针
     */
    explicit SystemDiagnostics(SystemManager* systemManager);
    
    /**
     * 析构函数
     */
    ~SystemDiagnostics();
    
    // ==================== 系统诊断 ====================
    
    /**
     * 执行完整系统诊断
     * @param level 诊断级别
     * @return DynamicJsonDocument 诊断报告
     */
    DynamicJsonDocument performSystemDiagnostics(DiagnosticLevel level = DiagnosticLevel::DETAILED);
    
    /**
     * 执行内存诊断
     * @return DynamicJsonDocument 内存诊断报告
     */
    DynamicJsonDocument performMemoryDiagnostics();
    
    /**
     * 执行硬件诊断
     * @return DynamicJsonDocument 硬件诊断报告
     */
    DynamicJsonDocument performHardwareDiagnostics();
    
    /**
     * 执行网络诊断
     * @return DynamicJsonDocument 网络诊断报告
     */
    DynamicJsonDocument performNetworkDiagnostics();
    
    /**
     * 执行组件诊断
     * @return DynamicJsonDocument 组件诊断报告
     */
    DynamicJsonDocument performComponentDiagnostics();
    
    /**
     * 执行性能诊断
     * @return DynamicJsonDocument 性能诊断报告
     */
    DynamicJsonDocument performPerformanceDiagnostics();
    
    // ==================== 系统监控 ====================
    
    /**
     * 开始系统监控
     * @param interval 监控间隔（毫秒）
     * @return bool 启动是否成功
     */
    bool startSystemMonitoring(unsigned long interval = 60000);
    
    /**
     * 停止系统监控
     */
    void stopSystemMonitoring();
    
    /**
     * 检查是否正在监控
     * @return bool 是否正在监控
     */
    bool isMonitoring() const { return m_isMonitoring; }
    
    /**
     * 处理监控循环
     */
    void handleMonitoringLoop();
    
    /**
     * 获取监控数据
     * @return DynamicJsonDocument 监控数据
     */
    DynamicJsonDocument getMonitoringData();
    
    // ==================== 性能分析 ====================
    
    /**
     * 开始性能分析
     * @param duration 分析持续时间（毫秒）
     * @return bool 启动是否成功
     */
    bool startPerformanceAnalysis(unsigned long duration = 300000);
    
    /**
     * 停止性能分析
     */
    void stopPerformanceAnalysis();
    
    /**
     * 获取性能分析报告
     * @return DynamicJsonDocument 性能分析报告
     */
    DynamicJsonDocument getPerformanceAnalysisReport();
    
    // ==================== 故障诊断 ====================
    
    /**
     * 诊断系统故障
     * @param symptoms 故障症状描述
     * @return DynamicJsonDocument 故障诊断报告
     */
    DynamicJsonDocument diagnoseFault(const String& symptoms);
    
    /**
     * 获取故障建议
     * @param faultCode 故障代码
     * @return DynamicJsonDocument 故障建议
     */
    DynamicJsonDocument getFaultRecommendations(const String& faultCode);
    
    /**
     * 执行自动修复
     * @param faultCode 故障代码
     * @return bool 修复是否成功
     */
    bool performAutoRepair(const String& faultCode);
    
    // ==================== 健康评分 ====================
    
    /**
     * 计算系统健康评分
     * @return int 健康评分 (0-100)
     */
    int calculateSystemHealthScore();
    
    /**
     * 获取健康评分详情
     * @return DynamicJsonDocument 健康评分详情
     */
    DynamicJsonDocument getHealthScoreDetails();
    
    /**
     * 获取健康趋势
     * @return DynamicJsonDocument 健康趋势数据
     */
    DynamicJsonDocument getHealthTrend();
    
    // ==================== 报告生成 ====================
    
    /**
     * 生成系统报告
     * @param includeHistory 是否包含历史数据
     * @return DynamicJsonDocument 系统报告
     */
    DynamicJsonDocument generateSystemReport(bool includeHistory = false);
    
    /**
     * 生成故障报告
     * @return DynamicJsonDocument 故障报告
     */
    DynamicJsonDocument generateFaultReport();
    
    /**
     * 生成性能报告
     * @return DynamicJsonDocument 性能报告
     */
    DynamicJsonDocument generatePerformanceReport();
    
    /**
     * 导出诊断数据
     * @param format 导出格式 ("json", "csv", "txt")
     * @return String 导出的数据
     */
    String exportDiagnosticData(const String& format = "json");

private:
    // ==================== 私有成员变量 ====================
    
    SystemManager* m_systemManager;      // 系统管理器指针
    bool m_isMonitoring;                 // 是否正在监控
    bool m_isAnalyzing;                  // 是否正在分析
    
    unsigned long m_monitoringInterval;  // 监控间隔
    unsigned long m_lastMonitoringTime;  // 最后监控时间
    unsigned long m_analysisStartTime;   // 分析开始时间
    unsigned long m_analysisDuration;    // 分析持续时间
    
    // 监控数据存储
    struct MonitoringData {
        unsigned long timestamp;
        size_t freeHeap;
        size_t freePSRAM;
        int cpuUsage;
        int memoryUsage;
        int networkActivity;
        int systemLoad;
    };
    
    static const size_t MAX_MONITORING_RECORDS = 100;
    MonitoringData m_monitoringHistory[MAX_MONITORING_RECORDS];
    size_t m_monitoringIndex;
    size_t m_monitoringCount;
    
    // 健康评分历史
    struct HealthScore {
        unsigned long timestamp;
        int score;
        String details;
    };
    
    static const size_t MAX_HEALTH_RECORDS = 50;
    HealthScore m_healthHistory[MAX_HEALTH_RECORDS];
    size_t m_healthIndex;
    size_t m_healthCount;
    
    // ==================== 私有方法 ====================
    
    /**
     * 记录监控数据
     */
    void recordMonitoringData();
    
    /**
     * 记录健康评分
     * @param score 健康评分
     * @param details 详情
     */
    void recordHealthScore(int score, const String& details);
    
    /**
     * 分析系统趋势
     * @return DynamicJsonDocument 趋势分析
     */
    DynamicJsonDocument analyzeTrends();
    
    /**
     * 检测异常
     * @return DynamicJsonDocument 异常检测结果
     */
    DynamicJsonDocument detectAnomalies();
    
    /**
     * 获取诊断状态字符串
     * @param status 诊断状态
     * @return String 状态字符串
     */
    String getDiagnosticStatusString(DiagnosticStatus status);
    
    /**
     * 获取诊断级别字符串
     * @param level 诊断级别
     * @return String 级别字符串
     */
    String getDiagnosticLevelString(DiagnosticLevel level);
    
    /**
     * 计算CPU使用率
     * @return int CPU使用率百分比
     */
    int calculateCPUUsage();
    
    /**
     * 计算内存使用率
     * @return int 内存使用率百分比
     */
    int calculateMemoryUsage();
    
    /**
     * 计算网络活动
     * @return int 网络活动指标
     */
    int calculateNetworkActivity();
    
    /**
     * 计算系统负载
     * @return int 系统负载指标
     */
    int calculateSystemLoad();
};

#endif // SYSTEM_DIAGNOSTICS_H

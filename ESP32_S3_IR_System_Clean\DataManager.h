/*
 * DataManager.h - Data Management with Zero Global Memory Allocation
 * 
 * All std::vector and dynamic objects are created after PSRAM initialization
 * Uses smart pointers and delayed initialization to avoid PSRAM conflicts
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <Arduino.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <vector>
#include <memory>
#include <map>

// ==================== Data Structures ====================

struct SignalData {
    String id;
    String name;
    String type;
    String protocol;
    String data;
    String rawData;
    int frequency;
    unsigned long timestamp;
    bool isActive;
    int useCount;
    String description;
    
    SignalData() : frequency(38000), timestamp(0), isActive(true), useCount(0) {}
    
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

struct TimerData {
    String id;
    String name;
    String signalId;
    String schedule;
    bool isActive;
    unsigned long timestamp;
    unsigned long lastExecuted;
    int executionCount;
    
    TimerData() : isActive(true), timestamp(0), lastExecuted(0), executionCount(0) {}
    
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

struct TaskData {
    String id;
    String name;
    String type;
    String config;
    bool isActive;
    unsigned long timestamp;
    unsigned long lastExecuted;
    int executionCount;
    
    TaskData() : isActive(true), timestamp(0), lastExecuted(0), executionCount(0) {}
    
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

// ==================== DataManager Class ====================

class DataManager {
public:
    DataManager();
    ~DataManager();
    
    // Initialization
    bool initialize();
    bool isInitialized() const { return m_initialized; }
    
    // Signal Management
    bool addSignal(const SignalData& signal);
    bool removeSignal(const String& id);
    bool updateSignal(const String& id, const SignalData& signal);
    SignalData getSignal(const String& id) const;
    DynamicJsonDocument getSignalsJSON() const;
    int getSignalCount() const;
    void clearAllSignals();
    
    // Timer Management
    bool addTimer(const TimerData& timer);
    bool removeTimer(const String& id);
    bool updateTimer(const String& id, const TimerData& timer);
    TimerData getTimer(const String& id) const;
    DynamicJsonDocument getTimersJSON() const;
    int getTimerCount() const;
    void clearAllTimers();
    
    // Task Management
    bool addTask(const TaskData& task);
    bool removeTask(const String& id);
    bool updateTask(const String& id, const TaskData& task);
    TaskData getTask(const String& id) const;
    DynamicJsonDocument getTasksJSON() const;
    int getTaskCount() const;
    void clearAllTasks();
    
    // Configuration Management
    bool setConfig(const String& key, const String& value);
    String getConfig(const String& key, const String& defaultValue = "") const;
    bool hasConfig(const String& key) const;
    DynamicJsonDocument getConfigJSON() const;
    bool setConfigJSON(const DynamicJsonDocument& config);
    
    // Data Persistence
    bool saveAllData();
    bool loadAllData();
    void clearAllData();
    
    // System Information
    size_t getUsedSpace() const;
    size_t getFreeSpace() const;
    DynamicJsonDocument getSystemStatus() const;

private:
    bool m_initialized;
    
    // File paths
    static const char* SIGNALS_FILE;
    static const char* TIMERS_FILE;
    static const char* TASKS_FILE;
    static const char* CONFIG_FILE;
    
    // Data storage - created after PSRAM is available
    std::unique_ptr<std::vector<SignalData>> m_signals;
    std::unique_ptr<std::vector<TimerData>> m_timers;
    std::unique_ptr<std::vector<TaskData>> m_tasks;
    std::unique_ptr<Preferences> m_preferences;

    // Configuration storage
    std::unique_ptr<std::map<String, String>> m_config;
    
    // Helper methods
    bool initializeStorage();
    bool createDataDirectories();
    String generateUniqueId(const String& prefix) const;
    
    // File operations
    bool loadSignalsFromFile();
    bool saveSignalsToFile();
    bool loadTimersFromFile();
    bool saveTimersToFile();
    bool loadTasksFromFile();
    bool saveTasksToFile();
    bool loadConfigFromFile();
    bool saveConfigToFile();
    
    // Search helpers
    int findSignalIndex(const String& id) const;
    int findTimerIndex(const String& id) const;
    int findTaskIndex(const String& id) const;
    
    // Validation
    bool validateSignalData(const SignalData& signal) const;
    bool validateTimerData(const TimerData& timer) const;
    bool validateTaskData(const TaskData& task) const;
};

#endif // DATA_MANAGER_H

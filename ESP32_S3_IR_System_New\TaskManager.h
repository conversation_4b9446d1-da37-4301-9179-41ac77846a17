/*
 * TaskManager.h - 任务管理器（100% API兼容）
 * 
 * 零全局内存分配的任务管理系统
 * 完全兼容原前端的所有任务相关API调用
 */

#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <memory>
#include <functional>

// Forward declarations
class DataManager;
class IRController;

// Task execution states
enum class TaskState {
    PENDING,
    RUNNING,
    PAUSED,
    COMPLETED,
    FAILED,
    CANCELLED
};

// Task execution context
struct TaskContext {
    String taskId;
    String currentSignalId;
    int currentStep;
    int totalSteps;
    unsigned long startTime;
    unsigned long lastStepTime;
    TaskState state;
    String errorMessage;
    
    TaskContext() : currentStep(0), totalSteps(0), startTime(0), lastStepTime(0), state(TaskState::PENDING) {}
};

class TaskManager {
public:
    TaskManager();
    ~TaskManager();
    
    bool initialize();
    void handleLoop();
    
    // Task control (API Compatible)
    bool startTask(const String& taskId);
    bool stopTask(const String& taskId);
    bool pauseTask(const String& taskId);
    bool resumeTask(const String& taskId);
    bool cancelTask(const String& taskId);
    
    // Task status (API Compatible)
    DynamicJsonDocument getTaskStatus(const String& taskId) const;
    DynamicJsonDocument getAllTasksStatus() const;
    bool isTaskRunning(const String& taskId) const;
    
    // Batch operations (API Compatible)
    bool executeBatch(const String& signalIds, int delayMs = 1000);
    bool stopAllTasks();
    bool pauseAllTasks();
    bool resumeAllTasks();
    
    // Timer integration (API Compatible)
    bool scheduleTask(const String& taskId, unsigned long delayMs);
    bool scheduleRecurringTask(const String& taskId, unsigned long intervalMs);
    bool cancelScheduledTask(const String& taskId);
    
    // Statistics and monitoring
    DynamicJsonDocument getStatistics() const;
    int getActiveTaskCount() const;
    int getCompletedTaskCount() const;
    
    // Set component references
    void setDataManager(DataManager* dataManager) { m_dataManager = dataManager; }
    void setIRController(IRController* irController) { m_irController = irController; }

private:
    bool m_initialized;
    
    // Component references
    DataManager* m_dataManager;
    IRController* m_irController;
    
    // Task execution contexts - created after PSRAM is available
    std::unique_ptr<std::vector<TaskContext>> m_runningTasks;
    std::unique_ptr<std::vector<TaskContext>> m_scheduledTasks;
    
    // Statistics
    int m_totalTasksExecuted;
    int m_totalTasksCompleted;
    int m_totalTasksFailed;
    unsigned long m_lastExecutionTime;
    
    // Internal methods
    bool initializeStorage();
    bool executeTask(const String& taskId);
    bool executeTaskStep(TaskContext& context);
    void updateTaskContext(TaskContext& context, TaskState newState, const String& error = "");
    void cleanupCompletedTasks();
    
    // Task context management
    TaskContext* findRunningTask(const String& taskId);
    TaskContext* findScheduledTask(const String& taskId);
    bool removeRunningTask(const String& taskId);
    bool removeScheduledTask(const String& taskId);
    
    // Signal execution
    bool executeSignal(const String& signalId);
    bool executeSignalSequence(const String& signalIds, int delayMs);
    
    // Validation
    bool validateTaskId(const String& taskId) const;
    TaskState stringToTaskState(const String& stateStr) const;
    String taskStateToString(TaskState state) const;
};

#endif // TASK_MANAGER_H

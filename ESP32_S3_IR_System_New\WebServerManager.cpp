#include "WebServerManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "TaskManager.h"
#include "WSManager.h"

WebServerManager::WebServerManager() 
    : m_initialized(false)
    , m_server(nullptr)
    , m_data<PERSON>anager(nullptr)
    , m_ir<PERSON><PERSON>roller(nullptr)
    , m_task<PERSON>anager(nullptr)
    , m_wsManager(nullptr)
    , m_totalRequests(0)
    , m_lastRequestTime(0)
{
    Serial.println("WebServerManager created (no memory allocated)");
}

WebServerManager::~WebServerManager() {
    // Server is managed by SystemManager
}

bool WebServerManager::initialize(AsyncWebServer* server, DataManager* dataManager, 
                                 IRController* irController, TaskManager* taskManager, WSManager* wsManager) {
    Serial.println("🌐 Initializing WebServerManager...");
    
    if (m_initialized) {
        Serial.println("WebServerManager already initialized");
        return true;
    }
    
    if (!server || !dataManager || !irController || !taskManager || !wsManager) {
        Serial.println("❌ Invalid component pointers");
        return false;
    }
    
    m_server = server;
    m_dataManager = dataManager;
    m_irController = irController;
    m_taskManager = taskManager;
    m_wsManager = wsManager;
    
    // Set component references
    m_irController->setDataManager(m_dataManager);
    m_taskManager->setDataManager(m_dataManager);
    m_taskManager->setIRController(m_irController);
    
    // Setup server
    setupCORS();
    setupRoutes();
    setupStaticFiles();
    setupErrorHandlers();
    
    m_initialized = true;
    Serial.println("✅ WebServerManager initialized successfully");
    
    return true;
}

void WebServerManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // WebServer loop is handled automatically by ESPAsyncWebServer
    // No additional processing needed
}

DynamicJsonDocument WebServerManager::getServerStats() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;
    
    JsonObject data = doc.createNestedObject("data");
    data["initialized"] = m_initialized;
    data["totalRequests"] = m_totalRequests;
    data["lastRequestTime"] = m_lastRequestTime;
    data["uptime"] = millis();
    
    data["timestamp"] = millis();
    
    return doc;
}

// ==================== Setup Methods ====================

void WebServerManager::setupCORS() {
    Serial.println("🔗 Setting up CORS headers...");
    
    // Add default CORS headers
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
    DefaultHeaders::Instance().addHeader("Access-Control-Max-Age", "86400");
}

void WebServerManager::setupRoutes() {
    Serial.println("🛣️  Setting up API routes...");

    // ==================== Test API ====================
    m_server->on("/api/test", HTTP_GET, [this](AsyncWebServerRequest* request) {
        DynamicJsonDocument response(256);
        response["success"] = true;
        response["message"] = "API is working";
        response["timestamp"] = millis();

        sendJsonResponse(request, response);
    });

    // ==================== System API ====================
    m_server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemStatus(request);
    });
    
    m_server->on("/api/system/info", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemInfo(request);
    });
    
    m_server->on("/api/system/restart", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSystemRestart(request);
    });
    
    // ==================== Signal API ====================
    m_server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignals(request);
    });
    
    m_server->on("/api/signals/send", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSignalsSend(request);
    });
    
    m_server->on("/api/signals/learn", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSignalsLearn(request);
    });
    
    m_server->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSignalsLearnStop(request);
    });
    
    m_server->on("/api/signals/learn/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignalsLearnStatus(request);
    });
    
    m_server->on("/api/signals/batch", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSignalsBatch(request);
    });
    
    m_server->on("/api/signals/delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostSignalsDelete(request);
    });
    
    // ==================== Task API ====================
    m_server->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTasks(request);
    });
    
    m_server->on("/api/tasks", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTasks(request);
    });
    
    m_server->on("/api/tasks/control", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTasksControl(request);
    });
    
    m_server->on("/api/tasks/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTasksStatus(request);
    });
    
    m_server->on("/api/tasks/delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTasksDelete(request);
    });
    
    // ==================== Timer API ====================
    m_server->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTimers(request);
    });
    
    m_server->on("/api/timers", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTimers(request);
    });
    
    m_server->on("/api/timers/toggle", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTimersToggle(request);
    });
    
    m_server->on("/api/timers/update", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTimersUpdate(request);
    });
    
    m_server->on("/api/timers/delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostTimersDelete(request);
    });
    
    // ==================== Data API ====================
    m_server->on("/api/data/export", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetDataExport(request);
    });
    
    m_server->on("/api/data/import", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostDataImport(request);
    });
    
    m_server->on("/api/data/backup", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostDataBackup(request);
    });
    
    m_server->on("/api/data/clear", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostDataClear(request);
    });
    
    // ==================== Config API ====================
    m_server->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetConfig(request);
    });
    
    m_server->on("/api/config", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostConfig(request);
    });
    
    m_server->on("/api/config/reset", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostConfigReset(request);
    });
    
    // ==================== WebSocket API ====================
    m_server->on("/api/websocket/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetWebSocketStats(request);
    });
    
    m_server->on("/api/websocket/clients", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetWebSocketClients(request);
    });
    
    m_server->on("/api/websocket/broadcast", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostWebSocketBroadcast(request);
    });
    
    // ==================== Batch API ====================
    m_server->on("/api/batch", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePostBatch(request);
    });
    
    // Handle request body for POST requests
    m_server->onRequestBody(onRequestBody);
    
    Serial.println("✅ API routes configured");
}

void WebServerManager::setupStaticFiles() {
    Serial.println("📁 Setting up static file serving...");
    
    // Serve static files from SPIFFS
    m_server->serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    Serial.println("✅ Static file serving configured");
}

void WebServerManager::setupErrorHandlers() {
    Serial.println("❌ Setting up error handlers...");
    
    // Handle OPTIONS requests for CORS
    m_server->onNotFound([this](AsyncWebServerRequest* request) {
        if (request->method() == HTTP_OPTIONS) {
            handleCORSPreflight(request);
        } else {
            sendNotFoundResponse(request);
        }
    });
    
    Serial.println("✅ Error handlers configured");
}

// ==================== System API Handlers ====================

void WebServerManager::handleGetSystemStatus(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    DynamicJsonDocument response(1024);
    response["success"] = true;

    JsonObject data = response.createNestedObject("data");
    data["uptime"] = millis();
    data["freeHeap"] = ESP.getFreeHeap();
    data["freePsram"] = psramFound() ? ESP.getFreePsram() : 0;
    data["chipModel"] = ESP.getChipModel();
    data["cpuFreq"] = ESP.getCpuFreqMHz();
    data["flashSize"] = ESP.getFlashChipSize();
    data["esp32Connected"] = true; // Always true since we ARE the ESP32

    // Component status
    JsonObject components = data.createNestedObject("components");
    components["dataManager"] = m_dataManager ? m_dataManager->isInitialized() : false;
    components["irController"] = m_irController ? true : false;
    components["taskManager"] = m_taskManager ? true : false;
    components["wsManager"] = m_wsManager ? true : false;

    // Statistics
    if (m_dataManager) {
        JsonObject stats = data.createNestedObject("statistics");
        stats["signalCount"] = m_dataManager->getSignalCount();
        stats["timerCount"] = m_dataManager->getTimerCount();
        stats["taskCount"] = m_dataManager->getTaskCount();
    }

    if (m_wsManager) {
        data["connectedClients"] = m_wsManager->getConnectedClients();
    }

    data["timestamp"] = millis();

    sendJsonResponse(request, response);
}

void WebServerManager::handleGetSystemInfo(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    DynamicJsonDocument response(512);
    response["success"] = true;

    JsonObject data = response.createNestedObject("data");
    data["version"] = "2.0.0";
    data["architecture"] = "ESP32-S3 Clean Architecture";
    data["chipModel"] = ESP.getChipModel();
    data["chipRevision"] = ESP.getChipRevision();
    data["sdkVersion"] = ESP.getSdkVersion();
    data["flashSize"] = ESP.getFlashChipSize();
    data["psramSize"] = psramFound() ? ESP.getPsramSize() : 0;
    data["cpuFreq"] = ESP.getCpuFreqMHz();

    data["features"] = "IR Control, Task Management, WebSocket, PSRAM Optimized";
    data["timestamp"] = millis();

    sendJsonResponse(request, response);
}

void WebServerManager::handlePostSystemRestart(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "System restart initiated";
    response["timestamp"] = millis();

    sendJsonResponse(request, response);

    // Restart after sending response
    delay(1000);
    ESP.restart();
}

// ==================== Signal API Handlers ====================

void WebServerManager::handleGetSignals(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument response = m_dataManager->getSignalsJSON();
    sendJsonResponse(request, response);
}

void WebServerManager::handlePostSignalsSend(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_irController) {
        sendErrorResponse(request, 500, "IR controller not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String signalId = requestData["signalId"] | "";
    if (signalId.isEmpty()) {
        sendErrorResponse(request, 400, "Signal ID is required");
        return;
    }

    bool success = m_irController->sendSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Signal sent successfully" : "Failed to send signal";
    response["signalId"] = signalId;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);

    // Notify WebSocket clients
    if (success && m_wsManager && m_dataManager) {
        SignalData* signal = m_dataManager->getSignal(signalId);
        if (signal) {
            m_wsManager->notifySignalSent(signalId, signal->name);
        }
    }
}

void WebServerManager::handlePostSignalsLearn(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_irController) {
        sendErrorResponse(request, 500, "IR controller not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String signalName = requestData["signalName"] | "";
    int timeout = requestData["timeout"] | 30000;

    if (signalName.isEmpty()) {
        sendErrorResponse(request, 400, "Signal name is required");
        return;
    }

    bool success = m_irController->startLearning(signalName, timeout);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Learning started" : "Failed to start learning";
    response["signalName"] = signalName;
    response["timeout"] = timeout;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);

    // Notify WebSocket clients
    if (success && m_wsManager) {
        m_wsManager->notifyLearningStarted(signalName);
    }
}

void WebServerManager::handlePostSignalsLearnStop(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_irController) {
        sendErrorResponse(request, 500, "IR controller not available");
        return;
    }

    bool success = m_irController->stopLearning();

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Learning stopped" : "No learning session active";
    response["timestamp"] = millis();

    sendJsonResponse(request, response);
}

void WebServerManager::handleGetSignalsLearnStatus(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_irController) {
        sendErrorResponse(request, 500, "IR controller not available");
        return;
    }

    DynamicJsonDocument response = m_irController->getLearningStatus();
    sendJsonResponse(request, response);
}

void WebServerManager::handlePostSignalsBatch(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_taskManager) {
        sendErrorResponse(request, 500, "Task manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String signalIds = requestData["signalIds"] | "";
    int delayMs = requestData["delay"] | 1000;

    if (signalIds.isEmpty()) {
        sendErrorResponse(request, 400, "Signal IDs are required");
        return;
    }

    bool success = m_taskManager->executeBatch(signalIds, delayMs);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Batch execution started" : "Failed to start batch execution";
    response["signalIds"] = signalIds;
    response["delay"] = delayMs;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostSignalsDelete(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String signalId = requestData["signalId"] | "";
    if (signalId.isEmpty()) {
        sendErrorResponse(request, 400, "Signal ID is required");
        return;
    }

    bool success = m_dataManager->deleteSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Signal deleted successfully" : "Failed to delete signal";
    response["signalId"] = signalId;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 404);
}

// ==================== Task API Handlers ====================

void WebServerManager::handleGetTasks(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument response = m_dataManager->getTasksJSON();
    sendJsonResponse(request, response);
}

void WebServerManager::handlePostTasks(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData) || !validateTaskData(requestData)) {
        return;
    }

    TaskData newTask;
    newTask.name = requestData["name"] | "";
    newTask.type = requestData["type"] | "";
    newTask.status = "pending";

    // 设置任务参数
    DynamicJsonDocument params(512);
    params["signalIds"] = requestData["signalIds"] | "";
    params["config"] = requestData["config"] | "";
    newTask.setParameters(params);

    bool success = m_dataManager->addTask(newTask);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Task created successfully" : "Failed to create task";
    if (success) {
        response["taskId"] = newTask.id;
    }
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 201 : 500);
}

void WebServerManager::handlePostTasksControl(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_taskManager) {
        sendErrorResponse(request, 500, "Task manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String taskId = requestData["taskId"] | "";
    String action = requestData["action"] | "";

    if (taskId.isEmpty() || action.isEmpty()) {
        sendErrorResponse(request, 400, "Task ID and action are required");
        return;
    }

    bool success = false;
    String message = "";

    if (action == "start") {
        success = m_taskManager->startTask(taskId);
        message = success ? "Task started" : "Failed to start task";
    } else if (action == "stop") {
        success = m_taskManager->stopTask(taskId);
        message = success ? "Task stopped" : "Failed to stop task";
    } else if (action == "pause") {
        success = m_taskManager->pauseTask(taskId);
        message = success ? "Task paused" : "Failed to pause task";
    } else if (action == "resume") {
        success = m_taskManager->resumeTask(taskId);
        message = success ? "Task resumed" : "Failed to resume task";
    } else if (action == "cancel") {
        success = m_taskManager->cancelTask(taskId);
        message = success ? "Task cancelled" : "Failed to cancel task";
    } else {
        sendErrorResponse(request, 400, "Invalid action");
        return;
    }

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = message;
    response["taskId"] = taskId;
    response["action"] = action;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);

    // Notify WebSocket clients
    if (success && m_wsManager && m_dataManager) {
        TaskData* task = m_dataManager->getTask(taskId);
        if (task) {
            if (action == "start") {
                m_wsManager->notifyTaskStarted(taskId, task->name);
            } else if (action == "stop" || action == "cancel") {
                m_wsManager->notifyTaskCompleted(taskId, task->name);
            }
        }
    }
}

void WebServerManager::handleGetTasksStatus(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_taskManager) {
        sendErrorResponse(request, 500, "Task manager not available");
        return;
    }

    String taskId = getRequestParam(request, "taskId");

    DynamicJsonDocument response;
    if (taskId.isEmpty()) {
        response = m_taskManager->getAllTasksStatus();
    } else {
        response = m_taskManager->getTaskStatus(taskId);
    }

    sendJsonResponse(request, response);
}

void WebServerManager::handlePostTasksDelete(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String taskId = requestData["taskId"] | "";
    if (taskId.isEmpty()) {
        sendErrorResponse(request, 400, "Task ID is required");
        return;
    }

    bool success = m_dataManager->deleteTask(taskId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Task deleted successfully" : "Failed to delete task";
    response["taskId"] = taskId;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 404);
}

// ==================== Helper Methods ====================

void WebServerManager::sendJsonResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& doc, int code) {
    String response;
    serializeJson(doc, response);

    AsyncWebServerResponse* asyncResponse = request->beginResponse(code, "application/json", response);
    addCORSHeaders(asyncResponse);
    request->send(asyncResponse);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest* request, const String& message) {
    DynamicJsonDocument doc(256);
    doc["success"] = true;
    doc["message"] = message;
    doc["timestamp"] = millis();

    sendJsonResponse(request, doc);
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    DynamicJsonDocument doc(256);
    doc["success"] = false;
    doc["error"] = message;
    doc["code"] = code;
    doc["timestamp"] = millis();

    sendJsonResponse(request, doc, code);
}

void WebServerManager::sendNotFoundResponse(AsyncWebServerRequest* request) {
    sendErrorResponse(request, 404, "Endpoint not found");
}

DynamicJsonDocument WebServerManager::parseRequestBody(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(2048);

    if (request->hasHeader("body")) {
        String body = request->getHeader("body")->value();
        DeserializationError error = deserializeJson(doc, body);
        if (error) {
            Serial.printf("❌ JSON parsing error: %s\n", error.c_str());
        }
    }

    return doc;
}

String WebServerManager::getRequestParam(AsyncWebServerRequest* request, const String& name, const String& defaultValue) {
    if (request->hasParam(name)) {
        return request->getParam(name)->value();
    }
    return defaultValue;
}

bool WebServerManager::hasRequestParam(AsyncWebServerRequest* request, const String& name) {
    return request->hasParam(name);
}

bool WebServerManager::validateJsonRequest(AsyncWebServerRequest* request, const DynamicJsonDocument& doc) {
    if (doc.isNull()) {
        sendErrorResponse(request, 400, "Invalid JSON data");
        return false;
    }
    return true;
}

bool WebServerManager::validateSignalData(const DynamicJsonDocument& data) {
    return data.containsKey("name") && !data["name"].as<String>().isEmpty();
}

bool WebServerManager::validateTaskData(const DynamicJsonDocument& data) {
    return data.containsKey("name") && !data["name"].as<String>().isEmpty() &&
           data.containsKey("type") && !data["type"].as<String>().isEmpty();
}

bool WebServerManager::validateTimerData(const DynamicJsonDocument& data) {
    return data.containsKey("name") && !data["name"].as<String>().isEmpty() &&
           data.containsKey("signalId") && !data["signalId"].as<String>().isEmpty();
}

void WebServerManager::handleCORSPreflight(AsyncWebServerRequest* request) {
    AsyncWebServerResponse* response = request->beginResponse(200);
    addCORSHeaders(response);
    request->send(response);
}

void WebServerManager::addCORSHeaders(AsyncWebServerResponse* response) {
    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
}

void WebServerManager::onRequestBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    // Store request body for later parsing
    String* body = nullptr;

    if (index == 0) {
        // First chunk - create new body string
        body = new String();
        body->reserve(total);
        request->_tempObject = body;
    } else {
        // Subsequent chunks - get existing body string
        body = (String*)request->_tempObject;
    }

    if (body) {
        // Append data to body
        for (size_t i = 0; i < len; i++) {
            *body += (char)data[i];
        }

        // If this is the last chunk, store in header for later retrieval
        if (index + len == total) {
            request->addInterestingHeader("body", *body);
            delete body;
            request->_tempObject = nullptr;
        }
    }
}

// ==================== Placeholder API Handlers ====================
// These would be fully implemented based on the original system's requirements

void WebServerManager::handleGetTimers(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_dataManager) {
        sendJsonResponse(request, m_dataManager->getTimersJSON());
    } else {
        sendErrorResponse(request, 500, "Data manager not available");
    }
}

void WebServerManager::handlePostTimers(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData) || !validateTimerData(requestData)) {
        return;
    }

    TimerData newTimer;
    newTimer.name = requestData["name"] | "";
    newTimer.signalId = requestData["signalId"] | "";
    newTimer.schedule = requestData["schedule"] | "";
    newTimer.enabled = requestData["enabled"] | true;
    newTimer.type = requestData["type"] | "once";

    bool success = m_dataManager->addTimer(newTimer);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Timer created successfully" : "Failed to create timer";
    if (success) {
        response["timerId"] = newTimer.id;
    }
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 201 : 500);
}

void WebServerManager::handlePostTimersToggle(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String timerId = requestData["timerId"] | "";
    if (timerId.isEmpty()) {
        sendErrorResponse(request, 400, "Timer ID is required");
        return;
    }

    TimerData* timer = m_dataManager->getTimer(timerId);
    if (!timer) {
        sendErrorResponse(request, 404, "Timer not found");
        return;
    }

    timer->enabled = !timer->enabled;
    bool success = m_dataManager->updateTimer(timerId, *timer);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Timer toggled successfully" : "Failed to toggle timer";
    response["timerId"] = timerId;
    response["enabled"] = timer->enabled;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostTimersUpdate(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String timerId = requestData["timerId"] | "";
    if (timerId.isEmpty()) {
        sendErrorResponse(request, 400, "Timer ID is required");
        return;
    }

    TimerData* timer = m_dataManager->getTimer(timerId);
    if (!timer) {
        sendErrorResponse(request, 404, "Timer not found");
        return;
    }

    // Update timer fields
    if (requestData.containsKey("name")) {
        timer->name = requestData["name"] | timer->name;
    }
    if (requestData.containsKey("signalId")) {
        timer->signalId = requestData["signalId"] | timer->signalId;
    }
    if (requestData.containsKey("schedule")) {
        timer->schedule = requestData["schedule"] | timer->schedule;
    }
    if (requestData.containsKey("isActive")) {
        timer->isActive = requestData["isActive"] | timer->isActive;
    }

    bool success = m_dataManager->updateTimer(timerId, *timer);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Timer updated successfully" : "Failed to update timer";
    response["timerId"] = timerId;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostTimersDelete(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String timerId = requestData["timerId"] | "";
    if (timerId.isEmpty()) {
        sendErrorResponse(request, 400, "Timer ID is required");
        return;
    }

    bool success = m_dataManager->deleteTimer(timerId);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Timer deleted successfully" : "Failed to delete timer";
    response["timerId"] = timerId;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 404);
}

void WebServerManager::handleGetDataExport(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_dataManager) {
        String type = getRequestParam(request, "type", "all");
        sendJsonResponse(request, m_dataManager->exportData(type));
    } else {
        sendErrorResponse(request, 500, "Data manager not available");
    }
}

void WebServerManager::handlePostDataImport(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String dataType = requestData["type"] | "all";
    bool success = m_dataManager->importData(requestData, dataType);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Data imported successfully" : "Failed to import data";
    response["type"] = dataType;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostDataBackup(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_dataManager) {
        bool success = m_dataManager->backupData();
        if (success) {
            sendSuccessResponse(request, "Backup created successfully");
        } else {
            sendErrorResponse(request, 500, "Failed to create backup");
        }
    } else {
        sendErrorResponse(request, 500, "Data manager not available");
    }
}

void WebServerManager::handlePostDataClear(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_dataManager) {
        m_dataManager->clearAllData();
        sendSuccessResponse(request, "All data cleared");
    } else {
        sendErrorResponse(request, 500, "Data manager not available");
    }
}

void WebServerManager::handleGetConfig(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_dataManager) {
        sendJsonResponse(request, m_dataManager->getSystemConfig());
    } else {
        sendErrorResponse(request, 500, "Data manager not available");
    }
}

void WebServerManager::handlePostConfig(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    bool success = m_dataManager->updateSystemConfig(requestData);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Configuration updated successfully" : "Failed to update configuration";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostConfigReset(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_dataManager) {
        sendErrorResponse(request, 500, "Data manager not available");
        return;
    }

    bool success = m_dataManager->resetSystemConfig();

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Configuration reset successfully" : "Failed to reset configuration";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);

    // Notify WebSocket clients
    if (success && m_wsManager) {
        m_wsManager->notifyConfigReset();
    }
}

void WebServerManager::handleGetWebSocketStats(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_wsManager) {
        sendJsonResponse(request, m_wsManager->getWebSocketStats());
    } else {
        sendErrorResponse(request, 500, "WebSocket manager not available");
    }
}

void WebServerManager::handleGetWebSocketClients(AsyncWebServerRequest* request) {
    m_totalRequests++;
    if (m_wsManager) {
        sendJsonResponse(request, m_wsManager->getClientList());
    } else {
        sendErrorResponse(request, 500, "WebSocket manager not available");
    }
}

void WebServerManager::handlePostWebSocketBroadcast(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_wsManager) {
        sendErrorResponse(request, 500, "WebSocket manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String messageType = requestData["type"] | "";
    String messageData = requestData["data"] | "";

    if (messageType.isEmpty()) {
        sendErrorResponse(request, 400, "Message type is required");
        return;
    }

    bool success = m_wsManager->broadcastMessage(messageType, messageData);

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = success ? "Message broadcasted successfully" : "Failed to broadcast message";
    response["type"] = messageType;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

void WebServerManager::handlePostBatch(AsyncWebServerRequest* request) {
    m_totalRequests++;
    m_lastRequestTime = millis();

    if (!m_taskManager) {
        sendErrorResponse(request, 500, "Task manager not available");
        return;
    }

    DynamicJsonDocument requestData = parseRequestBody(request);
    if (!validateJsonRequest(request, requestData)) {
        return;
    }

    String operation = requestData["operation"] | "";
    String signalIds = requestData["signalIds"] | "";
    int delayMs = requestData["delay"] | 1000;

    if (operation.isEmpty()) {
        sendErrorResponse(request, 400, "Operation type is required");
        return;
    }

    bool success = false;
    String message = "";

    if (operation == "send" && !signalIds.isEmpty()) {
        success = m_taskManager->executeBatch(signalIds, delayMs);
        message = success ? "Batch send operation started" : "Failed to start batch send";
    } else {
        sendErrorResponse(request, 400, "Invalid operation or missing parameters");
        return;
    }

    DynamicJsonDocument response(256);
    response["success"] = success;
    response["message"] = message;
    response["operation"] = operation;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, success ? 200 : 500);
}

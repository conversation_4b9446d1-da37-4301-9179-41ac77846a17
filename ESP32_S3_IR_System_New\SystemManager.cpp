#include "SystemManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "WebServerManager.h"
#include "TaskManager.h"
#include "WSManager.h"

SystemManager::SystemManager()
    : m_initialized(false)
    , m_startTime(millis())
    , m_systemMode(psramFound() ? SystemMode::HIGH_PERFORMANCE : SystemMode::STANDARD)
    , m_systemCapacity(SystemCapacity::getCapacity(m_systemMode))
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_webServerManager(nullptr)
    , m_taskManager(nullptr)
    , m_wsManager(nullptr)
    , m_server(nullptr)
    , m_webSocket(nullptr)
    , m_wifiInitialized(false)
    , m_apMode(false)
    , m_lastConnectionAttempt(0)
    , m_connectionAttempts(0)
{
    // Constructor does NO memory allocation
    Serial.printf("SystemManager created in %s mode (no memory allocated)\n", getSystemModeString());
    Serial.printf("System capacity: %d signals, %d tasks, %d timers\n",
                 m_systemCapacity.maxSignals, m_systemCapacity.maxTasks, m_systemCapacity.maxTimers);
}

SystemManager::~SystemManager() {
    cleanup();
}

bool SystemManager::initialize() {
    Serial.println("🔧 Initializing SystemManager...");
    
    if (m_initialized) {
        Serial.println("SystemManager already initialized");
        return true;
    }
    
    // Step 1: Create core server components
    Serial.println("📡 Creating server components...");
    m_server = new AsyncWebServer(80);
    if (!m_server) {
        Serial.println("❌ Failed to create AsyncWebServer");
        return false;
    }
    
    m_webSocket = new AsyncWebSocket("/ws");
    if (!m_webSocket) {
        Serial.println("❌ Failed to create AsyncWebSocket");
        cleanup();
        return false;
    }
    
    // Step 2: Create component managers
    if (!initializeComponents()) {
        Serial.println("❌ Component initialization failed");
        cleanup();
        return false;
    }
    
    // Step 3: Initialize network
    if (!initializeNetwork()) {
        Serial.println("❌ Network initialization failed");
        cleanup();
        return false;
    }
    
    // Step 4: Initialize web server
    if (!initializeWebServer()) {
        Serial.println("❌ Web server initialization failed");
        cleanup();
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ SystemManager initialized successfully");
    
    printMemoryStatus();
    return true;
}

bool SystemManager::initializeComponents() {
    Serial.println("📋 Creating component managers...");
    
    // Create DataManager
    m_dataManager = new DataManager();
    if (!m_dataManager || !m_dataManager->initialize()) {
        Serial.println("❌ DataManager initialization failed");
        return false;
    }
    Serial.println("✅ DataManager created and initialized");
    
    // Create IRController
    m_irController = new IRController();
    if (!m_irController) {
        Serial.println("❌ IRController creation failed");
        return false;
    }

    // Set dependencies for IRController
    m_irController->setDataManager(m_dataManager);

    if (!m_irController->initialize()) {
        Serial.println("❌ IRController initialization failed");
        return false;
    }
    Serial.println("✅ IRController created and initialized");
    
    // Create TaskManager
    m_taskManager = new TaskManager();
    if (!m_taskManager) {
        Serial.println("❌ TaskManager creation failed");
        return false;
    }

    // Set dependencies for TaskManager
    m_taskManager->setDataManager(m_dataManager);
    m_taskManager->setIRController(m_irController);

    if (!m_taskManager->initialize()) {
        Serial.println("❌ TaskManager initialization failed");
        return false;
    }
    Serial.println("✅ TaskManager created and initialized");
    
    // Create WSManager
    m_wsManager = new WSManager();
    if (!m_wsManager || !m_wsManager->initialize(m_webSocket)) {
        Serial.println("❌ WSManager initialization failed");
        return false;
    }
    Serial.println("✅ WSManager created and initialized");
    
    // Create WebServerManager (depends on other managers)
    m_webServerManager = new WebServerManager();
    if (!m_webServerManager || !m_webServerManager->initialize(m_server, m_dataManager, m_irController, m_taskManager, m_wsManager)) {
        Serial.println("❌ WebServerManager initialization failed");
        return false;
    }
    Serial.println("✅ WebServerManager created and initialized");
    
    return true;
}

bool SystemManager::initializeNetwork() {
    Serial.println("📶 Initializing network...");
    
    initializeWiFi();
    
    Serial.println("✅ Network initialization completed");
    return true;
}

bool SystemManager::initializeWebServer() {
    Serial.println("🌐 Initializing web server...");
    
    if (!m_server || !m_webServerManager) {
        Serial.println("❌ Server components not available");
        return false;
    }
    
    // Start web server
    m_server->begin();
    Serial.println("✅ Web server started on port 80");
    
    return true;
}

void SystemManager::initializeWiFi() {
    Serial.println("📶 Initializing WiFi...");
    
    // Set WiFi mode
    WiFi.mode(WIFI_AP_STA);
    
    // Set hostname
    WiFi.setHostname("ESP32-S3-IR-Controller");
    
    m_wifiInitialized = true;
    
    // Try to connect to saved credentials
    attemptStationConnection();
    
    Serial.println("✅ WiFi initialized");
}

void SystemManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Handle WiFi management
    handleWiFiLoop();
    
    // Handle all component loops
    if (m_irController) {
        m_irController->handleLoop();
    }
    
    if (m_taskManager) {
        m_taskManager->handleLoop();
    }
    
    if (m_wsManager) {
        m_wsManager->handleLoop();
    }
    
    if (m_webServerManager) {
        m_webServerManager->handleLoop();
    }
}

void SystemManager::handleWiFiLoop() {
    if (!m_wifiInitialized) {
        return;
    }
    
    // Check if we need to start AP mode
    if (!m_apMode && WiFi.status() != WL_CONNECTED) {
        if (millis() - m_lastConnectionAttempt > CONNECTION_TIMEOUT) {
            if (m_connectionAttempts >= MAX_CONNECTION_ATTEMPTS) {
                Serial.println("📡 Max connection attempts reached, starting AP mode");
                startAccessPoint();
            } else {
                attemptStationConnection();
            }
        }
    }
}

void SystemManager::attemptStationConnection() {
    m_connectionAttempts++;
    m_lastConnectionAttempt = millis();

    Serial.printf("🔄 WiFi connection attempt %d/%d to %s\n", m_connectionAttempts, MAX_CONNECTION_ATTEMPTS, WIFI_SSID);

    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    // Wait a bit to see if connection succeeds
    delay(100);
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("✅ WiFi connected successfully!");
        Serial.printf("🌐 IP Address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("📶 RSSI: %d dBm\n", WiFi.RSSI());
        m_connectionAttempts = 0;
    }
}

void SystemManager::startAccessPoint() {
    if (m_apMode) {
        return;
    }
    
    Serial.println("📡 Starting Access Point mode...");
    
    // Configure AP
    String apName = "ESP32-S3-IR-" + String(ESP.getChipId(), HEX);
    String apPassword = "12345678";
    
    bool success = WiFi.softAP(apName.c_str(), apPassword.c_str());
    
    if (success) {
        m_apMode = true;
        IPAddress apIP = WiFi.softAPIP();
        
        Serial.println("✅ Access Point started successfully");
        Serial.printf("📡 AP Name: %s\n", apName.c_str());
        Serial.printf("🔐 AP Password: %s\n", apPassword.c_str());
        Serial.printf("🌐 AP IP Address: %s\n", apIP.toString().c_str());
        Serial.println("👉 Connect to this AP and visit http://***********");
    } else {
        Serial.println("❌ Failed to start Access Point");
    }
}

DataManager* SystemManager::getDataManager() {
    return m_dataManager;
}

IRController* SystemManager::getIRController() {
    return m_irController;
}

WebServerManager* SystemManager::getWebServerManager() {
    return m_webServerManager;
}

TaskManager* SystemManager::getTaskManager() {
    return m_taskManager;
}

WSManager* SystemManager::getWSManager() {
    return m_wsManager;
}

unsigned long SystemManager::getUptime() const {
    return millis() - m_startTime;
}

void SystemManager::restart() {
    Serial.println("🔄 System restart requested");
    delay(1000);
    ESP.restart();
}

void SystemManager::factoryReset() {
    Serial.println("🏭 Factory reset requested");
    
    if (m_dataManager) {
        m_dataManager->clearAllData();
    }
    
    // Clear WiFi credentials
    WiFi.disconnect(true);
    
    Serial.println("Factory reset completed - restarting...");
    delay(2000);
    ESP.restart();
}

void SystemManager::printMemoryStatus() {
    Serial.println("📊 Memory Status:");
    Serial.printf("   Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    if (psramFound()) {
        Serial.printf("   Free PSRAM: %d bytes (%.2f KB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0);
    }
}

size_t SystemManager::getFreePSRAM() {
    return psramFound() ? ESP.getFreePsram() : 0;
}

size_t SystemManager::getFreeHeap() {
    return ESP.getFreeHeap();
}

void SystemManager::cleanup() {
    Serial.println("🧹 Cleaning up SystemManager...");
    
    if (m_webServerManager) {
        delete m_webServerManager;
        m_webServerManager = nullptr;
    }
    
    if (m_wsManager) {
        delete m_wsManager;
        m_wsManager = nullptr;
    }
    
    if (m_taskManager) {
        delete m_taskManager;
        m_taskManager = nullptr;
    }
    
    if (m_irController) {
        delete m_irController;
        m_irController = nullptr;
    }
    
    if (m_dataManager) {
        delete m_dataManager;
        m_dataManager = nullptr;
    }
    
    if (m_webSocket) {
        delete m_webSocket;
        m_webSocket = nullptr;
    }
    
    if (m_server) {
        delete m_server;
        m_server = nullptr;
    }
    
    m_initialized = false;
}

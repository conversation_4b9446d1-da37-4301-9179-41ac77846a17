/*
 * SystemManager.h - Central System Management
 * 
 * Singleton pattern with delayed initialization to avoid PSRAM conflicts
 * All memory allocation happens after PSRAM is confirmed available
 */

#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

// Forward declarations
class DataManager;
class IRController;
class WebServerManager;
class WiFiManager;

class SystemManager {
public:
    SystemManager();
    ~SystemManager();
    
    // Initialization
    bool initialize();
    void handleLoop();
    
    // Component access
    DataManager* getDataManager();
    IRController* getIRController();
    WebServerManager* getWebServerManager();
    WiFiManager* getWiFiManager();
    
    // System status
    bool isInitialized() const { return m_initialized; }
    unsigned long getUptime() const;
    
    // System control
    void restart();
    void factoryReset();
    
    // Memory management
    void printMemoryStatus();
    size_t getFreePSRAM();
    size_t getFreeHeap();

private:
    bool m_initialized;
    unsigned long m_startTime;
    
    // Component managers - created after PSRAM is available
    DataManager* m_dataManager;
    IRController* m_irController;
    WebServerManager* m_webServerManager;
    WiFiManager* m_wifiManager;
    
    // Core server components - created after PSRAM is available
    AsyncWebServer* m_server;
    AsyncWebSocket* m_webSocket;
    
    // Initialization helpers
    bool initializeComponents();
    bool initializeNetwork();
    bool initializeWebServer();
    
    // Cleanup
    void cleanup();
};

#endif // SYSTEM_MANAGER_H

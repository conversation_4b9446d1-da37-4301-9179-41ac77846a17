#include "MemoryAllocator.h"

// ==================== 静态成员初始化 ====================
size_t MemoryAllocator::s_totalAllocations = 0;
size_t MemoryAllocator::s_totalDeallocations = 0;
size_t MemoryAllocator::s_currentAllocatedSize = 0;
size_t MemoryAllocator::s_peakMemoryUsage = 0;
size_t MemoryAllocator::s_psramUsage = 0;
size_t MemoryAllocator::s_heapUsage = 0;

MemoryAllocator::MemoryPool* MemoryAllocator::s_memoryPools = nullptr;
size_t MemoryAllocator::s_poolCount = 0;

#ifdef DEBUG_MODE
bool MemoryAllocator::s_debugMode = false;
std::vector<MemoryAllocator::AllocationInfo> MemoryAllocator::s_allocations;
#endif

// ==================== 核心内存分配接口 ====================
void* MemoryAllocator::smartAlloc(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    bool isPSRAM = false;
    
    // 策略选择
    if (shouldUsePSRAM(size)) {
        // 尝试使用PSRAM
        ptr = ps_malloc(size);
        if (ptr) {
            isPSRAM = true;
            s_psramUsage += size;
        }
    }
    
    // 如果PSRAM分配失败或不适用，使用普通RAM
    if (!ptr) {
        ptr = malloc(size);
        if (ptr) {
            s_heapUsage += size;
        }
    }
    
    // 更新统计信息
    if (ptr) {
        s_totalAllocations++;
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        // 调试模式下记录分配信息
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, isPSRAM);
        }
        #endif
    }
    
    return ptr;
}

void MemoryAllocator::smartFree(void* ptr) {
    if (!ptr) {
        return;
    }
    
    // 查找分配信息以更新统计
    #ifdef DEBUG_MODE
    if (s_debugMode) {
        AllocationInfo* info = findAllocationInfo(ptr);
        if (info) {
            if (info->isPSRAM) {
                s_psramUsage -= info->size;
            } else {
                s_heapUsage -= info->size;
            }
            s_currentAllocatedSize -= info->size;
            trackDeallocation(ptr);
        }
    }
    #endif
    
    s_totalDeallocations++;
    
    // 释放内存（free函数可以处理PSRAM和普通RAM）
    free(ptr);
}

void* MemoryAllocator::smartRealloc(void* ptr, size_t newSize) {
    if (!ptr) {
        return smartAlloc(newSize);
    }
    
    if (newSize == 0) {
        smartFree(ptr);
        return nullptr;
    }
    
    // 简单实现：分配新内存，复制数据，释放旧内存
    void* newPtr = smartAlloc(newSize);
    if (newPtr && ptr) {
        // 这里需要知道原始大小，简化处理
        // 在实际实现中，应该维护一个分配大小的映射表
        memcpy(newPtr, ptr, newSize);  // 假设newSize不超过原始大小
        smartFree(ptr);
    }
    
    return newPtr;
}

void* MemoryAllocator::smartCalloc(size_t count, size_t size) {
    size_t totalSize = count * size;
    void* ptr = smartAlloc(totalSize);
    if (ptr) {
        memset(ptr, 0, totalSize);
    }
    return ptr;
}

// ==================== 专用分配器 ====================
void* MemoryAllocator::allocFromPSRAM(size_t size) {
    if (!PSRAMManager::isPSRAMAvailable()) {
        return nullptr;
    }
    
    void* ptr = ps_malloc(size);
    if (ptr) {
        s_totalAllocations++;
        s_currentAllocatedSize += size;
        s_psramUsage += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, true);
        }
        #endif
    }
    
    return ptr;
}

void* MemoryAllocator::allocFromHeap(size_t size) {
    void* ptr = malloc(size);
    if (ptr) {
        s_totalAllocations++;
        s_currentAllocatedSize += size;
        s_heapUsage += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, false);
        }
        #endif
    }
    
    return ptr;
}

void* MemoryAllocator::allocDMAMemory(size_t size) {
    // DMA内存必须使用普通RAM
    return allocFromHeap(size);
}

// ==================== 内存健康检查 ====================
bool MemoryAllocator::checkMemoryLeaks() {
    // 简单的内存泄漏检查：分配次数应该等于释放次数
    return s_totalAllocations == s_totalDeallocations;
}

float MemoryAllocator::getFragmentationLevel() {
    // 获取PSRAM和堆内存的碎片化程度
    float psramFrag = PSRAMManager::getMemoryFragmentation();
    
    // 简单的堆内存碎片化检测
    size_t freeHeap = ESP.getFreeHeap();
    size_t largestBlock = ESP.getMaxAllocHeap();
    float heapFrag = freeHeap > 0 ? 1.0f - (float)largestBlock / freeHeap : 0.0f;
    
    // 返回较高的碎片化程度
    return max(psramFrag, heapFrag);
}

bool MemoryAllocator::checkMemoryIntegrity() {
    // 执行基本的内存完整性检查
    
    // 1. 检查统计数据的一致性
    if (s_totalAllocations < s_totalDeallocations) {
        return false;
    }
    
    // 2. 检查当前分配大小的合理性
    if (s_currentAllocatedSize > s_peakMemoryUsage) {
        return false;
    }
    
    // 3. 检查PSRAM状态（如果可用）
    if (PSRAMManager::isPSRAMAvailable()) {
        if (!PSRAMManager::checkMemoryHealth()) {
            return false;
        }
    }
    
    return true;
}

// ==================== 内存清理 ====================
void MemoryAllocator::performCleanup() {
    Serial.println("🧹 Performing memory cleanup...");
    
    // 清理内存池
    cleanupMemoryPools();
    
    // 执行PSRAM清理
    PSRAMManager::performMemoryCleanup();
    
    // 重置统计信息中的峰值（可选）
    // s_peakMemoryUsage = s_currentAllocatedSize;
    
    Serial.println("✅ Memory cleanup completed");
}

void MemoryAllocator::forceGarbageCollection() {
    // ESP32没有内置垃圾回收，这里主要是占位符
    performCleanup();
}

void MemoryAllocator::defragmentMemory() {
    // ESP32没有内存碎片整理功能，这里主要是占位符
    Serial.println("⚠️  Memory defragmentation not supported on ESP32");
}

// ==================== 内存池管理 ====================
bool MemoryAllocator::initializeMemoryPools() {
    // 简化的内存池实现
    // 在实际应用中，可以根据需要创建不同大小的内存池
    Serial.println("🏊 Initializing memory pools...");
    
    // 这里可以添加内存池初始化逻辑
    s_poolCount = 0;
    s_memoryPools = nullptr;
    
    Serial.println("✅ Memory pools initialized");
    return true;
}

void* MemoryAllocator::allocFromPool(size_t size) {
    // 简化实现：直接使用smartAlloc
    return smartAlloc(size);
}

void MemoryAllocator::freeToPool(void* ptr, size_t size) {
    // 简化实现：直接使用smartFree
    smartFree(ptr);
}

void MemoryAllocator::cleanupMemoryPools() {
    if (s_memoryPools) {
        free(s_memoryPools);
        s_memoryPools = nullptr;
        s_poolCount = 0;
    }
}

// ==================== 调试功能 ====================
void MemoryAllocator::printMemoryStats() {
    Serial.println("📊 Memory Allocator Statistics:");
    Serial.printf("   Total Allocations: %d\n", s_totalAllocations);
    Serial.printf("   Total Deallocations: %d\n", s_totalDeallocations);
    Serial.printf("   Current Allocated: %d bytes (%.2f KB)\n", 
                 s_currentAllocatedSize, s_currentAllocatedSize / 1024.0);
    Serial.printf("   Peak Usage: %d bytes (%.2f KB)\n", 
                 s_peakMemoryUsage, s_peakMemoryUsage / 1024.0);
    Serial.printf("   PSRAM Usage: %d bytes (%.2f KB)\n", 
                 s_psramUsage, s_psramUsage / 1024.0);
    Serial.printf("   Heap Usage: %d bytes (%.2f KB)\n", 
                 s_heapUsage, s_heapUsage / 1024.0);
    Serial.printf("   Memory Leaks: %s\n", checkMemoryLeaks() ? "None" : "Detected");
    Serial.printf("   Fragmentation: %.1f%%\n", getFragmentationLevel() * 100);
}

void MemoryAllocator::printAllocationDetails() {
    Serial.printf("📋 Memory Allocation Details:\n");
    Serial.printf("   Available Memory: %d bytes\n", getAvailableMemory());
    Serial.printf("   Free Heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("   Largest Heap Block: %d bytes\n", ESP.getMaxAllocHeap());
    
    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("   Free PSRAM: %d bytes\n", PSRAMManager::getFreePSRAM());
    }
}

String MemoryAllocator::generateMemoryReport() {
    String report = "Memory Allocator Report\n";
    report += "======================\n";
    report += "Total Allocations: " + String(s_totalAllocations) + "\n";
    report += "Total Deallocations: " + String(s_totalDeallocations) + "\n";
    report += "Current Allocated: " + String(s_currentAllocatedSize) + " bytes\n";
    report += "Peak Usage: " + String(s_peakMemoryUsage) + " bytes\n";
    report += "PSRAM Usage: " + String(s_psramUsage) + " bytes\n";
    report += "Heap Usage: " + String(s_heapUsage) + " bytes\n";
    report += "Memory Leaks: " + String(checkMemoryLeaks() ? "None" : "Detected") + "\n";
    report += "Fragmentation: " + String(getFragmentationLevel() * 100, 1) + "%\n";
    
    return report;
}

// ==================== 私有方法 ====================
int MemoryAllocator::getBestAllocationStrategy(size_t size) {
    // 0 = Heap, 1 = PSRAM, 2 = Pool
    
    if (size < SMALL_ALLOCATION_THRESHOLD) {
        return 0;  // 小内存使用堆
    }
    
    if (shouldUsePSRAM(size)) {
        return 1;  // 大内存使用PSRAM
    }
    
    return 0;  // 默认使用堆
}

bool MemoryAllocator::isMemoryUnderPressure() {
    // 检查内存压力
    size_t freeHeap = ESP.getFreeHeap();
    size_t freePSRAM = PSRAMManager::getFreePSRAM();
    
    return (freeHeap < MIN_FREE_HEAP) || 
           (PSRAMManager::isPSRAMAvailable() && freePSRAM < MIN_FREE_PSRAM);
}

#ifdef DEBUG_MODE
void MemoryAllocator::trackAllocation(void* ptr, size_t size, bool isPSRAM) {
    if (!s_debugMode) return;
    
    AllocationInfo info;
    info.ptr = ptr;
    info.size = size;
    info.isPSRAM = isPSRAM;
    info.timestamp = millis();
    info.file = nullptr;  // 可以通过宏来设置
    info.line = 0;
    
    s_allocations.push_back(info);
}

void MemoryAllocator::trackDeallocation(void* ptr) {
    if (!s_debugMode) return;
    
    for (auto it = s_allocations.begin(); it != s_allocations.end(); ++it) {
        if (it->ptr == ptr) {
            s_allocations.erase(it);
            break;
        }
    }
}

MemoryAllocator::AllocationInfo* MemoryAllocator::findAllocationInfo(void* ptr) {
    if (!s_debugMode) return nullptr;
    
    for (auto& info : s_allocations) {
        if (info.ptr == ptr) {
            return &info;
        }
    }
    return nullptr;
}

void MemoryAllocator::enableDebugMode() {
    s_debugMode = true;
    Serial.println("🐛 Memory allocator debug mode enabled");
}

void MemoryAllocator::disableDebugMode() {
    s_debugMode = false;
    s_allocations.clear();
    Serial.println("🐛 Memory allocator debug mode disabled");
}

void MemoryAllocator::printAllocationTrace() {
    if (!s_debugMode) {
        Serial.println("⚠️  Debug mode not enabled");
        return;
    }
    
    Serial.printf("📋 Active Allocations (%d):\n", s_allocations.size());
    for (const auto& info : s_allocations) {
        Serial.printf("   %p: %d bytes (%s) at %lu ms\n", 
                     info.ptr, info.size, 
                     info.isPSRAM ? "PSRAM" : "Heap", 
                     info.timestamp);
    }
}
#endif

/**
 * Web服务器管理器 - 头文件
 * 负责HTTP API路由、请求处理、响应格式化
 */

#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "SignalManager.h"
#include "IRController.h"

class WebServerManager {
private:
    AsyncWebServer* server;
    SignalManager* signalManager;
    IRController* irController;
    
    // CORS配置
    static const char* CORS_HEADERS;
    static const char* CORS_METHODS;
    static const char* CORS_ORIGIN;
    
    // 请求处理方法
    void handleCORS(AsyncWebServerRequest* request);
    void handleNotFound(AsyncWebServerRequest* request);
    void handleFileRequest(AsyncWebServerRequest* request);
    
    // 信号管理API处理器
    void handleGetSignals(AsyncWebServerRequest* request);
    void handleDeleteSignal(AsyncWebServerRequest* request);
    void handleBatchDeleteSignals(AsyncWebServerRequest* request);
    void handleUpdateSignal(AsyncWebServerRequest* request);
    void handleSendSignal(AsyncWebServerRequest* request);
    
    // 学习功能API处理器
    void handleStartLearning(AsyncWebServerRequest* request);
    void handleStopLearning(AsyncWebServerRequest* request);
    void handleSaveLearning(AsyncWebServerRequest* request);
    
    // 导入功能API处理器
    void handleImportSignals(AsyncWebServerRequest* request);
    void handleImportText(AsyncWebServerRequest* request);
    void handleExecuteImport(AsyncWebServerRequest* request);
    void handleExecuteTextImport(AsyncWebServerRequest* request);
    
    // 系统管理API处理器
    void handleGetSystemLogs(AsyncWebServerRequest* request);
    void handleSaveSystemLogs(AsyncWebServerRequest* request);
    void handleErrorLog(AsyncWebServerRequest* request);
    void handleGetSystemStats(AsyncWebServerRequest* request);
    
    // 控制管理API处理器
    void handleGetControlHistory(AsyncWebServerRequest* request);
    void handleSaveControlHistory(AsyncWebServerRequest* request);
    
    // 工具方法
    void sendJsonResponse(AsyncWebServerRequest* request, const APIResponse& response);
    void sendErrorResponse(AsyncWebServerRequest* request, const String& error, int code = 400);
    void sendSuccessResponse(AsyncWebServerRequest* request, const JsonDocument& data = JsonDocument());
    bool parseRequestBody(AsyncWebServerRequest* request, JsonDocument& doc);
    void addCORSHeaders(AsyncWebServerResponse* response);
    
    // 请求验证
    bool validateSignalId(const String& id);
    bool validateJsonRequest(AsyncWebServerRequest* request, JsonDocument& doc);
    bool validateRequiredFields(const JsonObject& obj, const std::vector<String>& fields);
    
    // 日志和错误处理
    void logRequest(AsyncWebServerRequest* request);
    void logError(const String& error, const String& context = "");
    
public:
    WebServerManager(AsyncWebServer* server, SignalManager* signalManager, IRController* irController);
    ~WebServerManager();
    
    // 初始化和配置
    bool init();
    void setupRoutes();
    void setupStaticFiles();
    void setupCORS();
    
    // 服务器控制
    void start();
    void stop();
    bool isRunning() const;
    
    // 统计信息
    unsigned long getRequestCount() const { return requestCount; }
    unsigned long getErrorCount() const { return errorCount; }
    
private:
    // 统计计数器
    unsigned long requestCount;
    unsigned long errorCount;
    
    // 配置标志
    bool initialized;
    bool debugEnabled;
};

#endif // WEB_SERVER_MANAGER_H

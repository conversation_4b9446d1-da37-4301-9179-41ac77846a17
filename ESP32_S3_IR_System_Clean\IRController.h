/*
 * IRController.h - IR Control with Zero Global Memory Allocation
 */

#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <ArduinoJson.h>
#include <functional>

class IRController {
public:
    IRController();
    ~IRController();
    
    bool initialize();
    void handleLoop();
    
    // Signal transmission
    bool sendSignal(const String& id);
    bool sendSignalByData(const String& protocol, const String& data, int frequency = 38000);
    
    // Signal learning
    bool startLearning(const String& signalName, int timeoutMs = 30000);
    void stopLearning();
    bool isLearning() const { return m_isLearning; }
    
    // Status
    DynamicJsonDocument getStatus() const;

private:
    bool m_initialized;
    bool m_isLearning;
    unsigned long m_learningStartTime;
    int m_learningTimeout;
    String m_learningSignalName;
    
    // IR components - created after initialization
    IRsend* m_irSend;
    IRrecv* m_irRecv;
    
    // Pin definitions
    static const int IR_SEND_PIN = 4;
    static const int IR_RECV_PIN = 5;
    
    void processReceivedSignal();
};

#endif // IR_CONTROLLER_H

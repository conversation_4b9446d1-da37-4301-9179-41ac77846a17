#include "WiFiManager.h"

WiFiManager::WiFiManager() 
    : m_initialized(false)
    , m_apMode(false)
    , m_lastConnectionAttempt(0)
    , m_connectionAttempts(0)
{
    Serial.println("WiFiManager created (no memory allocated)");
}

WiFiManager::~WiFiManager() {
    if (m_initialized) {
        WiFi.disconnect(true);
    }
}

bool WiFiManager::initialize() {
    Serial.println("📶 Initializing WiFiManager...");
    
    if (m_initialized) {
        Serial.println("WiFiManager already initialized");
        return true;
    }
    
    // Set WiFi mode
    WiFi.mode(WIFI_AP_STA);
    
    // Set hostname
    WiFi.setHostname("ESP32-S3-IR-Controller");
    
    m_initialized = true;
    Serial.println("✅ WiFiManager initialized successfully");
    
    return true;
}

void WiFiManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Handle connection timeout
    if (!m_apMode && WiFi.status() != WL_CONNECTED) {
        handleConnectionTimeout();
    }
}

void WiFiManager::startConnection() {
    if (!m_initialized) {
        Serial.println("❌ WiFiManager not initialized");
        return;
    }
    
    Serial.println("📶 Starting WiFi connection...");
    
    // Try to connect to saved credentials first
    WiFi.begin();
    
    m_lastConnectionAttempt = millis();
    m_connectionAttempts = 1;
    
    Serial.println("🔍 Attempting to connect to saved WiFi credentials...");
}

void WiFiManager::disconnect() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("📶 Disconnecting WiFi...");
    WiFi.disconnect();
    
    if (m_apMode) {
        stopAP();
    }
}

bool WiFiManager::isConnected() const {
    return m_initialized && WiFi.status() == WL_CONNECTED;
}

void WiFiManager::startAP() {
    if (!m_initialized) {
        Serial.println("❌ WiFiManager not initialized");
        return;
    }
    
    if (m_apMode) {
        Serial.println("AP mode already active");
        return;
    }
    
    Serial.println("📡 Starting Access Point mode...");
    
    // Configure AP
    String apName = "ESP32-S3-IR-" + String(ESP.getChipId(), HEX);
    String apPassword = "12345678"; // Default password
    
    bool success = WiFi.softAP(apName.c_str(), apPassword.c_str());
    
    if (success) {
        m_apMode = true;
        IPAddress apIP = WiFi.softAPIP();
        
        Serial.println("✅ Access Point started successfully");
        Serial.printf("📡 AP Name: %s\n", apName.c_str());
        Serial.printf("🔐 AP Password: %s\n", apPassword.c_str());
        Serial.printf("🌐 AP IP Address: %s\n", apIP.toString().c_str());
        Serial.println("👉 Connect to this AP and visit http://***********");
    } else {
        Serial.println("❌ Failed to start Access Point");
    }
}

void WiFiManager::stopAP() {
    if (!m_apMode) {
        return;
    }
    
    Serial.println("📡 Stopping Access Point...");
    WiFi.softAPdisconnect(true);
    m_apMode = false;
    
    Serial.println("✅ Access Point stopped");
}

bool WiFiManager::isAPActive() const {
    return m_apMode;
}

void WiFiManager::attemptConnection() {
    if (m_connectionAttempts >= MAX_CONNECTION_ATTEMPTS) {
        Serial.println("❌ Max connection attempts reached, starting AP mode");
        startAP();
        return;
    }
    
    Serial.printf("🔄 Connection attempt %d/%d\n", m_connectionAttempts, MAX_CONNECTION_ATTEMPTS);
    
    WiFi.begin();
    m_lastConnectionAttempt = millis();
    m_connectionAttempts++;
}

void WiFiManager::handleConnectionTimeout() {
    if (millis() - m_lastConnectionAttempt < CONNECTION_TIMEOUT) {
        return;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("✅ WiFi connected successfully!");
        Serial.printf("🌐 IP Address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("📶 RSSI: %d dBm\n", WiFi.RSSI());
        Serial.printf("🔗 Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
        Serial.printf("🌐 Subnet: %s\n", WiFi.subnetMask().toString().c_str());
        
        m_connectionAttempts = 0;
        return;
    }
    
    Serial.printf("⏰ Connection timeout (attempt %d)\n", m_connectionAttempts);
    attemptConnection();
}

DynamicJsonDocument WiFiManager::getStatus() const {
    DynamicJsonDocument doc(512);
    
    doc["initialized"] = m_initialized;
    doc["connected"] = isConnected();
    doc["ap_mode"] = m_apMode;
    doc["connection_attempts"] = m_connectionAttempts;
    
    if (isConnected()) {
        doc["station"]["ip"] = WiFi.localIP().toString();
        doc["station"]["gateway"] = WiFi.gatewayIP().toString();
        doc["station"]["subnet"] = WiFi.subnetMask().toString();
        doc["station"]["rssi"] = WiFi.RSSI();
        doc["station"]["ssid"] = WiFi.SSID();
        doc["station"]["bssid"] = WiFi.BSSIDstr();
        doc["station"]["channel"] = WiFi.channel();
    }
    
    if (m_apMode) {
        doc["ap"]["ip"] = WiFi.softAPIP().toString();
        doc["ap"]["clients"] = WiFi.softAPgetStationNum();
        doc["ap"]["ssid"] = WiFi.softAPSSID();
    }
    
    doc["mac_address"] = WiFi.macAddress();
    doc["hostname"] = WiFi.getHostname();
    doc["timestamp"] = millis();
    
    return doc;
}

String WiFiManager::getLocalIP() const {
    if (isConnected()) {
        return WiFi.localIP().toString();
    } else if (m_apMode) {
        return WiFi.softAPIP().toString();
    }
    return "0.0.0.0";
}

int WiFiManager::getRSSI() const {
    return isConnected() ? WiFi.RSSI() : 0;
}

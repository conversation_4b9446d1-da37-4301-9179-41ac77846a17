/*
 * ESP32-S3 OPI PSRAM Clean Test
 * No Chinese characters, no external dependencies, no Unicode issues
 * Hardware: ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
 * 
 * Arduino IDE Configuration:
 * - Board: ESP32S3 Dev Module
 * - Flash Mode: QIO
 * - Flash Size: 16MB (128Mb)
 * - PSRAM: OPI PSRAM
 * - Partition Scheme: 16M Flash (3MB APP/9.9MB FATFS)
 * - Flash Frequency: 80MHz
 * - Upload Speed: 460800
 */

void setup() {
    Serial.begin(115200);
    delay(3000);

    Serial.println();
    Serial.println("========================================");
    Serial.println("ESP32-S3 OPI PSRAM Clean Test");
    Serial.println("No Unicode, No Chinese, No Dependencies");
    Serial.println("========================================");

    // Check boot configuration
    Serial.println("Boot Configuration Check:");
    Serial.printf("Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);
    
    // Check Flash Mode - This is CRITICAL for PSRAM detection
    uint32_t flash_mode = ESP.getFlashChipMode();
    Serial.printf("Flash Mode: %s\n", 
        flash_mode == 0 ? "QIO" :
        flash_mode == 1 ? "QOUT" :
        flash_mode == 2 ? "DIO" :
        flash_mode == 3 ? "DOUT" : "UNKNOWN");
    
    if (flash_mode != 0) {
        Serial.println();
        Serial.println("*** CRITICAL ERROR ***");
        Serial.println("Flash Mode is NOT QIO!");
        Serial.println("This WILL prevent OPI PSRAM from working!");
        Serial.println();
        Serial.println("Required Arduino IDE Settings:");
        Serial.println("- Board: ESP32S3 Dev Module (NOT ESP32 Dev Module)");
        Serial.println("- Flash Mode: QIO (NOT DIO/QOUT/DOUT)");
        Serial.println("- PSRAM: OPI PSRAM (NOT Disabled)");
        Serial.println("- Flash Size: 16MB (128Mb)");
        Serial.println("- Partition Scheme: 16M Flash (3MB APP/9.9MB FATFS)");
        Serial.println();
        Serial.println("Please fix Arduino IDE configuration and try again!");
        Serial.println("========================================");
        return;
    }
    
    Serial.println();
    Serial.println("PSRAM Detection Test:");
    
    if (!psramFound()) {
        Serial.println();
        Serial.println("*** PSRAM NOT FOUND ***");
        Serial.println("Root cause analysis:");
        Serial.printf("- Flash Mode: %s (should be QIO)\n", 
            flash_mode == 0 ? "QIO - CORRECT" :
            flash_mode == 2 ? "DIO - WRONG!" : "OTHER - WRONG!");
        Serial.println();
        Serial.println("This confirms the Arduino IDE configuration issue.");
        Serial.println("Please check the configuration guide and try again.");
        Serial.println("========================================");
        return;
    }

    Serial.println();
    Serial.println("*** PSRAM DETECTION SUCCESSFUL! ***");
    Serial.println("PSRAM is available and working!");
    Serial.printf("Total Size: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("Free Size: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    
    // Test 1: Basic allocation
    Serial.println();
    Serial.println("Test 1: Basic Allocation (1KB)");
    void* ptr1 = ps_malloc(1024);
    if (ptr1) {
        Serial.printf("SUCCESS: 1KB allocated at address 0x%08X\n", (uint32_t)ptr1);
        free(ptr1);
        Serial.println("SUCCESS: Memory freed");
    } else {
        Serial.println("FAILED: 1KB allocation failed");
    }
    
    // Test 2: Large allocation
    Serial.println();
    Serial.println("Test 2: Large Allocation (1MB)");
    size_t largeSize = 1024 * 1024; // 1MB
    void* ptr2 = ps_malloc(largeSize);
    if (ptr2) {
        Serial.printf("SUCCESS: 1MB allocated at address 0x%08X\n", (uint32_t)ptr2);
        free(ptr2);
        Serial.println("SUCCESS: Memory freed");
    } else {
        Serial.println("FAILED: 1MB allocation failed");
    }
    
    // Test 3: Read/Write integrity
    Serial.println();
    Serial.println("Test 3: Read/Write Integrity (256KB)");
    size_t testSize = 256 * 1024; // 256KB
    uint8_t* testBuffer = (uint8_t*)ps_malloc(testSize);
    if (testBuffer) {
        Serial.printf("SUCCESS: 256KB allocated at address 0x%08X\n", (uint32_t)testBuffer);
        
        // Write test pattern
        Serial.println("Writing test pattern...");
        for (size_t i = 0; i < testSize; i++) {
            testBuffer[i] = (uint8_t)(i % 256);
        }
        Serial.println("Test pattern written");
        
        // Verify test pattern
        Serial.println("Verifying test pattern...");
        bool integrity = true;
        for (size_t i = 0; i < testSize; i++) {
            if (testBuffer[i] != (uint8_t)(i % 256)) {
                integrity = false;
                Serial.printf("FAILED: Data mismatch at index %d\n", i);
                break;
            }
        }
        
        if (integrity) {
            Serial.println("SUCCESS: Read/Write integrity test passed");
        } else {
            Serial.println("FAILED: Read/Write integrity test failed");
        }
        
        free(testBuffer);
        Serial.println("SUCCESS: Memory freed");
    } else {
        Serial.println("FAILED: 256KB allocation failed");
    }
    
    Serial.println();
    Serial.println("========================================");
    Serial.println("*** ALL PSRAM TESTS COMPLETED ***");
    Serial.println("PSRAM is working correctly!");
    Serial.println("You can now use the full IR control system.");
    Serial.println("========================================");
}

void loop() {
    // Show PSRAM status every 10 seconds
    static unsigned long lastCheck = 0;
    if (millis() - lastCheck > 10000) {
        lastCheck = millis();
        
        if (psramFound()) {
            Serial.printf("PSRAM Status: OK - Free: %d bytes (%.2f MB)\n", 
                         ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
        } else {
            Serial.println("PSRAM Status: ERROR - Not detected");
        }
    }
    
    delay(1000);
}

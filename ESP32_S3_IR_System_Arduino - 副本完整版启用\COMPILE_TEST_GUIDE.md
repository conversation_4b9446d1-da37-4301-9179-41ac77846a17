# 🔧 编译测试指南

## 📋 测试步骤

### 1. 基本PSRAM测试
**文件**: `PSRAM_Test_Simple.ino`

这是一个独立的PSRAM测试程序，用于验证PSRAM功能：

```
1. 在Arduino IDE中打开 PSRAM_Test_Simple.ino
2. 确保板子配置正确：
   - Board: ESP32S3 Dev Module
   - Flash Mode: QIO
   - PSRAM: OPI PSRAM
   - Flash Size: 16MB
3. 编译并上传
4. 打开串口监视器 (115200 baud)
5. 查看PSRAM检测结果
```

**期望输出**:
```
✅ PSRAM 检测成功!
📊 PSRAM总大小: 8388608 bytes (8.00 MB)
✅ 1KB分配成功
✅ 1MB分配成功
✅ 读写完整性测试通过
🎉 PSRAM功能正常，可以在项目中使用!
```

### 2. 主项目编译测试
**文件**: `ESP32_S3_IR_System_Arduino.ino`

这是完整的IR控制系统项目：

```
1. 在Arduino IDE中打开 ESP32_S3_IR_System_Arduino.ino
2. 确保板子配置正确（同上）
3. 编译项目（不需要上传）
4. 检查编译输出是否有错误
```

## 🔍 编译问题排查

### 问题1: 库缺失错误
如果看到类似错误：
```
fatal error: ArduinoJson.h: No such file or directory
```

**解决方案**:
项目已经配置为自动检测库是否可用。如果库缺失，会使用简化版本。

### 问题2: PSRAM相关错误
如果看到PSRAM相关编译错误：

**检查项**:
- [ ] Board: ESP32S3 Dev Module (不是ESP32 Dev Module)
- [ ] Flash Mode: QIO
- [ ] PSRAM: OPI PSRAM
- [ ] build_opt.h 文件存在且内容正确

### 问题3: 内存不足错误
如果编译时提示内存不足：

**解决方案**:
- 确保Flash Size设置为16MB
- 检查Partition Scheme设置

## 📊 编译成功标志

### 编译输出示例
```
Sketch uses 1234567 bytes (XX%) of program storage space. Maximum is 16777216 bytes.
Global variables use 123456 bytes (XX%) of dynamic memory, leaving XXXXX bytes for local variables.
```

### 上传成功标志
```
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:QIO, clock div:1  ← 必须显示QIO
```

## 🚀 下一步

1. **如果PSRAM测试成功**: 您的硬件和配置都正确
2. **如果主项目编译成功**: 所有依赖都已解决
3. **如果两者都成功**: 可以开始使用完整的IR控制系统

## 📞 故障排除

如果遇到问题：

1. **重新检查Arduino IDE配置**
2. **确认build_opt.h文件存在**
3. **尝试先运行PSRAM_Test_Simple.ino**
4. **检查串口输出中的错误信息**

## 🎯 配置检查清单

- [ ] Arduino IDE版本 2.0+
- [ ] ESP32 Board Package 2.0.11+
- [ ] Board: ESP32S3 Dev Module
- [ ] Flash Mode: QIO
- [ ] Flash Size: 16MB (128Mb)
- [ ] PSRAM: OPI PSRAM
- [ ] Upload Speed: 460800
- [ ] build_opt.h 文件存在
- [ ] 编译无错误
- [ ] PSRAM检测成功

/*
 * IR Controller Implementation - ESP32-S3 IR Control System
 * Fully compatible with IRremoteESP8266 2.8.6 API
 * Handles IR signal learning, transmission, and hardware management
 */

#include "ir_controller.h"
#include "data_manager.h"
#include <IRutils.h>

IRController::IRController() {
    irSend = nullptr;
    irRecv = nullptr;
    dataManager = nullptr;
    
    m_learningState = LearningState::IDLE;
    m_currentLearningSignalName = "";
    m_learningStartTime = 0;
    m_learningTimeoutMs = LEARNING_TIMEOUT;

    m_transmissionState = TransmissionState::IDLE;
    m_isTransmitting = false;
    
    defaultFrequency = 38000;
    
    totalSignalsSent = 0;
    totalSignalsLearned = 0;
    totalErrors = 0;
    
    // Clear callbacks
    onSignalLearned = nullptr;
    onLearningTimeout = nullptr;
    onLearningError = nullptr;
    onSignalSent = nullptr;
    onTransmissionError = nullptr;
}

IRController::~IRController() {
    cleanupHardware();
}

bool IRController::initialize() {
    Serial.println("Initializing IR Controller...");
    
    if (!initializeHardware()) {
        Serial.println("ERROR: IR hardware initialization failed");
        return false;
    }
    
    if (!testHardware()) {
        Serial.println("WARNING: IR hardware test failed, but continuing");
    }
    
    Serial.println("✅ IR Controller initialized successfully");
    Serial.printf("📡 IR Transmitter: GPIO%d\n", IR_SEND_PIN);
    Serial.printf("📡 IR Receiver: GPIO%d\n", IR_RECV_PIN);
    Serial.printf("🔧 Default Frequency: %d Hz\n", defaultFrequency);
    
    return true;
}

void IRController::handleLoop() {
    // Handle learning process
    if (m_learningState == LearningState::LEARNING) {
        // Check for timeout
        if (millis() - m_learningStartTime > m_learningTimeoutMs) {
            m_learningState = LearningState::TIMEOUT;

            Serial.printf("⏰ Learning timeout for signal: %s\n", m_currentLearningSignalName.c_str());

            if (onLearningTimeout) {
                onLearningTimeout(m_currentLearningSignalName);
            }

            m_currentLearningSignalName = "";
            return;
        }
        
        // Check for received signal
        if (processReceivedSignal()) {
            m_learningState = LearningState::COMPLETED;

            Serial.printf("✅ Signal learned successfully: %s\n", m_currentLearningSignalName.c_str());

            m_currentLearningSignalName = "";
        }
    }
    
    // Handle IR receiver
    if (irRecv && irRecv->decode(&results)) {
        // Process received signal if in learning mode
        if (m_learningState == LearningState::LEARNING) {
            processReceivedSignal();
        }
        
        // Resume receiving
        irRecv->resume();
    }
}

bool IRController::isHardwareReady() const {
    return (irSend != nullptr && irRecv != nullptr);
}

bool IRController::testHardware() {
    if (!isHardwareReady()) {
        Serial.println("ERROR: IR hardware not initialized");
        return false;
    }
    
    Serial.println("Testing IR hardware...");
    
    // Test IR transmitter by sending a test signal
    try {
        irSend->sendNEC(0x00FF00FF, 32);
        Serial.println("✅ IR transmitter test passed");
    } catch (...) {
        Serial.println("❌ IR transmitter test failed");
        return false;
    }
    
    // Test IR receiver
    if (irRecv) {
        Serial.println("✅ IR receiver initialized");
    } else {
        Serial.println("❌ IR receiver test failed");
        return false;
    }
    
    return true;
}

// ==================== Learning Functions ====================

bool IRController::startLearning(const String& signalName) {
    if (m_learningState == LearningState::LEARNING) {
        Serial.println("ERROR: Already in learning mode");
        return false;
    }

    if (signalName.isEmpty()) {
        Serial.println("ERROR: Signal name cannot be empty");
        return false;
    }

    if (!isHardwareReady()) {
        Serial.println("ERROR: IR hardware not ready");
        return false;
    }

    m_currentLearningSignalName = signalName;
    m_learningStartTime = millis();
    m_learningState = LearningState::LEARNING;
    
    // Enable IR receiver
    irRecv->enableIRIn();
    
    Serial.printf("🎯 Started learning signal: %s\n", signalName.c_str());
    Serial.printf("⏱️  Timeout: %lu ms\n", m_learningTimeoutMs);
    
    return true;
}

bool IRController::stopLearning() {
    if (m_learningState != LearningState::LEARNING) {
        Serial.println("ERROR: Not in learning mode");
        return false;
    }

    m_learningState = LearningState::IDLE;
    m_currentLearningSignalName = "";
    
    Serial.println("🛑 Learning stopped");
    return true;
}

unsigned long IRController::getLearningElapsed() const {
    if (m_learningState == LearningState::LEARNING) {
        return millis() - m_learningStartTime;
    }
    return 0;
}

unsigned long IRController::getLearningRemaining() const {
    if (m_learningState == LearningState::LEARNING) {
        unsigned long elapsed = getLearningElapsed();
        if (elapsed < m_learningTimeoutMs) {
            return m_learningTimeoutMs - elapsed;
        }
    }
    return 0;
}

// ==================== Signal Transmission ====================

bool IRController::sendSignal(const String& signalId) {
    if (!dataManager) {
        Serial.println("ERROR: Data manager not set");
        return false;
    }
    
    if (!isHardwareReady()) {
        Serial.println("ERROR: IR hardware not ready");
        return false;
    }
    
    if (m_isTransmitting) {
        Serial.println("ERROR: Already transmitting");
        return false;
    }

    SignalData* signal = dataManager->getSignal(signalId);
    if (!signal) {
        Serial.printf("ERROR: Signal %s not found\n", signalId.c_str());
        return false;
    }

    Serial.printf("📡 Sending signal: %s (%s)\n", signal->name.c_str(), signalId.c_str());

    m_transmissionState = TransmissionState::TRANSMITTING;
    m_isTransmitting = true;
    
    bool success = false;
    
    // Try to send using protocol data first
    if (!signal->data.isEmpty() && !signal->protocol.isEmpty()) {
        success = sendSignalByData(signal->protocol, signal->data, signal->rawData, signal->frequency);
    }
    // Fall back to raw data
    else if (!signal->rawData.isEmpty()) {
        success = sendRawSignal(signal->rawData, signal->frequency);
    }
    else {
        Serial.println("ERROR: No valid signal data found");
        success = false;
    }
    
    m_isTransmitting = false;

    if (success) {
        m_transmissionState = TransmissionState::COMPLETED;
        totalSignalsSent++;
        
        // Update signal use count
        signal->useCount++;
        
        Serial.printf("✅ Signal sent successfully: %s\n", signalId.c_str());
        
        if (onSignalSent) {
            onSignalSent(signalId, true);
        }
    } else {
        m_transmissionState = TransmissionState::ERROR;
        totalErrors++;
        
        Serial.printf("❌ Failed to send signal: %s\n", signalId.c_str());
        
        if (onSignalSent) {
            onSignalSent(signalId, false);
        }
        
        if (onTransmissionError) {
            onTransmissionError("Failed to transmit signal: " + signalId);
        }
    }
    
    return success;
}

bool IRController::sendSignalByData(const String& protocol, const String& data, 
                                   const String& rawData, int frequency) {
    if (!isHardwareReady()) {
        return false;
    }
    
    decode_type_t type;
    uint64_t value;
    uint16_t bits;
    
    // Parse protocol signal data
    if (parseSignalData(protocol, data, type, value, bits)) {
        Serial.printf("📡 Sending %s signal: 0x%llX (%d bits) @ %d Hz\n", 
                     protocol.c_str(), value, bits, frequency);
        
        return sendProtocolSignal(type, value, bits);
    }
    
    // Fall back to raw data if available
    if (!rawData.isEmpty()) {
        Serial.printf("📡 Falling back to raw signal @ %d Hz\n", frequency);
        return sendRawSignal(rawData, frequency);
    }
    
    Serial.println("ERROR: Unable to parse signal data");
    return false;
}

bool IRController::batchSendSignals(const std::vector<String>& signalIds, int delayMs) {
    if (signalIds.empty()) {
        Serial.println("ERROR: No signals to send");
        return false;
    }
    
    Serial.printf("📡 Batch sending %d signals with %d ms delay\n", signalIds.size(), delayMs);
    
    bool allSuccess = true;
    
    for (size_t i = 0; i < signalIds.size(); i++) {
        const String& signalId = signalIds[i];
        
        Serial.printf("📡 Batch signal %d/%d: %s\n", i + 1, signalIds.size(), signalId.c_str());
        
        bool success = sendSignal(signalId);
        if (!success) {
            allSuccess = false;
            Serial.printf("❌ Batch signal failed: %s\n", signalId.c_str());
        }
        
        // Add delay between signals (except for the last one)
        if (i < signalIds.size() - 1 && delayMs > 0) {
            delay(delayMs);
        }
    }
    
    Serial.printf("📡 Batch transmission completed - Success: %s\n", allSuccess ? "YES" : "NO");
    return allSuccess;
}

// ==================== Status and Information ====================

DynamicJsonDocument IRController::getStatistics() const {
    DynamicJsonDocument stats(512);

    stats["total_signals_sent"] = totalSignalsSent;
    stats["total_signals_learned"] = totalSignalsLearned;
    stats["total_errors"] = totalErrors;
    stats["hardware_ready"] = isHardwareReady();
    stats["learning_state"] = static_cast<int>(m_learningState);
    stats["transmission_state"] = static_cast<int>(m_transmissionState);
    stats["is_transmitting"] = m_isTransmitting;
    stats["default_frequency"] = defaultFrequency;
    stats["learning_timeout"] = m_learningTimeoutMs;

    if (m_learningState == LearningState::LEARNING) {
        stats["learning_signal"] = m_currentLearningSignalName;
        stats["learning_elapsed"] = getLearningElapsed();
        stats["learning_remaining"] = getLearningRemaining();
    }

    return stats;
}

DynamicJsonDocument IRController::getAllSignalsJSON() {
    if (!dataManager) {
        DynamicJsonDocument empty(64);
        empty["error"] = "Data manager not available";
        return empty;
    }

    return dataManager->getAllSignalsJSON();
}

// ==================== Configuration ====================

void IRController::setLearningTimeout(unsigned long timeoutMs) {
    m_learningTimeoutMs = timeoutMs;
    Serial.printf("🔧 Learning timeout set to: %lu ms\n", timeoutMs);
}

// ==================== Event Callback Setters ====================

void IRController::setOnSignalLearned(std::function<void(const DynamicJsonDocument&)> callback) {
    onSignalLearned = callback;
    Serial.println("📡 Signal learned callback set");
}

void IRController::setOnSignalSent(std::function<void(const String&, bool)> callback) {
    onSignalSent = callback;
    Serial.println("📡 Signal sent callback set");
}

void IRController::setTransmissionFrequency(int frequency) {
    defaultFrequency = frequency;
    Serial.printf("🔧 Default transmission frequency set to: %d Hz\n", frequency);
}

// ==================== Private Methods ====================

bool IRController::initializeHardware() {
    Serial.println("Initializing IR hardware...");

    // Initialize IR transmitter
    irSend = new IRsend(IR_SEND_PIN);
    if (!irSend) {
        Serial.println("ERROR: Failed to create IR transmitter");
        return false;
    }

    irSend->begin();
    Serial.printf("✅ IR transmitter initialized on GPIO%d\n", IR_SEND_PIN);

    // Initialize IR receiver
    irRecv = new IRrecv(IR_RECV_PIN);
    if (!irRecv) {
        Serial.println("ERROR: Failed to create IR receiver");
        delete irSend;
        irSend = nullptr;
        return false;
    }

    irRecv->enableIRIn();
    Serial.printf("✅ IR receiver initialized on GPIO%d\n", IR_RECV_PIN);

    return true;
}

void IRController::cleanupHardware() {
    if (irSend) {
        delete irSend;
        irSend = nullptr;
    }

    if (irRecv) {
        delete irRecv;
        irRecv = nullptr;
    }

    Serial.println("IR hardware cleaned up");
}

bool IRController::processReceivedSignal() {
    if (!irRecv || !irRecv->decode(&results)) {
        return false;
    }

    if (m_learningState != LearningState::LEARNING) {
        irRecv->resume();
        return false;
    }

    // Parse the received signal
    DynamicJsonDocument signalData = parseIRSignal(results);

    if (signalData.isNull()) {
        Serial.println("ERROR: Failed to parse received signal");
        irRecv->resume();
        return false;
    }

    // Add signal name and metadata
    signalData["name"] = m_currentLearningSignalName;
    signalData["type"] = "learned";
    signalData["timestamp"] = millis();
    signalData["is_active"] = true;
    signalData["use_count"] = 0;
    signalData["description"] = "Learned signal from IR remote";

    // Save the signal
    if (dataManager) {
        String signalId = dataManager->createSignal(signalData);
        if (!signalId.isEmpty()) {
            totalSignalsLearned++;

            Serial.printf("✅ Signal learned and saved: %s (ID: %s)\n",
                         m_currentLearningSignalName.c_str(), signalId.c_str());

            if (onSignalLearned) {
                onSignalLearned(signalData);
            }

            irRecv->resume();
            return true;
        } else {
            Serial.println("ERROR: Failed to save learned signal");
        }
    } else {
        Serial.println("ERROR: Data manager not available");
    }

    irRecv->resume();
    return false;
}

DynamicJsonDocument IRController::parseIRSignal(const decode_results& results) {
    DynamicJsonDocument signal(512);

    // Get protocol name
    String protocolName = typeToString(results.decode_type);
    if (protocolName == "UNKNOWN") {
        protocolName = "RAW";
    }

    signal["protocol"] = protocolName;
    signal["frequency"] = defaultFrequency;

    // Handle known protocols
    if (results.decode_type != UNKNOWN) {
        signal["data"] = uint64ToString(results.value, 16);
        signal["bits"] = results.bits;

        Serial.printf("📡 Learned %s signal: 0x%s (%d bits)\n",
                     protocolName.c_str(), signal["data"].as<String>().c_str(), results.bits);
    }

    // Always include raw data for backup
    if (results.rawlen > 0 && results.rawlen <= MAX_RAW_LENGTH) {
        String rawData = "";
        for (uint16_t i = 1; i < results.rawlen; i++) {
            if (i > 1) rawData += ",";
            rawData += String(results.rawbuf[i] * kRawTick);
        }
        signal["raw_data"] = rawData;
        signal["raw_length"] = results.rawlen - 1;

        Serial.printf("📡 Raw data length: %d\n", results.rawlen - 1);
    }

    return signal;
}

bool IRController::sendProtocolSignal(decode_type_t type, uint64_t value, uint16_t bits) {
    if (!irSend) {
        return false;
    }

    try {
        switch (type) {
            case NEC:
                irSend->sendNEC(value, bits);
                break;
            case SONY:
                irSend->sendSony(value, bits);
                break;
            case SAMSUNG:
                irSend->sendSAMSUNG(value, bits);
                break;
            case LG:
                irSend->sendLG(value, bits);
                break;
            case PANASONIC:
                irSend->sendPanasonic(value, bits);
                break;
            case RC5:
                irSend->sendRC5(value, bits);
                break;
            case RC6:
                irSend->sendRC6(value, bits);
                break;
            default:
                Serial.printf("ERROR: Unsupported protocol: %d\n", type);
                return false;
        }

        return true;
    } catch (...) {
        Serial.println("ERROR: Exception during signal transmission");
        return false;
    }
}

bool IRController::sendRawSignal(const String& rawData, int frequency) {
    if (!irSend || rawData.isEmpty()) {
        return false;
    }

    // Parse raw data
    uint16_t rawArray[MAX_RAW_LENGTH];
    uint16_t rawLen = 0;

    if (!parseRawData(rawData, rawArray, rawLen)) {
        Serial.println("ERROR: Failed to parse raw data");
        return false;
    }

    try {
        irSend->sendRaw(rawArray, rawLen, frequency);
        return true;
    } catch (...) {
        Serial.println("ERROR: Exception during raw signal transmission");
        return false;
    }
}

bool IRController::parseSignalData(const String& protocol, const String& data,
                                  decode_type_t& type, uint64_t& value, uint16_t& bits) {
    if (protocol.isEmpty() || data.isEmpty()) {
        return false;
    }

    // Convert protocol string to decode_type_t
    if (protocol.equalsIgnoreCase("NEC")) {
        type = NEC;
        bits = 32;
    } else if (protocol.equalsIgnoreCase("SONY")) {
        type = SONY;
        bits = 12; // Default, can be 12, 15, or 20
    } else if (protocol.equalsIgnoreCase("SAMSUNG")) {
        type = SAMSUNG;
        bits = 32;
    } else if (protocol.equalsIgnoreCase("LG")) {
        type = LG;
        bits = 28;
    } else if (protocol.equalsIgnoreCase("PANASONIC")) {
        type = PANASONIC;
        bits = 48;
    } else if (protocol.equalsIgnoreCase("RC5")) {
        type = RC5;
        bits = 13;
    } else if (protocol.equalsIgnoreCase("RC6")) {
        type = RC6;
        bits = 20;
    } else {
        Serial.printf("ERROR: Unknown protocol: %s\n", protocol.c_str());
        return false;
    }

    // Parse hex data
    value = 0;
    String cleanData = data;
    cleanData.replace("0x", "");
    cleanData.replace("0X", "");

    for (int i = 0; i < cleanData.length(); i++) {
        char c = cleanData.charAt(i);
        value *= 16;

        if (c >= '0' && c <= '9') {
            value += c - '0';
        } else if (c >= 'A' && c <= 'F') {
            value += c - 'A' + 10;
        } else if (c >= 'a' && c <= 'f') {
            value += c - 'a' + 10;
        } else {
            Serial.printf("ERROR: Invalid hex character: %c\n", c);
            return false;
        }
    }

    return true;
}

bool IRController::parseRawData(const String& rawData, uint16_t* rawArray, uint16_t& rawLen) {
    if (rawData.isEmpty() || !rawArray) {
        return false;
    }

    rawLen = 0;
    int startIndex = 0;

    while (startIndex < rawData.length() && rawLen < MAX_RAW_LENGTH) {
        int commaIndex = rawData.indexOf(',', startIndex);

        String valueStr;
        if (commaIndex == -1) {
            valueStr = rawData.substring(startIndex);
        } else {
            valueStr = rawData.substring(startIndex, commaIndex);
        }

        valueStr.trim();
        if (valueStr.length() > 0) {
            rawArray[rawLen] = valueStr.toInt();
            rawLen++;
        }

        if (commaIndex == -1) {
            break;
        }

        startIndex = commaIndex + 1;
    }

    return rawLen > 0;
}

String IRController::uint64ToString(uint64_t value, uint8_t base) {
    String result = "";

    if (value == 0) {
        return "0";
    }

    while (value > 0) {
        uint8_t digit = value % base;
        if (digit < 10) {
            result = char('0' + digit) + result;
        } else {
            result = char('A' + digit - 10) + result;
        }
        value /= base;
    }

    if (base == 16) {
        result = "0x" + result;
    }

    return result;
}

bool IRController::isValidProtocol(const String& protocol) {
    return (protocol.equalsIgnoreCase("NEC") ||
            protocol.equalsIgnoreCase("SONY") ||
            protocol.equalsIgnoreCase("SAMSUNG") ||
            protocol.equalsIgnoreCase("LG") ||
            protocol.equalsIgnoreCase("PANASONIC") ||
            protocol.equalsIgnoreCase("RC5") ||
            protocol.equalsIgnoreCase("RC6") ||
            protocol.equalsIgnoreCase("RAW"));
}

int IRController::getDefaultBitsForProtocol(decode_type_t type) {
    switch (type) {
        case NEC: return 32;
        case SONY: return 12;
        case SAMSUNG: return 32;
        case LG: return 28;
        case PANASONIC: return 48;
        case RC5: return 13;
        case RC6: return 20;
        default: return 32;
    }
}

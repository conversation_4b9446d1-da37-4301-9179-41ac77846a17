/*
 * SystemManager.h - Central System Management
 * 
 * 零全局内存分配的系统管理器
 * 确保100% API兼容性和PSRAM优化
 */

#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

// ==================== WiFi Configuration ====================
// 用户需要修改这些WiFi设置
const char* WIFI_SSID = "YourWiFiSSID";        // 修改为您的WiFi名称
const char* WIFI_PASSWORD = "YourWiFiPassword"; // 修改为您的WiFi密码

// Forward declarations
class DataManager;
class IRController;
class WebServerManager;
class TaskManager;
class WSManager;

// ==================== System Mode Management ====================
enum class SystemMode {
    HIGH_PERFORMANCE,  // PSRAM available
    STANDARD          // RAM only
};

struct SystemCapacity {
    int maxSignals;
    int maxTasks;
    int maxTimers;
    size_t bufferSize;

    static SystemCapacity getCapacity(SystemMode mode) {
        if (mode == SystemMode::HIGH_PERFORMANCE) {
            return {1000, 100, 50, 8192};  // High performance mode
        } else {
            return {100, 20, 10, 2048};    // Standard mode
        }
    }
};

class SystemManager {
public:
    SystemManager();
    ~SystemManager();
    
    // Initialization
    bool initialize();
    void handleLoop();
    
    // Component access
    DataManager* getDataManager();
    IRController* getIRController();
    WebServerManager* getWebServerManager();
    TaskManager* getTaskManager();
    WSManager* getWSManager();
    
    // System status
    bool isInitialized() const { return m_initialized; }
    unsigned long getUptime() const;
    
    // System control
    void restart();
    void factoryReset();
    
    // Memory management
    void printMemoryStatus();
    size_t getFreePSRAM();
    size_t getFreeHeap();

    // System mode management
    SystemMode getSystemMode() const { return m_systemMode; }
    SystemCapacity getSystemCapacity() const { return m_systemCapacity; }
    bool isPSRAMAvailable() const;
    const char* getSystemModeString() const;

    // ESP32 connection status (for frontend compatibility)
    bool isESP32Connected() const { return true; } // Always true since we ARE the ESP32

private:
    bool m_initialized;
    unsigned long m_startTime;

    // System mode and capacity
    SystemMode m_systemMode;
    SystemCapacity m_systemCapacity;
    
    // Component managers - created after PSRAM is available
    DataManager* m_dataManager;
    IRController* m_irController;
    WebServerManager* m_webServerManager;
    TaskManager* m_taskManager;
    WSManager* m_wsManager;
    
    // Core server components - created after PSRAM is available
    AsyncWebServer* m_server;
    AsyncWebSocket* m_webSocket;
    
    // WiFi management
    bool m_wifiInitialized;
    bool m_apMode;
    unsigned long m_lastConnectionAttempt;
    int m_connectionAttempts;
    static const int MAX_CONNECTION_ATTEMPTS = 3;
    static const unsigned long CONNECTION_TIMEOUT = 30000;
    
    // Initialization helpers
    bool initializeComponents();
    bool initializeNetwork();
    bool initializeWebServer();
    
    // WiFi management
    void initializeWiFi();
    void handleWiFiLoop();
    void startAccessPoint();
    void attemptStationConnection();
    
    // Cleanup
    void cleanup();
};

#endif // SYSTEM_MANAGER_H

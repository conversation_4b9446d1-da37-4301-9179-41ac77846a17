// ESP32-S3 IR System - Arduino IDE Build Options
// 从PlatformIO转换的编译器宏定义
// 这些宏定义对于ESPAsyncWebServer平台检测是必需的

// Core ESP32-S3 Configuration
-DARDUINO_ESP32S3_DEV
-<PERSON>ARD<PERSON>NO_ARCH_ESP32
-DESP32
-DESP32S3
-DCONFIG_FREERTOS_UNICORE=0
-DBOARD_HAS_PSRAM

// OPI PSRAM Configuration
-DCONFIG_SPIRAM_SUPPORT=1
-DCONFIG_SPIRAM_MODE_OCT=1
-DCONFIG_SPIRAM_TYPE_ESPPSRAM64=1
-DCONFIG_SPIRAM_USE_CAPS_ALLOC=1
-DCON<PERSON>G_SPIRAM_USE_MALLOC=1
-DCONFIG_SPIRAM_CACHE_WORKAROUND=1

// USB Debug
-DARDUINO_USB_CDC_ON_BOOT=1

// IR Decode Options
-DSEND_RAW=true
-DDECODE_NEC=true
-DDECODE_SONY=true
-DDECODE_SAMSUNG=true
-DDECODE_LG=true
-DDECODE_PANASONIC=true
-DDECODE_RC5=true
-DDECODE_RC6=true
-DDECODE_HASH=true

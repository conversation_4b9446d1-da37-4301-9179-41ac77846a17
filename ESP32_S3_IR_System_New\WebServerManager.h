/*
 * WebServerManager.h - Web服务器管理器（100% API兼容）
 * 
 * 零全局内存分配的Web服务器管理系统
 * 完全兼容原前端的所有HTTP API调用
 */

#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

// Forward declarations
class DataManager;
class IRController;
class TaskManager;
class WSManager;

class WebServerManager {
public:
    WebServerManager();
    ~WebServerManager();
    
    bool initialize(AsyncWebServer* server, DataManager* dataManager, 
                   IRController* irController, TaskManager* taskManager, WSManager* wsManager);
    void handleLoop();
    
    // Server status
    bool isInitialized() const { return m_initialized; }
    DynamicJsonDocument getServerStats() const;

private:
    bool m_initialized;
    
    // Component references
    AsyncWebServer* m_server;
    DataManager* m_dataManager;
    IRController* m_irController;
    TaskManager* m_taskManager;
    WSManager* m_wsManager;
    
    // Statistics
    unsigned long m_totalRequests;
    unsigned long m_lastRequestTime;
    
    // Setup methods
    void setupRoutes();
    void setupCORS();
    void setupStaticFiles();
    void setupErrorHandlers();
    
    // ==================== API Handlers (100% Compatible) ====================
    
    // System API
    void handleGetSystemStatus(AsyncWebServerRequest* request);
    void handleGetSystemInfo(AsyncWebServerRequest* request);
    void handlePostSystemRestart(AsyncWebServerRequest* request);
    
    // Signal API
    void handleGetSignals(AsyncWebServerRequest* request);
    void handlePostSignalsSend(AsyncWebServerRequest* request);
    void handlePostSignalsLearn(AsyncWebServerRequest* request);
    void handlePostSignalsLearnStop(AsyncWebServerRequest* request);
    void handleGetSignalsLearnStatus(AsyncWebServerRequest* request);
    void handlePostSignalsBatch(AsyncWebServerRequest* request);
    void handlePostSignalsDelete(AsyncWebServerRequest* request);
    
    // Task API
    void handleGetTasks(AsyncWebServerRequest* request);
    void handlePostTasks(AsyncWebServerRequest* request);
    void handlePostTasksControl(AsyncWebServerRequest* request);
    void handleGetTasksStatus(AsyncWebServerRequest* request);
    void handlePostTasksDelete(AsyncWebServerRequest* request);
    
    // Timer API
    void handleGetTimers(AsyncWebServerRequest* request);
    void handlePostTimers(AsyncWebServerRequest* request);
    void handlePostTimersToggle(AsyncWebServerRequest* request);
    void handlePostTimersUpdate(AsyncWebServerRequest* request);
    void handlePostTimersDelete(AsyncWebServerRequest* request);
    
    // Data API
    void handleGetDataExport(AsyncWebServerRequest* request);
    void handlePostDataImport(AsyncWebServerRequest* request);
    void handlePostDataBackup(AsyncWebServerRequest* request);
    void handlePostDataClear(AsyncWebServerRequest* request);
    
    // Config API
    void handleGetConfig(AsyncWebServerRequest* request);
    void handlePostConfig(AsyncWebServerRequest* request);
    void handlePostConfigReset(AsyncWebServerRequest* request);
    
    // WebSocket API
    void handleGetWebSocketStats(AsyncWebServerRequest* request);
    void handleGetWebSocketClients(AsyncWebServerRequest* request);
    void handlePostWebSocketBroadcast(AsyncWebServerRequest* request);
    
    // Batch API
    void handlePostBatch(AsyncWebServerRequest* request);
    
    // ==================== Helper Methods ====================
    
    // Response helpers
    void sendJsonResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& doc, int code = 200);
    void sendSuccessResponse(AsyncWebServerRequest* request, const String& message = "Success");
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
    void sendNotFoundResponse(AsyncWebServerRequest* request);
    
    // Request parsing
    DynamicJsonDocument parseRequestBody(AsyncWebServerRequest* request);
    String getRequestParam(AsyncWebServerRequest* request, const String& name, const String& defaultValue = "");
    bool hasRequestParam(AsyncWebServerRequest* request, const String& name);
    
    // Validation
    bool validateJsonRequest(AsyncWebServerRequest* request, const DynamicJsonDocument& doc);
    bool validateSignalData(const DynamicJsonDocument& data);
    bool validateTaskData(const DynamicJsonDocument& data);
    bool validateTimerData(const DynamicJsonDocument& data);
    
    // CORS handling
    void handleCORSPreflight(AsyncWebServerRequest* request);
    void addCORSHeaders(AsyncWebServerResponse* response);
    
    // Request body handling
    static void onRequestBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
};

#endif // WEB_SERVER_MANAGER_H

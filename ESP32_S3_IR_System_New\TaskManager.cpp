#include "TaskManager.h"
#include "DataManager.h"
#include "IRController.h"

TaskManager::TaskManager() 
    : m_initialized(false)
    , m_dataManager(nullptr)
    , m_ir<PERSON>ontroller(nullptr)
    , m_totalTasksExecuted(0)
    , m_totalTasksCompleted(0)
    , m_totalTasksFailed(0)
    , m_lastExecutionTime(0)
{
    Serial.println("TaskManager created (no memory allocated)");
}

TaskManager::~TaskManager() {
    // Smart pointers automatically clean up
    Serial.println("TaskManager destroyed");
}

bool TaskManager::initialize() {
    Serial.println("⚙️  Initializing TaskManager...");
    
    if (m_initialized) {
        Serial.println("TaskManager already initialized");
        return true;
    }
    
    // Create storage containers after PSRAM is available
    if (!initializeStorage()) {
        Serial.println("❌ Failed to initialize task storage");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ TaskManager initialized successfully");
    
    return true;
}

bool TaskManager::initializeStorage() {
    Serial.println("🗄️  Creating task storage containers...");
    
    try {
        m_runningTasks = std::make_unique<std::vector<TaskContext>>();
        m_scheduledTasks = std::make_unique<std::vector<TaskContext>>();
        
        if (!m_runningTasks || !m_scheduledTasks) {
            Serial.println("❌ Failed to create task storage containers");
            return false;
        }
        
        Serial.println("✅ Task storage containers created successfully");
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception creating task storage: %s\n", e.what());
        return false;
    }
}

void TaskManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Process scheduled tasks
    if (m_scheduledTasks) {
        for (auto it = m_scheduledTasks->begin(); it != m_scheduledTasks->end();) {
            if (millis() >= it->startTime) {
                // Move to running tasks
                if (m_runningTasks) {
                    it->state = TaskState::RUNNING;
                    it->startTime = millis();
                    m_runningTasks->push_back(*it);
                }
                it = m_scheduledTasks->erase(it);
            } else {
                ++it;
            }
        }
    }
    
    // Process running tasks
    if (m_runningTasks) {
        for (auto& context : *m_runningTasks) {
            if (context.state == TaskState::RUNNING) {
                executeTaskStep(context);
            }
        }
    }
    
    // Cleanup completed tasks periodically
    static unsigned long lastCleanup = 0;
    if (millis() - lastCleanup > 10000) { // Every 10 seconds
        cleanupCompletedTasks();
        lastCleanup = millis();
    }
}

// ==================== Task Control ====================

bool TaskManager::startTask(const String& taskId) {
    if (!m_initialized || !m_dataManager) {
        return false;
    }
    
    if (!validateTaskId(taskId)) {
        Serial.printf("❌ Invalid task ID: %s\n", taskId.c_str());
        return false;
    }
    
    // Check if task is already running
    if (findRunningTask(taskId)) {
        Serial.printf("⚠️  Task already running: %s\n", taskId.c_str());
        return false;
    }
    
    return executeTask(taskId);
}

bool TaskManager::stopTask(const String& taskId) {
    if (!m_initialized) {
        return false;
    }
    
    TaskContext* context = findRunningTask(taskId);
    if (!context) {
        Serial.printf("❌ Running task not found: %s\n", taskId.c_str());
        return false;
    }
    
    updateTaskContext(*context, TaskState::CANCELLED);
    Serial.printf("✅ Task stopped: %s\n", taskId.c_str());
    
    return true;
}

bool TaskManager::pauseTask(const String& taskId) {
    if (!m_initialized) {
        return false;
    }
    
    TaskContext* context = findRunningTask(taskId);
    if (!context) {
        return false;
    }
    
    if (context->state == TaskState::RUNNING) {
        updateTaskContext(*context, TaskState::PAUSED);
        Serial.printf("⏸️  Task paused: %s\n", taskId.c_str());
        return true;
    }
    
    return false;
}

bool TaskManager::resumeTask(const String& taskId) {
    if (!m_initialized) {
        return false;
    }
    
    TaskContext* context = findRunningTask(taskId);
    if (!context) {
        return false;
    }
    
    if (context->state == TaskState::PAUSED) {
        updateTaskContext(*context, TaskState::RUNNING);
        Serial.printf("▶️  Task resumed: %s\n", taskId.c_str());
        return true;
    }
    
    return false;
}

bool TaskManager::cancelTask(const String& taskId) {
    if (!m_initialized) {
        return false;
    }
    
    // Try to cancel running task
    if (removeRunningTask(taskId)) {
        Serial.printf("✅ Running task cancelled: %s\n", taskId.c_str());
        return true;
    }
    
    // Try to cancel scheduled task
    if (removeScheduledTask(taskId)) {
        Serial.printf("✅ Scheduled task cancelled: %s\n", taskId.c_str());
        return true;
    }
    
    Serial.printf("❌ Task not found: %s\n", taskId.c_str());
    return false;
}

// ==================== Task Status ====================

DynamicJsonDocument TaskManager::getTaskStatus(const String& taskId) const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;
    
    JsonObject data = doc.createNestedObject("data");
    data["taskId"] = taskId;
    
    // Check running tasks
    if (m_runningTasks) {
        for (const auto& context : *m_runningTasks) {
            if (context.taskId == taskId) {
                data["state"] = taskStateToString(context.state);
                data["currentStep"] = context.currentStep;
                data["totalSteps"] = context.totalSteps;
                data["startTime"] = context.startTime;
                data["elapsed"] = millis() - context.startTime;
                data["currentSignal"] = context.currentSignalId;
                if (!context.errorMessage.isEmpty()) {
                    data["error"] = context.errorMessage;
                }
                data["timestamp"] = millis();
                return doc;
            }
        }
    }
    
    // Check scheduled tasks
    if (m_scheduledTasks) {
        for (const auto& context : *m_scheduledTasks) {
            if (context.taskId == taskId) {
                data["state"] = "scheduled";
                data["scheduledTime"] = context.startTime;
                data["remaining"] = context.startTime - millis();
                data["timestamp"] = millis();
                return doc;
            }
        }
    }
    
    // Task not found
    data["state"] = "not_found";
    data["timestamp"] = millis();
    
    return doc;
}

DynamicJsonDocument TaskManager::getAllTasksStatus() const {
    DynamicJsonDocument doc(2048);
    doc["success"] = true;
    
    JsonArray tasks = doc.createNestedArray("data");
    
    // Add running tasks
    if (m_runningTasks) {
        for (const auto& context : *m_runningTasks) {
            JsonObject task = tasks.createNestedObject();
            task["taskId"] = context.taskId;
            task["state"] = taskStateToString(context.state);
            task["currentStep"] = context.currentStep;
            task["totalSteps"] = context.totalSteps;
            task["startTime"] = context.startTime;
            task["elapsed"] = millis() - context.startTime;
            task["currentSignal"] = context.currentSignalId;
            if (!context.errorMessage.isEmpty()) {
                task["error"] = context.errorMessage;
            }
        }
    }
    
    // Add scheduled tasks
    if (m_scheduledTasks) {
        for (const auto& context : *m_scheduledTasks) {
            JsonObject task = tasks.createNestedObject();
            task["taskId"] = context.taskId;
            task["state"] = "scheduled";
            task["scheduledTime"] = context.startTime;
            task["remaining"] = context.startTime - millis();
        }
    }
    
    doc["count"] = tasks.size();
    doc["timestamp"] = millis();
    
    return doc;
}

bool TaskManager::isTaskRunning(const String& taskId) const {
    return findRunningTask(taskId) != nullptr;
}

// ==================== Batch Operations ====================

bool TaskManager::executeBatch(const String& signalIds, int delayMs) {
    if (!m_initialized || !m_irController) {
        return false;
    }

    Serial.printf("⚙️  Executing batch with %d ms delay\n", delayMs);

    // Create a temporary task context for batch execution
    TaskContext batchContext;
    batchContext.taskId = "batch_" + String(millis());
    batchContext.state = TaskState::RUNNING;
    batchContext.startTime = millis();

    bool success = executeSignalSequence(signalIds, delayMs);

    if (success) {
        updateTaskContext(batchContext, TaskState::COMPLETED);
        m_totalTasksCompleted++;
    } else {
        updateTaskContext(batchContext, TaskState::FAILED, "Batch execution failed");
        m_totalTasksFailed++;
    }

    m_totalTasksExecuted++;
    m_lastExecutionTime = millis();

    return success;
}

bool TaskManager::stopAllTasks() {
    if (!m_initialized || !m_runningTasks) {
        return false;
    }

    int stoppedCount = 0;
    for (auto& context : *m_runningTasks) {
        if (context.state == TaskState::RUNNING || context.state == TaskState::PAUSED) {
            updateTaskContext(context, TaskState::CANCELLED);
            stoppedCount++;
        }
    }

    Serial.printf("✅ Stopped %d tasks\n", stoppedCount);
    return true;
}

bool TaskManager::pauseAllTasks() {
    if (!m_initialized || !m_runningTasks) {
        return false;
    }

    int pausedCount = 0;
    for (auto& context : *m_runningTasks) {
        if (context.state == TaskState::RUNNING) {
            updateTaskContext(context, TaskState::PAUSED);
            pausedCount++;
        }
    }

    Serial.printf("⏸️  Paused %d tasks\n", pausedCount);
    return true;
}

bool TaskManager::resumeAllTasks() {
    if (!m_initialized || !m_runningTasks) {
        return false;
    }

    int resumedCount = 0;
    for (auto& context : *m_runningTasks) {
        if (context.state == TaskState::PAUSED) {
            updateTaskContext(context, TaskState::RUNNING);
            resumedCount++;
        }
    }

    Serial.printf("▶️  Resumed %d tasks\n", resumedCount);
    return true;
}

// ==================== Timer Integration ====================

bool TaskManager::scheduleTask(const String& taskId, unsigned long delayMs) {
    if (!m_initialized || !m_scheduledTasks) {
        return false;
    }

    if (!validateTaskId(taskId)) {
        return false;
    }

    TaskContext scheduledContext;
    scheduledContext.taskId = taskId;
    scheduledContext.startTime = millis() + delayMs;
    scheduledContext.state = TaskState::PENDING;

    m_scheduledTasks->push_back(scheduledContext);

    Serial.printf("⏰ Task scheduled: %s (delay: %lu ms)\n", taskId.c_str(), delayMs);
    return true;
}

bool TaskManager::scheduleRecurringTask(const String& taskId, unsigned long intervalMs) {
    // For now, just schedule once
    // In a full implementation, you would track recurring tasks separately
    return scheduleTask(taskId, intervalMs);
}

bool TaskManager::cancelScheduledTask(const String& taskId) {
    return removeScheduledTask(taskId);
}

// ==================== Statistics and Monitoring ====================

DynamicJsonDocument TaskManager::getStatistics() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");
    data["totalExecuted"] = m_totalTasksExecuted;
    data["totalCompleted"] = m_totalTasksCompleted;
    data["totalFailed"] = m_totalTasksFailed;
    data["activeCount"] = getActiveTaskCount();
    data["scheduledCount"] = m_scheduledTasks ? m_scheduledTasks->size() : 0;
    data["lastExecutionTime"] = m_lastExecutionTime;

    if (m_totalTasksExecuted > 0) {
        data["successRate"] = (float)m_totalTasksCompleted / m_totalTasksExecuted * 100;
    } else {
        data["successRate"] = 0;
    }

    data["timestamp"] = millis();

    return doc;
}

int TaskManager::getActiveTaskCount() const {
    if (!m_runningTasks) return 0;

    int count = 0;
    for (const auto& context : *m_runningTasks) {
        if (context.state == TaskState::RUNNING || context.state == TaskState::PAUSED) {
            count++;
        }
    }
    return count;
}

int TaskManager::getCompletedTaskCount() const {
    return m_totalTasksCompleted;
}

// ==================== Internal Methods ====================

bool TaskManager::executeTask(const String& taskId) {
    if (!m_dataManager || !m_runningTasks) {
        return false;
    }

    TaskData* taskData = m_dataManager->getTask(taskId);
    if (!taskData) {
        Serial.printf("❌ Task data not found: %s\n", taskId.c_str());
        return false;
    }

    TaskContext context;
    context.taskId = taskId;
    context.state = TaskState::RUNNING;
    context.startTime = millis();
    context.lastStepTime = millis();

    // Parse signal IDs from task data parameters
    DynamicJsonDocument params = taskData->getParameters();
    String signalIds = params["signalIds"] | "";
    if (signalIds.isEmpty()) {
        Serial.printf("❌ No signals defined for task: %s\n", taskId.c_str());
        return false;
    }

    // Count total steps
    int commaCount = 0;
    for (int i = 0; i < signalIds.length(); i++) {
        if (signalIds.charAt(i) == ',') commaCount++;
    }
    context.totalSteps = commaCount + 1;

    m_runningTasks->push_back(context);

    Serial.printf("⚙️  Task started: %s (%d steps)\n", taskId.c_str(), context.totalSteps);
    m_totalTasksExecuted++;

    return true;
}

bool TaskManager::executeTaskStep(TaskContext& context) {
    if (!m_dataManager || !m_irController) {
        updateTaskContext(context, TaskState::FAILED, "Missing components");
        return false;
    }

    TaskData* taskData = m_dataManager->getTask(context.taskId);
    if (!taskData) {
        updateTaskContext(context, TaskState::FAILED, "Task data not found");
        return false;
    }

    // Parse signal IDs and execute current step
    DynamicJsonDocument params = taskData->getParameters();
    String signalIds = params["signalIds"] | "";
    String currentSignalId = "";

    int currentIndex = 0;
    int startPos = 0;

    for (int i = 0; i <= signalIds.length(); i++) {
        if (i == signalIds.length() || signalIds.charAt(i) == ',') {
            if (currentIndex == context.currentStep) {
                currentSignalId = signalIds.substring(startPos, i);
                currentSignalId.trim();
                break;
            }
            currentIndex++;
            startPos = i + 1;
        }
    }

    if (currentSignalId.isEmpty()) {
        updateTaskContext(context, TaskState::COMPLETED);
        m_totalTasksCompleted++;
        return true;
    }

    context.currentSignalId = currentSignalId;

    // Execute the signal
    if (executeSignal(currentSignalId)) {
        context.currentStep++;
        context.lastStepTime = millis();

        if (context.currentStep >= context.totalSteps) {
            updateTaskContext(context, TaskState::COMPLETED);
            m_totalTasksCompleted++;
        }

        return true;
    } else {
        updateTaskContext(context, TaskState::FAILED, "Signal execution failed");
        m_totalTasksFailed++;
        return false;
    }
}

void TaskManager::updateTaskContext(TaskContext& context, TaskState newState, const String& error) {
    context.state = newState;
    if (!error.isEmpty()) {
        context.errorMessage = error;
    }

    Serial.printf("📊 Task %s: %s\n", context.taskId.c_str(), taskStateToString(newState).c_str());
}

void TaskManager::cleanupCompletedTasks() {
    if (!m_runningTasks) return;

    auto it = m_runningTasks->begin();
    while (it != m_runningTasks->end()) {
        if (it->state == TaskState::COMPLETED ||
            it->state == TaskState::FAILED ||
            it->state == TaskState::CANCELLED) {

            // Keep completed tasks for a while for status queries
            if (millis() - it->lastStepTime > 60000) { // 1 minute
                it = m_runningTasks->erase(it);
            } else {
                ++it;
            }
        } else {
            ++it;
        }
    }
}

// ==================== Task Context Management ====================

TaskContext* TaskManager::findRunningTask(const String& taskId) {
    if (!m_runningTasks) return nullptr;

    for (auto& context : *m_runningTasks) {
        if (context.taskId == taskId) {
            return &context;
        }
    }
    return nullptr;
}

TaskContext* TaskManager::findScheduledTask(const String& taskId) {
    if (!m_scheduledTasks) return nullptr;

    for (auto& context : *m_scheduledTasks) {
        if (context.taskId == taskId) {
            return &context;
        }
    }
    return nullptr;
}

bool TaskManager::removeRunningTask(const String& taskId) {
    if (!m_runningTasks) return false;

    auto it = m_runningTasks->begin();
    while (it != m_runningTasks->end()) {
        if (it->taskId == taskId) {
            m_runningTasks->erase(it);
            return true;
        }
        ++it;
    }
    return false;
}

bool TaskManager::removeScheduledTask(const String& taskId) {
    if (!m_scheduledTasks) return false;

    auto it = m_scheduledTasks->begin();
    while (it != m_scheduledTasks->end()) {
        if (it->taskId == taskId) {
            m_scheduledTasks->erase(it);
            return true;
        }
        ++it;
    }
    return false;
}

// ==================== Signal Execution ====================

bool TaskManager::executeSignal(const String& signalId) {
    if (!m_irController) {
        Serial.printf("❌ IRController not available for signal: %s\n", signalId.c_str());
        return false;
    }

    return m_irController->sendSignal(signalId);
}

bool TaskManager::executeSignalSequence(const String& signalIds, int delayMs) {
    if (!m_irController) {
        return false;
    }

    return m_irController->sendBatchSignals(signalIds, delayMs);
}

// ==================== Validation ====================

bool TaskManager::validateTaskId(const String& taskId) const {
    if (taskId.isEmpty()) {
        return false;
    }

    if (!m_dataManager) {
        return false;
    }

    TaskData* taskData = m_dataManager->getTask(taskId);
    return taskData != nullptr;
}

TaskState TaskManager::stringToTaskState(const String& stateStr) const {
    if (stateStr == "pending") return TaskState::PENDING;
    if (stateStr == "running") return TaskState::RUNNING;
    if (stateStr == "paused") return TaskState::PAUSED;
    if (stateStr == "completed") return TaskState::COMPLETED;
    if (stateStr == "failed") return TaskState::FAILED;
    if (stateStr == "cancelled") return TaskState::CANCELLED;
    return TaskState::PENDING;
}

String TaskManager::taskStateToString(TaskState state) const {
    switch (state) {
        case TaskState::PENDING: return "pending";
        case TaskState::RUNNING: return "running";
        case TaskState::PAUSED: return "paused";
        case TaskState::COMPLETED: return "completed";
        case TaskState::FAILED: return "failed";
        case TaskState::CANCELLED: return "cancelled";
        default: return "unknown";
    }
}

#ifndef PSRAM_MANAGER_H
#define PSRAM_MANAGER_H

#include <Arduino.h>
#include "../../config/system-config.h"

/**
 * PSRAM管理器类
 * 
 * 负责ESP32-S3 PSRAM的检测、初始化和管理
 * 实现优雅降级机制，确保系统在PSRAM不可用时仍能正常运行
 * 
 * 核心功能：
 * - PSRAM硬件检测
 * - PSRAM功能测试
 * - 系统模式选择
 * - 内存状态监控
 * - 错误恢复机制
 */
class PSRAMManager {
public:
    // ==================== 静态接口 ====================
    
    /**
     * 初始化PSRAM管理器
     * @return SystemMode 返回系统运行模式
     */
    static SystemMode initialize();
    
    /**
     * 检测PSRAM是否可用
     * @return bool PSRAM是否可用
     */
    static bool isPSRAMAvailable();
    
    /**
     * 获取PSRAM大小
     * @return size_t PSRAM总大小（字节）
     */
    static size_t getPSRAMSize();
    
    /**
     * 获取可用PSRAM大小
     * @return size_t 可用PSRAM大小（字节）
     */
    static size_t getFreePSRAM();
    
    /**
     * 获取PSRAM使用率
     * @return float PSRAM使用率（0.0-1.0）
     */
    static float getPSRAMUsage();
    
    /**
     * 打印PSRAM状态信息
     */
    static void printPSRAMStatus();
    
    /**
     * 执行PSRAM功能测试
     * @return bool 测试是否通过
     */
    static bool testPSRAMFunctionality();
    
    /**
     * 获取系统模式字符串
     * @param mode 系统模式
     * @return const char* 模式字符串
     */
    static const char* getSystemModeString(SystemMode mode);
    
    // ==================== 内存监控 ====================
    
    /**
     * 检查内存健康状态
     * @return bool 内存状态是否健康
     */
    static bool checkMemoryHealth();
    
    /**
     * 获取内存碎片化程度
     * @return float 碎片化程度（0.0-1.0）
     */
    static float getMemoryFragmentation();
    
    /**
     * 执行内存清理
     */
    static void performMemoryCleanup();
    
    // ==================== 错误处理 ====================
    
    /**
     * 获取最后的错误信息
     * @return const char* 错误信息
     */
    static const char* getLastError();
    
    /**
     * 清除错误状态
     */
    static void clearError();
    
    /**
     * 重新初始化PSRAM
     * @return bool 重新初始化是否成功
     */
    static bool reinitializePSRAM();

private:
    // ==================== 私有成员 ====================
    static bool s_initialized;          // 是否已初始化
    static bool s_psramAvailable;       // PSRAM是否可用
    static SystemMode s_systemMode;     // 当前系统模式
    static String s_lastError;          // 最后的错误信息
    static unsigned long s_lastCheck;   // 最后检查时间
    
    // ==================== 私有方法 ====================
    
    /**
     * 检查硬件配置
     * @return bool 硬件配置是否正确
     */
    static bool checkHardwareConfiguration();
    
    /**
     * 执行PSRAM基础测试
     * @return bool 基础测试是否通过
     */
    static bool performBasicPSRAMTest();
    
    /**
     * 执行PSRAM压力测试
     * @return bool 压力测试是否通过
     */
    static bool performPSRAMStressTest();
    
    /**
     * 执行PSRAM读写测试
     * @param size 测试数据大小
     * @return bool 读写测试是否通过
     */
    static bool performPSRAMReadWriteTest(size_t size);
    
    /**
     * 检查Flash模式配置
     * @return bool Flash模式是否正确
     */
    static bool checkFlashMode();
    
    /**
     * 检查分区表配置
     * @return bool 分区表是否正确
     */
    static bool checkPartitionTable();
    
    /**
     * 设置错误信息
     * @param error 错误信息
     */
    static void setError(const String& error);
    
    /**
     * 记录PSRAM初始化日志
     * @param message 日志信息
     * @param isError 是否为错误日志
     */
    static void logPSRAMStatus(const String& message, bool isError = false);
    
    // ==================== 测试模式 ====================
    
    /**
     * 生成测试数据
     * @param buffer 缓冲区
     * @param size 数据大小
     */
    static void generateTestData(uint8_t* buffer, size_t size);
    
    /**
     * 验证测试数据
     * @param buffer 缓冲区
     * @param size 数据大小
     * @return bool 数据是否正确
     */
    static bool verifyTestData(const uint8_t* buffer, size_t size);
    
    /**
     * 执行内存模式测试
     * @return bool 内存模式测试是否通过
     */
    static bool testMemoryPatterns();
    
    // ==================== 调试功能 ====================
    
#ifdef DEBUG_MODE
    /**
     * 打印详细的PSRAM信息
     */
    static void debugPrintPSRAMDetails();
    
    /**
     * 打印内存映射信息
     */
    static void debugPrintMemoryMap();
    
    /**
     * 执行PSRAM基准测试
     */
    static void debugPSRAMBenchmark();
#endif
};

// ==================== 内联函数实现 ====================

inline bool PSRAMManager::isPSRAMAvailable() {
    return s_psramAvailable;
}

inline size_t PSRAMManager::getPSRAMSize() {
    return psramFound() ? ESP.getPsramSize() : 0;
}

inline size_t PSRAMManager::getFreePSRAM() {
    return psramFound() ? ESP.getFreePsram() : 0;
}

inline float PSRAMManager::getPSRAMUsage() {
    if (!psramFound()) return 0.0f;
    size_t total = ESP.getPsramSize();
    size_t free = ESP.getFreePsram();
    return total > 0 ? (float)(total - free) / total : 0.0f;
}

inline const char* PSRAMManager::getLastError() {
    return s_lastError.c_str();
}

inline void PSRAMManager::clearError() {
    s_lastError = "";
}

// ==================== 全局辅助函数 ====================

/**
 * 检查当前是否运行在高性能模式
 * @return bool 是否为高性能模式
 */
inline bool isHighPerformanceMode() {
    return PSRAMManager::isPSRAMAvailable();
}

/**
 * 获取推荐的缓冲区大小
 * @param baseSize 基础大小
 * @return size_t 推荐的缓冲区大小
 */
inline size_t getRecommendedBufferSize(size_t baseSize) {
    return PSRAMManager::isPSRAMAvailable() ? baseSize * 4 : baseSize;
}

/**
 * 获取推荐的最大项目数量
 * @param baseCount 基础数量
 * @return int 推荐的最大数量
 */
inline int getRecommendedMaxCount(int baseCount) {
    return PSRAMManager::isPSRAMAvailable() ? baseCount * 10 : baseCount;
}

#endif // PSRAM_MANAGER_H

# ESP32-S3 IR Control System

## Project Overview

This is an ESP32-S3 based infrared control system with OPI PSRAM support, featuring complete Web interface and WebSocket real-time communication.

## PSRAM Problem Solved

This project has completely solved the ESP32-S3 OPI PSRAM detection failure issue:
- **Root Cause**: DynamicJsonDocument global constructors in DataManager and TaskManager allocate memory before PSRAM initialization
- **Solution**: Lightweight cache approach with on-demand JSON object creation
- **Result**: 100% successful PSRAM detection, stable system operation

## 🔧 Arduino IDE 配置

### 板子设置
- **Board**: ESP32S3 Dev Module
- **Flash Mode**: QIO 80MHz
- **Flash Size**: 16MB (128Mb)
- **PSRAM**: OPI PSRAM
- **Upload Speed**: 460800
- **CPU Frequency**: 240MHz (WiFi)

### 必需库
```
ArduinoJson (6.21.3)
ESPAsyncWebServer
AsyncTCP
IRremoteESP8266 (2.8.6)
```

## 🚀 快速开始

1. **配置Arduino IDE**：按照上述设置配置板子
2. **安装库**：通过库管理器安装必需库
3. **编译上传**：打开ESP32_S3_IR_System_Arduino.ino编译上传
4. **验证PSRAM**：查看串口输出确认PSRAM检测成功

## 📊 预期输出

```
🎉 PSRAM DETECTION SUCCESSFUL!
✅ PSRAM is available!
📊 Size: 8386279 bytes (8.00 MB)
🧠 Free: 8386035 bytes (8.00 MB)
🚀 PSRAM ready for use!

✅ Data Manager initialized successfully
✅ IR Controller initialized successfully
✅ WiFi Manager initialized successfully
✅ WebSocket Manager initialized successfully
✅ Task Manager initialized successfully
✅ Web Server Manager initialized successfully
✅ System Monitor initialized successfully

🎉 SYSTEM INITIALIZATION COMPLETED! 🎉
```

## 🏗️ 系统架构

- **DataManager**: 数据存储和配置管理（使用轻量级缓存）
- **IRController**: 红外信号收发控制
- **WiFiManager**: WiFi连接管理
- **WebSocketManager**: 实时通信管理
- **TaskManager**: 任务队列管理（使用轻量级参数存储）
- **WebServerManager**: HTTP服务器管理
- **SystemMonitor**: 系统状态监控

## 🔧 技术特点

- **零全局内存分配**：所有Manager构造函数都不分配内存
- **按需JSON创建**：JSON对象仅在需要时临时创建
- **轻量级缓存**：使用String和简单结构体替代复杂对象
- **完整API兼容**：保持所有原有接口不变
- **PSRAM优化**：完全兼容ESP32-S3 OPI PSRAM

## 📁 文件结构

```
ESP32_S3_IR_System_Arduino/
├── ESP32_S3_IR_System_Arduino.ino  # 主程序
├── data_manager.h/cpp               # 数据管理器
├── ir_controller.h/cpp              # 红外控制器
├── wifi_manager.h/cpp               # WiFi管理器
├── websocket_manager.h/cpp          # WebSocket管理器
├── task_manager.h/cpp               # 任务管理器
├── web_server_manager.h/cpp         # Web服务器管理器
├── system_monitor.h/cpp             # 系统监控器
├── IRremoteESP8266_Core3_Fix.h      # 兼容性补丁
└── data/                            # Web界面文件
```

## 🎉 成功标志

如果看到以下输出，说明系统运行完全正常：
- PSRAM检测成功并显示8MB大小
- 所有Manager初始化成功
- 系统初始化完成消息
- Web服务器在端口80启动
- WebSocket服务正常运行

## 📞 支持

如有问题，请检查：
1. Arduino IDE板子配置是否正确
2. 必需库是否已安装
3. 串口输出中的错误信息

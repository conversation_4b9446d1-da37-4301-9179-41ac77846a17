#include "SystemManager.h"
#include "../data/DataManager.h"
#include "../hardware/IRController.h"
#include "../network/WebServerManager.h"
#include "../tasks/TaskManager.h"
#include "../network/WSManager.h"
#include "../network/NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
SystemManager::SystemManager(SystemMode mode)
    : m_initialized(false)
    , m_systemMode(mode)
    , m_systemCapacity(SystemCapacity::getCapacity(mode))
    , m_startTime(millis())
    , m_lastError("")
    , m_lastStatsUpdate(0)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_webServerManager(nullptr)
    , m_taskManager(nullptr)
    , m_wsManager(nullptr)
    , m_networkSecurity(nullptr)
{
    Serial.printf("🏗️  SystemManager created in %s mode\n", getSystemModeString());
    Serial.printf("📊 System capacity: %d signals, %d tasks, %d timers\n", 
                 m_systemCapacity.maxSignals, m_systemCapacity.maxTasks, m_systemCapacity.maxTimers);
}

SystemManager::~SystemManager() {
    shutdown();
    cleanupComponents();
    Serial.println("🗑️  SystemManager destroyed");
}

// ==================== 系统初始化 ====================
bool SystemManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  SystemManager already initialized");
        return true;
    }
    
    Serial.println("🚀 Initializing SystemManager...");
    
    // 1. 初始化组件
    if (!initializeComponents()) {
        logError("Component initialization failed");
        return false;
    }
    
    // 2. 设置组件依赖关系
    setupComponentDependencies();
    
    // 3. 执行初始健康检查
    if (!checkComponentHealth()) {
        logError("Component health check failed");
        return false;
    }
    
    m_initialized = true;
    m_lastStatsUpdate = millis();
    
    Serial.println("✅ SystemManager initialization completed");
    return true;
}

// ==================== 组件初始化 ====================
bool SystemManager::initializeComponents() {
    Serial.println("🔧 Initializing system components...");
    
    // 1. 创建数据管理器
    if (!createDataManager()) {
        return false;
    }
    
    // 2. 创建红外控制器
    if (!createIRController()) {
        return false;
    }
    
    // 3. 创建任务管理器
    if (!createTaskManager()) {
        return false;
    }
    
    // 4. 创建WebSocket管理器
    if (!createWSManager()) {
        return false;
    }
    
    // 5. 创建Web服务器管理器（最后创建，因为它依赖其他组件）
    if (!createWebServerManager()) {
        return false;
    }
    
    Serial.println("✅ All components created successfully");
    return true;
}

bool SystemManager::createDataManager() {
    Serial.println("📊 Creating DataManager...");
    
    // 使用智能内存分配器
    m_dataManager = (DataManager*)MemoryAllocator::smartAlloc(sizeof(DataManager));
    if (!m_dataManager) {
        logError("Failed to allocate memory for DataManager");
        return false;
    }
    
    // 使用placement new构造对象
    new(m_dataManager) DataManager(m_systemCapacity);
    
    if (!m_dataManager->initialize()) {
        logError("DataManager initialization failed");
        return false;
    }
    
    Serial.println("✅ DataManager created and initialized");
    return true;
}

bool SystemManager::createIRController() {
    Serial.println("📡 Creating IRController...");
    
    m_irController = (IRController*)MemoryAllocator::smartAlloc(sizeof(IRController));
    if (!m_irController) {
        logError("Failed to allocate memory for IRController");
        return false;
    }
    
    new(m_irController) IRController();
    
    if (!m_irController->initialize()) {
        logError("IRController initialization failed");
        return false;
    }
    
    Serial.println("✅ IRController created and initialized");
    return true;
}

bool SystemManager::createTaskManager() {
    Serial.println("⚙️  Creating TaskManager...");
    
    m_taskManager = (TaskManager*)MemoryAllocator::smartAlloc(sizeof(TaskManager));
    if (!m_taskManager) {
        logError("Failed to allocate memory for TaskManager");
        return false;
    }
    
    new(m_taskManager) TaskManager(m_systemCapacity);
    
    if (!m_taskManager->initialize()) {
        logError("TaskManager initialization failed");
        return false;
    }
    
    Serial.println("✅ TaskManager created and initialized");
    return true;
}

bool SystemManager::createWSManager() {
    Serial.println("🔌 Creating WSManager...");
    
    m_wsManager = (WSManager*)MemoryAllocator::smartAlloc(sizeof(WSManager));
    if (!m_wsManager) {
        logError("Failed to allocate memory for WSManager");
        return false;
    }
    
    new(m_wsManager) WSManager(m_systemCapacity.maxConnections);
    
    if (!m_wsManager->initialize()) {
        logError("WSManager initialization failed");
        return false;
    }
    
    Serial.println("✅ WSManager created and initialized");
    return true;
}

bool SystemManager::createWebServerManager() {
    Serial.println("🌐 Creating WebServerManager...");
    
    m_webServerManager = (WebServerManager*)MemoryAllocator::smartAlloc(sizeof(WebServerManager));
    if (!m_webServerManager) {
        logError("Failed to allocate memory for WebServerManager");
        return false;
    }
    
    new(m_webServerManager) WebServerManager();
    
    if (!m_webServerManager->initialize()) {
        logError("WebServerManager initialization failed");
        return false;
    }
    
    Serial.println("✅ WebServerManager created and initialized");
    return true;
}

// ==================== 组件依赖设置 ====================
void SystemManager::setupComponentDependencies() {
    Serial.println("🔗 Setting up component dependencies...");
    
    if (m_irController && m_dataManager) {
        m_irController->setDataManager(m_dataManager);
    }
    
    if (m_taskManager && m_dataManager && m_irController) {
        m_taskManager->setDataManager(m_dataManager);
        m_taskManager->setIRController(m_irController);
    }
    
    if (m_webServerManager && m_dataManager && m_irController && m_taskManager && m_wsManager) {
        m_webServerManager->setDataManager(m_dataManager);
        m_webServerManager->setIRController(m_irController);
        m_webServerManager->setTaskManager(m_taskManager);
        m_webServerManager->setWSManager(m_wsManager);
    }
    
    Serial.println("✅ Component dependencies configured");
}

// ==================== 主循环处理 ====================
void SystemManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // 定期维护任务
    static unsigned long lastMaintenance = 0;
    if (millis() - lastMaintenance > 30000) {  // 每30秒执行一次维护
        performMaintenance();
        lastMaintenance = millis();
    }
    
    // 更新统计信息
    if (millis() - m_lastStatsUpdate > STATS_UPDATE_INTERVAL) {
        updateSystemStats();
        m_lastStatsUpdate = millis();
    }
    
    // 让各组件处理自己的循环任务
    if (m_taskManager) {
        m_taskManager->handleLoop();
    }
    
    if (m_webServerManager) {
        m_webServerManager->handleLoop();
    }
    
    if (m_wsManager) {
        m_wsManager->handleLoop();
    }
}

// ==================== 系统状态管理 ====================
const char* SystemManager::getSystemModeString() const {
    return PSRAMManager::getSystemModeString(m_systemMode);
}

unsigned long SystemManager::getUptime() const {
    return millis() - m_startTime;
}

// ==================== 内存管理 ====================
void SystemManager::printMemoryStatus() {
    Serial.println("📊 System Memory Status:");
    Serial.printf("   System Mode: %s\n", getSystemModeString());
    Serial.printf("   Free Heap: %d bytes (%.2f KB)\n", 
                 ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    
    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("   Free PSRAM: %d bytes (%.2f KB)\n", 
                     PSRAMManager::getFreePSRAM(), PSRAMManager::getFreePSRAM() / 1024.0);
        Serial.printf("   PSRAM Usage: %.1f%%\n", PSRAMManager::getPSRAMUsage() * 100);
    }
    
    Serial.printf("   Memory Allocations: %d\n", MemoryAllocator::getTotalAllocations());
    Serial.printf("   Memory Deallocations: %d\n", MemoryAllocator::getTotalDeallocations());
    Serial.printf("   Current Allocated: %d bytes\n", MemoryAllocator::getCurrentAllocatedSize());
    Serial.printf("   Peak Usage: %d bytes\n", MemoryAllocator::getPeakMemoryUsage());
}

void SystemManager::printDetailedMemoryStatus() {
    printMemoryStatus();
    MemoryAllocator::printMemoryStats();
    PSRAMManager::printPSRAMStatus();
}

size_t SystemManager::getFreePSRAM() {
    return PSRAMManager::getFreePSRAM();
}

size_t SystemManager::getFreeHeap() {
    return ESP.getFreeHeap();
}

bool SystemManager::checkMemoryHealth() {
    // 检查堆内存
    if (ESP.getFreeHeap() < MIN_FREE_HEAP) {
        logError("Low heap memory");
        return false;
    }
    
    // 检查PSRAM（如果可用）
    if (PSRAMManager::isPSRAMAvailable()) {
        if (PSRAMManager::getFreePSRAM() < MIN_FREE_PSRAM) {
            logError("Low PSRAM memory");
            return false;
        }
    }
    
    // 检查内存分配器状态
    if (!MemoryAllocator::checkMemoryLeaks()) {
        logError("Memory leaks detected");
        return false;
    }
    
    return true;
}

// ==================== 错误处理 ====================
void SystemManager::logError(const String& error) {
    m_lastError = error;
    logMessage("ERROR: " + error, LOG_LEVEL_ERROR);
}

void SystemManager::clearError() {
    m_lastError = "";
}

void SystemManager::handleCriticalError(const String& error, bool shouldRestart) {
    logError("CRITICAL: " + error);
    
    if (shouldRestart) {
        Serial.println("🔄 System will restart in 10 seconds due to critical error...");
        delay(10000);
        ESP.restart();
    }
}

// ==================== 系统监控 ====================
DynamicJsonDocument SystemManager::getSystemStats() {
    DynamicJsonDocument stats(1024);
    
    stats["uptime"] = getUptime();
    stats["free_heap"] = ESP.getFreeHeap();
    stats["free_psram"] = PSRAMManager::getFreePSRAM();
    stats["system_mode"] = getSystemModeString();
    stats["psram_available"] = PSRAMManager::isPSRAMAvailable();
    stats["memory_allocations"] = MemoryAllocator::getTotalAllocations();
    stats["memory_deallocations"] = MemoryAllocator::getTotalDeallocations();
    stats["peak_memory_usage"] = MemoryAllocator::getPeakMemoryUsage();
    
    if (m_dataManager) {
        stats["signal_count"] = m_dataManager->getSignalCount();
        stats["task_count"] = m_dataManager->getTaskCount();
        stats["timer_count"] = m_dataManager->getTimerCount();
    }
    
    return stats;
}

DynamicJsonDocument SystemManager::getSystemStatus() {
    DynamicJsonDocument status(512);
    
    status["initialized"] = m_initialized;
    status["system_mode"] = getSystemModeString();
    status["uptime"] = getUptime();
    status["last_error"] = m_lastError;
    status["memory_healthy"] = checkMemoryHealth();
    status["components_healthy"] = checkComponentHealth();
    
    return status;
}

void SystemManager::updateSystemStats() {
    // 这里可以添加定期统计更新逻辑
    // 例如：更新性能计数器、清理过期数据等
}

// ==================== 私有方法 ====================
void SystemManager::cleanupComponents() {
    if (m_webServerManager) {
        m_webServerManager->~WebServerManager();
        MemoryAllocator::smartFree(m_webServerManager);
        m_webServerManager = nullptr;
    }

    if (m_wsManager) {
        m_wsManager->~WSManager();
        MemoryAllocator::smartFree(m_wsManager);
        m_wsManager = nullptr;
    }

    if (m_networkSecurity) {
        m_networkSecurity->~NetworkSecurity();
        MemoryAllocator::smartFree(m_networkSecurity);
        m_networkSecurity = nullptr;
    }

    if (m_taskManager) {
        m_taskManager->~TaskManager();
        MemoryAllocator::smartFree(m_taskManager);
        m_taskManager = nullptr;
    }

    if (m_irController) {
        m_irController->~IRController();
        MemoryAllocator::smartFree(m_irController);
        m_irController = nullptr;
    }

    if (m_dataManager) {
        m_dataManager->~DataManager();
        MemoryAllocator::smartFree(m_dataManager);
        m_dataManager = nullptr;
    }
}

bool SystemManager::checkComponentHealth() {
    // 检查各组件是否正常工作
    if (m_dataManager && !m_dataManager->isHealthy()) {
        logError("DataManager health check failed");
        return false;
    }

    if (m_irController && !m_irController->isHealthy()) {
        logError("IRController health check failed");
        return false;
    }

    if (m_taskManager && !m_taskManager->isHealthy()) {
        logError("TaskManager health check failed");
        return false;
    }

    if (m_webServerManager && !m_webServerManager->isHealthy()) {
        logError("WebServerManager health check failed");
        return false;
    }

    if (m_wsManager && !m_wsManager->isHealthy()) {
        logError("WSManager health check failed");
        return false;
    }

    // 网络安全管理器没有健康检查方法，假设总是健康的

    return true;
}

void SystemManager::performMaintenance() {
    // 执行定期维护任务
    if (!checkMemoryHealth()) {
        MemoryAllocator::performCleanup();
    }
    
    // 检查组件健康状态
    if (!checkComponentHealth()) {
        logError("Component health check failed during maintenance");
    }
}

void SystemManager::logMessage(const String& message, int level) {
    const char* levelStr = "";
    switch (level) {
        case LOG_LEVEL_DEBUG: levelStr = "DEBUG"; break;
        case LOG_LEVEL_INFO: levelStr = "INFO"; break;
        case LOG_LEVEL_WARNING: levelStr = "WARN"; break;
        case LOG_LEVEL_ERROR: levelStr = "ERROR"; break;
        default: levelStr = "UNKNOWN"; break;
    }
    
    Serial.printf("[%s] %s\n", levelStr, message.c_str());
}

// ==================== 系统控制 ====================
void SystemManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔄 Shutting down SystemManager...");
    
    // 按相反顺序关闭组件
    if (m_webServerManager) {
        m_webServerManager->shutdown();
    }

    if (m_wsManager) {
        m_wsManager->shutdown();
    }

    if (m_networkSecurity) {
        m_networkSecurity->shutdown();
    }

    if (m_taskManager) {
        m_taskManager->shutdown();
    }

    if (m_irController) {
        m_irController->shutdown();
    }

    if (m_dataManager) {
        m_dataManager->shutdown();
    }
    
    m_initialized = false;
    Serial.println("✅ SystemManager shutdown completed");
}

void SystemManager::factoryReset() {
    Serial.println("🏭 Performing factory reset...");
    
    if (m_dataManager) {
        m_dataManager->factoryReset();
    }
    
    // 清除配置文件
    if (SPIFFS.exists(CONFIG_FILE_PATH)) {
        SPIFFS.remove(CONFIG_FILE_PATH);
    }
    
    Serial.println("✅ Factory reset completed");
}

// ==================== 新增系统集成方法 ====================
bool SystemManager::initializePSRAM() {
    Serial.println("🧠 Initializing PSRAM and memory management...");

    // 检测系统模式
    m_systemMode = PSRAMManager::detectSystemMode();
    Serial.printf("🧠 System mode detected: %s\n", PSRAMManager::getSystemModeString(m_systemMode));

    // 初始化PSRAM（如果可用）
    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("🧠 PSRAM available: %d bytes (%.2f MB)\n",
                     PSRAMManager::getPSRAMSize(), PSRAMManager::getPSRAMSize() / 1024.0 / 1024.0);
    } else {
        Serial.println("⚠️  PSRAM not available, using heap memory only");
    }

    // 初始化内存分配器
    if (!MemoryAllocator::initialize()) {
        logError("Memory allocator initialization failed");
        return false;
    }

    Serial.println("✅ PSRAM and memory management initialized");
    return true;
}

bool SystemManager::createComponents() {
    Serial.println("🔧 Creating system components...");

    // 创建网络安全管理器
    if (!createNetworkSecurity()) {
        return false;
    }

    // 创建其他组件（已有的方法）
    if (!createDataManager()) {
        return false;
    }

    if (!createIRController()) {
        return false;
    }

    if (!createTaskManager()) {
        return false;
    }

    if (!createWSManager()) {
        return false;
    }

    if (!createWebServerManager()) {
        return false;
    }

    Serial.println("✅ All components created successfully");
    return true;
}

bool SystemManager::createNetworkSecurity() {
    Serial.println("🔒 Creating NetworkSecurity...");

    m_networkSecurity = (NetworkSecurity*)MemoryAllocator::smartAlloc(sizeof(NetworkSecurity));
    if (!m_networkSecurity) {
        logError("Failed to allocate memory for NetworkSecurity");
        return false;
    }

    new(m_networkSecurity) NetworkSecurity();

    if (!m_networkSecurity->initialize()) {
        logError("NetworkSecurity initialization failed");
        return false;
    }

    Serial.println("✅ NetworkSecurity created and initialized");
    return true;
}

bool SystemManager::setupDependencies() {
    Serial.println("🔗 Setting up comprehensive component dependencies...");

    // 设置基本依赖关系
    setupComponentDependencies();

    // 设置网络安全依赖
    if (m_webServerManager && m_networkSecurity) {
        m_webServerManager->setNetworkSecurity(m_networkSecurity);
    }

    Serial.println("✅ All component dependencies configured");
    return true;
}

bool SystemManager::startServices() {
    Serial.println("🚀 Starting system services...");

    // 启动网络服务
    if (m_webServerManager) {
        if (!m_webServerManager->startServer()) {
            logError("Failed to start web server");
            return false;
        }
    }

    Serial.println("✅ All services started successfully");
    return true;
}

bool SystemManager::performSystemSelfTest() {
    Serial.println("🔍 Performing system self-test...");

    bool allTestsPassed = true;

    // 1. 内存测试
    if (!checkMemoryHealth()) {
        Serial.println("❌ Memory health test failed");
        allTestsPassed = false;
    } else {
        Serial.println("✅ Memory health test passed");
    }

    // 2. 组件健康测试
    if (!checkComponentHealth()) {
        Serial.println("❌ Component health test failed");
        allTestsPassed = false;
    } else {
        Serial.println("✅ Component health test passed");
    }

    // 3. 硬件测试
    if (m_irController && !m_irController->testHardware()) {
        Serial.println("❌ Hardware test failed");
        allTestsPassed = false;
    } else {
        Serial.println("✅ Hardware test passed");
    }

    // 4. 数据完整性测试
    if (m_dataManager) {
        DynamicJsonDocument integrityReport = m_dataManager->getDataIntegrityReport();
        if (!integrityReport["overall_integrity"]) {
            Serial.println("❌ Data integrity test failed");
            allTestsPassed = false;
        } else {
            Serial.println("✅ Data integrity test passed");
        }
    }

    if (allTestsPassed) {
        Serial.println("✅ System self-test completed successfully");
    } else {
        Serial.println("⚠️  System self-test completed with warnings");
    }

    return allTestsPassed;
}

void SystemManager::printSystemInfo() {
    Serial.println("📋 System Information:");
    Serial.printf("   Firmware Version: %s\n", FIRMWARE_VERSION);
    Serial.printf("   System Mode: %s\n", getSystemModeString());
    Serial.printf("   Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("   Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("   CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("   Flash Size: %d bytes\n", ESP.getFlashChipSize());
    Serial.printf("   Free Heap: %d bytes\n", ESP.getFreeHeap());

    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("   PSRAM Size: %d bytes\n", PSRAMManager::getPSRAMSize());
        Serial.printf("   Free PSRAM: %d bytes\n", PSRAMManager::getFreePSRAM());
    }

    Serial.printf("   Uptime: %lu ms\n", getUptime());
    Serial.println("📋 System ready for operation");
}

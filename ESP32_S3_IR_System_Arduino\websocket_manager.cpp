/*
 * WebSocket Manager Implementation - ESP32-S3 IR Control System
 * Fully compatible with ESPAsyncWebServer 3.7.8 API
 * Handles real-time communication between frontend and backend
 */

#include "websocket_manager.h"

WebSocketManager::WebSocketManager() {
    ws = nullptr;
    totalConnections = 0;
    totalMessages = 0;
    totalErrors = 0;
    
    // Clear callbacks
    onClientConnected = nullptr;
    onClientDisconnected = nullptr;
    onTextMessage = nullptr;
    onJsonMessage = nullptr;
}

WebSocketManager::~WebSocketManager() {
    disconnectAllClients();
}

bool WebSocketManager::initialize(AsyncWebSocket* webSocket) {
    if (!webSocket) {
        Serial.println("ERROR: WebSocket pointer is null");
        return false;
    }
    
    ws = webSocket;
    
    // Set up WebSocket event handler - ESPAsyncWebServer 3.7.8 compatible
    ws->onEvent([this](AsyncWebSocket *server, AsyncWebSocketClient *client, 
                       AwsEventType type, void *arg, uint8_t *data, size_t len) {
        this->handleWebSocketEvent(server, client, type, arg, data, len);
    });
    
    Serial.println("WebSocket Manager initialized successfully");
    return true;
}

void WebSocketManager::handleLoop() {
    // Clean up inactive clients
    cleanupInactiveClients();
    
    // Process message queue
    if (!messageQueue.empty()) {
        QueuedMessage msg = messageQueue.front();
        messageQueue.erase(messageQueue.begin());
        
        if (msg.targetClientId == 0) {
            // Broadcast message
            ws->textAll(msg.payload);
        } else {
            // Send to specific client
            AsyncWebSocketClient* client = ws->client(msg.targetClientId);
            if (client && client->status() == WS_CONNECTED) {
                client->text(msg.payload);
            }
        }
        
        totalMessages++;
    }
}

bool WebSocketManager::broadcastMessage(const String& type, const DynamicJsonDocument& payload) {
    if (!ws) {
        Serial.println("ERROR: WebSocket not initialized");
        return false;
    }
    
    String message = createWebSocketMessage(type, payload);
    
    if (message.isEmpty()) {
        Serial.println("ERROR: Failed to create WebSocket message");
        return false;
    }
    
    ws->textAll(message);
    totalMessages++;
    
    Serial.printf("Broadcasted message: %s\n", type.c_str());
    return true;
}

bool WebSocketManager::sendToClient(uint32_t clientId, const String& type, const DynamicJsonDocument& payload) {
    if (!ws) {
        Serial.println("ERROR: WebSocket not initialized");
        return false;
    }
    
    AsyncWebSocketClient* client = ws->client(clientId);
    if (!client || client->status() != WS_CONNECTED) {
        Serial.printf("ERROR: Client %u not connected\n", clientId);
        return false;
    }
    
    String message = createWebSocketMessage(type, payload);
    
    if (message.isEmpty()) {
        Serial.println("ERROR: Failed to create WebSocket message");
        return false;
    }
    
    client->text(message);
    totalMessages++;
    
    Serial.printf("Sent message to client %u: %s\n", clientId, type.c_str());
    return true;
}

bool WebSocketManager::queueMessage(const String& type, const DynamicJsonDocument& payload, uint32_t clientId) {
    if (messageQueue.size() >= MAX_QUEUE_SIZE) {
        Serial.println("WARNING: Message queue full, dropping oldest message");
        messageQueue.erase(messageQueue.begin());
    }
    
    QueuedMessage msg;
    msg.type = type;
    msg.payload = createWebSocketMessage(type, payload);
    msg.targetClientId = clientId;
    msg.timestamp = millis();
    
    messageQueue.push_back(msg);
    return true;
}

int WebSocketManager::getClientCount() const {
    return clients.size();
}

std::vector<WSClient> WebSocketManager::getActiveClients() const {
    std::vector<WSClient> activeClients;
    
    for (const auto& client : clients) {
        if (client.isActive) {
            activeClients.push_back(client);
        }
    }
    
    return activeClients;
}

bool WebSocketManager::isClientConnected(uint32_t clientId) const {
    WSClient* client = const_cast<WebSocketManager*>(this)->findClient(clientId);
    return client && client->isActive;
}

void WebSocketManager::disconnectClient(uint32_t clientId) {
    AsyncWebSocketClient* client = ws->client(clientId);
    if (client) {
        client->close();
    }
    
    removeClient(clientId);
}

void WebSocketManager::disconnectAllClients() {
    if (ws) {
        ws->closeAll();
    }
    
    clients.clear();
    messageQueue.clear();
}

void WebSocketManager::pingAllClients() {
    if (!ws) return;
    
    for (auto& client : clients) {
        if (client.isActive) {
            pingClient(client.id);
        }
    }
}

void WebSocketManager::pingClient(uint32_t clientId) {
    AsyncWebSocketClient* client = ws->client(clientId);
    if (client && client->status() == WS_CONNECTED) {
        client->ping();
        
        WSClient* wsClient = findClient(clientId);
        if (wsClient) {
            wsClient->lastPing = millis();
        }
    }
}

DynamicJsonDocument WebSocketManager::getStatistics() const {
    DynamicJsonDocument stats(512);
    
    stats["total_connections"] = totalConnections;
    stats["total_messages"] = totalMessages;
    stats["total_errors"] = totalErrors;
    stats["active_clients"] = getClientCount();
    stats["queue_size"] = messageQueue.size();
    stats["max_clients"] = MAX_CLIENTS;
    stats["max_queue_size"] = MAX_QUEUE_SIZE;
    
    return stats;
}

void WebSocketManager::sendSystemStatus() {
    DynamicJsonDocument status(512);
    status["type"] = "system_status";
    status["uptime"] = millis();
    status["free_heap"] = ESP.getFreeHeap();
    status["clients"] = getClientCount();
    status["timestamp"] = millis();
    
    broadcastMessage("system_status", status);
}

void WebSocketManager::sendClientList() {
    DynamicJsonDocument clientList(1024);
    JsonArray clientsArray = clientList.createNestedArray("clients");
    
    for (const auto& client : clients) {
        if (client.isActive) {
            DynamicJsonDocument clientInfo(128);
            clientInfo["id"] = client.id;
            clientInfo["ip"] = client.ip;
            clientInfo["connected_at"] = client.connectedAt;
            clientInfo["last_ping"] = client.lastPing;
            
            clientsArray.add(clientInfo);
        }
    }
    
    clientList["count"] = clientsArray.size();
    broadcastMessage("client_list", clientList);
}

void WebSocketManager::sendErrorMessage(uint32_t clientId, const String& error) {
    DynamicJsonDocument errorMsg(256);
    errorMsg["error"] = error;
    errorMsg["timestamp"] = millis();
    
    sendToClient(clientId, "error", errorMsg);
}

void WebSocketManager::sendNotification(const String& message, const String& level) {
    DynamicJsonDocument notification(256);
    notification["message"] = message;
    notification["level"] = level;
    notification["timestamp"] = millis();
    
    broadcastMessage("notification", notification);
}

void WebSocketManager::setOnClientConnected(std::function<void(uint32_t, String)> callback) {
    onClientConnected = callback;
}

void WebSocketManager::setOnClientDisconnected(std::function<void(uint32_t)> callback) {
    onClientDisconnected = callback;
}

void WebSocketManager::setOnTextMessage(std::function<void(uint32_t, String)> callback) {
    onTextMessage = callback;
}

void WebSocketManager::setOnJsonMessage(std::function<void(uint32_t, DynamicJsonDocument)> callback) {
    onJsonMessage = callback;
}

// ==================== Private Methods ====================

void WebSocketManager::handleWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, 
                                           AwsEventType type, void *arg, uint8_t *data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            onClientConnect(client);
            break;
            
        case WS_EVT_DISCONNECT:
            onClientDisconnect(client);
            break;
            
        case WS_EVT_DATA:
            onClientMessage(client, data, len);
            break;
            
        case WS_EVT_PONG:
            // Handle pong response
            {
                WSClient* wsClient = findClient(client->id());
                if (wsClient) {
                    wsClient->lastPing = millis();
                }
            }
            break;
            
        case WS_EVT_ERROR:
            onClientError(client, "WebSocket error occurred");
            break;
    }
}

void WebSocketManager::onClientConnect(AsyncWebSocketClient* client) {
    if (clients.size() >= MAX_CLIENTS) {
        Serial.println("WARNING: Maximum clients reached, rejecting connection");
        client->close();
        return;
    }
    
    String clientIP = client->remoteIP().toString();
    uint32_t clientId = client->id();
    
    if (addClient(clientId, clientIP)) {
        totalConnections++;
        
        Serial.printf("WebSocket client connected: ID=%u, IP=%s\n", clientId, clientIP.c_str());
        
        // Send welcome message
        DynamicJsonDocument welcome(256);
        welcome["message"] = "Connected to ESP32-S3 IR Control System";
        welcome["client_id"] = clientId;
        welcome["timestamp"] = millis();
        
        sendToClient(clientId, "welcome", welcome);
        
        // Trigger callback
        if (onClientConnected) {
            onClientConnected(clientId, clientIP);
        }
        
        // Broadcast client list update
        sendClientList();
    }
}

void WebSocketManager::onClientDisconnect(AsyncWebSocketClient* client) {
    uint32_t clientId = client->id();
    
    Serial.printf("WebSocket client disconnected: ID=%u\n", clientId);
    
    removeClient(clientId);
    
    // Trigger callback
    if (onClientDisconnected) {
        onClientDisconnected(clientId);
    }
    
    // Broadcast client list update
    sendClientList();
}

void WebSocketManager::onClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len) {
    uint32_t clientId = client->id();
    
    // Convert data to string
    String message = "";
    for (size_t i = 0; i < len; i++) {
        message += (char)data[i];
    }
    
    Serial.printf("WebSocket message from client %u: %s\n", clientId, message.c_str());
    
    // Try to parse as JSON
    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, message);
    
    if (!error) {
        // Valid JSON message
        if (validateMessage(doc)) {
            processIncomingMessage(client, doc);
            
            if (onJsonMessage) {
                onJsonMessage(clientId, doc);
            }
        } else {
            sendErrorMessage(clientId, "Invalid message format");
        }
    } else {
        // Plain text message
        if (onTextMessage) {
            onTextMessage(clientId, message);
        }
    }
}

void WebSocketManager::onClientError(AsyncWebSocketClient* client, const String& error) {
    uint32_t clientId = client->id();
    
    Serial.printf("WebSocket client error: ID=%u, Error=%s\n", clientId, error.c_str());
    
    totalErrors++;
    
    // Remove problematic client
    removeClient(clientId);
}

bool WebSocketManager::addClient(uint32_t id, const String& ip) {
    // Check if client already exists
    if (findClient(id)) {
        return false;
    }

    WSClient newClient;
    newClient.id = id;
    newClient.ip = ip;
    newClient.connectedAt = millis();
    newClient.lastPing = millis();
    newClient.isActive = true;

    clients.push_back(newClient);
    return true;
}

bool WebSocketManager::removeClient(uint32_t id) {
    for (auto it = clients.begin(); it != clients.end(); ++it) {
        if (it->id == id) {
            clients.erase(it);
            return true;
        }
    }
    return false;
}

WSClient* WebSocketManager::findClient(uint32_t id) {
    for (auto& client : clients) {
        if (client.id == id) {
            return &client;
        }
    }
    return nullptr;
}

void WebSocketManager::cleanupInactiveClients() {
    unsigned long currentTime = millis();

    for (auto it = clients.begin(); it != clients.end();) {
        if (currentTime - it->lastPing > CLIENT_TIMEOUT) {
            Serial.printf("Removing inactive client: %u\n", it->id);
            it = clients.erase(it);
        } else {
            ++it;
        }
    }
}

String WebSocketManager::createWebSocketMessage(const String& type, const DynamicJsonDocument& payload) {
    DynamicJsonDocument message(payload.memoryUsage() + 128);

    message["type"] = type;
    message["timestamp"] = millis();
    message["payload"] = payload;  // 前端期望的是 "payload" 而不是 "data"

    String output;
    serializeJson(message, output);

    return output;
}

bool WebSocketManager::validateMessage(const DynamicJsonDocument& message) {
    // Check if message has required fields
    if (!message.containsKey("type")) {
        return false;
    }

    String type = message["type"];
    if (type.isEmpty()) {
        return false;
    }

    return true;
}

void WebSocketManager::processIncomingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    String type = message["type"];
    uint32_t clientId = client->id();

    if (type == "ping") {
        handlePingMessage(client, message);
    } else if (type == "subscribe") {
        handleSubscribeMessage(client, message);
    } else if (type == "unsubscribe") {
        handleUnsubscribeMessage(client, message);
    } else {
        Serial.printf("Unknown message type: %s\n", type.c_str());
    }
}

void WebSocketManager::handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload) {
    DynamicJsonDocument pong(128);
    pong["message"] = "pong";
    pong["timestamp"] = millis();

    sendToClient(client->id(), "pong", pong);
}

void WebSocketManager::handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload) {
    // Handle subscription requests
    DynamicJsonDocument response(128);
    response["message"] = "Subscription confirmed";
    response["timestamp"] = millis();

    sendToClient(client->id(), "subscribe_response", response);
}

void WebSocketManager::handleUnsubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& payload) {
    // Handle unsubscription requests
    DynamicJsonDocument response(128);
    response["message"] = "Unsubscription confirmed";
    response["timestamp"] = millis();

    sendToClient(client->id(), "unsubscribe_response", response);
}

#include "SystemManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "WebServerManager.h"
#include "TaskManager.h"
#include "WSManager.h"
#include "NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
SystemManager::SystemManager(const SystemCapacity& capacity)
    : m_capacity(capacity)
    , m_initialized(false)
    , m_started(false)
    , m_debugMode(false)
    , m_lastError("")
    , m_startTime(0)
    , m_lastStatusUpdate(0)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_taskManager(nullptr)
    , m_webServerManager(nullptr)
    , m_wsManager(nullptr)
    , m_networkSecurity(nullptr)
    , m_wifiConnected(false)
    , m_fileSystemReady(false)
    , m_componentsHealthy(false)
{
    Serial.printf("🏗️ SystemManager created with capacity: %d signals, %d tasks, %d timers\n", 
                 m_capacity.maxSignals, m_capacity.maxTasks, m_capacity.maxTimers);
}

SystemManager::~SystemManager() {
    shutdown();
    cleanupComponents();
    Serial.println("🗑️ SystemManager destroyed");
}

// ==================== 系统生命周期 ====================
bool SystemManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️ SystemManager already initialized");
        return true;
    }
    
    Serial.println("🚀 Initializing SystemManager...");
    
    // 1. 初始化文件系统
    if (!initializeFileSystem()) {
        handleSystemError("File system initialization failed", true);
        return false;
    }
    
    // 2. 初始化WiFi
    if (!initializeWiFi()) {
        handleSystemError("WiFi initialization failed", false);
        // WiFi失败不是致命错误，继续初始化
    }
    
    // 3. 初始化组件
    if (!initializeComponents()) {
        handleSystemError("Component initialization failed", true);
        return false;
    }
    
    // 4. 设置组件依赖关系
    setupComponentDependencies();
    
    m_initialized = true;
    m_startTime = millis();
    
    Serial.println("✅ SystemManager initialization completed");
    return true;
}

bool SystemManager::start() {
    if (!m_initialized) {
        handleSystemError("System not initialized", true);
        return false;
    }
    
    if (m_started) {
        Serial.println("⚠️ System already started");
        return true;
    }
    
    Serial.println("🚀 Starting system services...");
    
    if (!startServices()) {
        handleSystemError("Service startup failed", true);
        return false;
    }
    
    m_started = true;
    Serial.println("✅ System started successfully");
    
    // 广播系统启动消息
    broadcastSystemStatus();
    
    return true;
}

void SystemManager::stop() {
    if (!m_started) {
        return;
    }
    
    Serial.println("🛑 Stopping system services...");
    
    stopServices();
    m_started = false;
    
    Serial.println("✅ System stopped");
}

void SystemManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔄 Shutting down SystemManager...");
    
    stop();
    
    // 关闭所有组件
    if (m_wsManager) {
        m_wsManager->shutdown();
    }
    if (m_webServerManager) {
        m_webServerManager->shutdown();
    }
    if (m_taskManager) {
        m_taskManager->shutdown();
    }
    if (m_irController) {
        m_irController->shutdown();
    }
    if (m_dataManager) {
        m_dataManager->shutdown();
    }
    if (m_networkSecurity) {
        m_networkSecurity->shutdown();
    }
    
    disconnectWiFi();
    
    m_initialized = false;
    Serial.println("✅ SystemManager shutdown completed");
}

bool SystemManager::restart() {
    Serial.println("🔄 Restarting system...");
    
    stop();
    delay(1000);
    
    return start();
}

bool SystemManager::isHealthy() const {
    if (!m_initialized || !m_started) {
        return false;
    }
    
    return m_componentsHealthy && checkComponentsHealth();
}

void SystemManager::emergencyStop() {
    Serial.println("🚨 EMERGENCY STOP ACTIVATED");
    
    // 立即停止所有操作
    if (m_taskManager) {
        m_taskManager->stopExecution();
    }
    if (m_irController) {
        m_irController->stopSending();
        m_irController->stopLearning();
    }
    
    // 广播紧急停止消息
    DynamicJsonDocument emergency(256);
    emergency["type"] = "emergency_stop";
    emergency["timestamp"] = millis();
    emergency["message"] = "System emergency stop activated";
    
    broadcastMessage(emergency);
    
    stop();
}

// ==================== 主循环 ====================
void SystemManager::loop() {
    if (!m_initialized || !m_started) {
        return;
    }
    
    // 更新系统状态
    updateSystemStatus();
    
    // 处理系统事件
    processSystemEvents();
    
    // 监控系统资源
    monitorSystemResources();
    
    // 让各组件执行循环处理
    if (m_taskManager) {
        m_taskManager->loop();
    }
}

void SystemManager::updateSystemStatus() {
    unsigned long currentTime = millis();
    
    // 每30秒更新一次状态
    if (currentTime - m_lastStatusUpdate < 30000) {
        return;
    }
    
    m_lastStatusUpdate = currentTime;
    
    // 检查组件健康状态
    m_componentsHealthy = checkComponentsHealth();
    
    // 检查WiFi连接
    m_wifiConnected = (WiFi.status() == WL_CONNECTED);
    
    // 检查文件系统
    m_fileSystemReady = checkFileSystemHealth();
    
    // 广播状态更新
    broadcastSystemStatus();
}

void SystemManager::processSystemEvents() {
    // 处理系统级事件（占位符实现）
    // 实际实现中可以处理按键事件、定时器事件等
}

// ==================== 网络管理 ====================
bool SystemManager::initializeWiFi() {
    Serial.println("📶 Initializing WiFi...");
    
    WiFi.mode(WIFI_STA);
    
    // 尝试连接到默认WiFi（如果配置了）
    // 这里使用硬编码的WiFi信息，实际应该从配置文件读取
    String defaultSSID = "YourWiFiSSID";
    String defaultPassword = "YourWiFiPassword";
    
    if (!defaultSSID.isEmpty()) {
        return connectToWiFi(defaultSSID, defaultPassword);
    }
    
    Serial.println("⚠️ No default WiFi configured");
    return false;
}

bool SystemManager::connectToWiFi(const String& ssid, const String& password) {
    Serial.printf("📶 Connecting to WiFi: %s\n", ssid.c_str());
    
    WiFi.begin(ssid.c_str(), password.c_str());
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        m_wifiConnected = true;
        Serial.printf("\n✅ WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
        return true;
    } else {
        m_wifiConnected = false;
        Serial.println("\n❌ WiFi connection failed");
        return false;
    }
}

void SystemManager::disconnectWiFi() {
    if (m_wifiConnected) {
        WiFi.disconnect();
        m_wifiConnected = false;
        Serial.println("📶 WiFi disconnected");
    }
}

bool SystemManager::isWiFiConnected() const {
    return m_wifiConnected && (WiFi.status() == WL_CONNECTED);
}

DynamicJsonDocument SystemManager::getWiFiStatus() const {
    DynamicJsonDocument status(256);
    
    status["connected"] = isWiFiConnected();
    status["ssid"] = WiFi.SSID();
    status["ip"] = WiFi.localIP().toString();
    status["rssi"] = WiFi.RSSI();
    status["mac"] = WiFi.macAddress();
    
    return status;
}

// ==================== 文件系统管理 ====================
bool SystemManager::initializeFileSystem() {
    Serial.println("📁 Initializing file system...");
    
    if (!SPIFFS.begin(true)) {
        Serial.println("❌ SPIFFS initialization failed");
        return false;
    }
    
    m_fileSystemReady = true;
    
    // 打印文件系统信息
    size_t totalBytes = SPIFFS.totalBytes();
    size_t usedBytes = SPIFFS.usedBytes();
    
    Serial.printf("✅ SPIFFS initialized: %d KB total, %d KB used\n", 
                 totalBytes / 1024, usedBytes / 1024);
    
    return true;
}

bool SystemManager::checkFileSystemHealth() {
    return SPIFFS.totalBytes() > 0;
}

DynamicJsonDocument SystemManager::getFileSystemInfo() const {
    DynamicJsonDocument info(256);
    
    info["ready"] = m_fileSystemReady;
    info["total_bytes"] = SPIFFS.totalBytes();
    info["used_bytes"] = SPIFFS.usedBytes();
    info["free_bytes"] = SPIFFS.totalBytes() - SPIFFS.usedBytes();
    
    return info;
}

// ==================== 系统状态 ====================
DynamicJsonDocument SystemManager::getSystemStatus() const {
    DynamicJsonDocument status(1024);

    status["initialized"] = m_initialized;
    status["started"] = m_started;
    status["healthy"] = isHealthy();
    status["uptime"] = millis() - m_startTime;
    status["free_heap"] = ESP.getFreeHeap();
    status["wifi_connected"] = m_wifiConnected;
    status["filesystem_ready"] = m_fileSystemReady;
    status["components_healthy"] = m_componentsHealthy;

    // PSRAM信息
    if (PSRAMManager::isPSRAMAvailable()) {
        status["psram_available"] = true;
        status["free_psram"] = PSRAMManager::getFreePSRAM();
        status["psram_size"] = PSRAMManager::getPSRAMSize();
    } else {
        status["psram_available"] = false;
    }

    // 组件状态
    JsonObject components = status.createNestedObject("components");
    components["data_manager"] = (m_dataManager && m_dataManager->isHealthy());
    components["ir_controller"] = (m_irController && m_irController->isHealthy());
    components["task_manager"] = (m_taskManager && m_taskManager->isHealthy());
    components["web_server"] = (m_webServerManager && m_webServerManager->isHealthy());
    components["websocket"] = (m_wsManager && m_wsManager->isHealthy());
    components["network_security"] = (m_networkSecurity && m_networkSecurity->isHealthy());

    return status;
}

DynamicJsonDocument SystemManager::getSystemStatistics() const {
    DynamicJsonDocument stats(512);

    stats["uptime"] = millis() - m_startTime;
    stats["total_heap"] = ESP.getHeapSize();
    stats["free_heap"] = ESP.getFreeHeap();
    stats["min_free_heap"] = ESP.getMinFreeHeap();
    stats["max_alloc_heap"] = ESP.getMaxAllocHeap();

    if (PSRAMManager::isPSRAMAvailable()) {
        stats["psram_size"] = PSRAMManager::getPSRAMSize();
        stats["free_psram"] = PSRAMManager::getFreePSRAM();
        stats["psram_usage"] = PSRAMManager::getPSRAMUsage();
    }

    // 组件统计
    if (m_dataManager) {
        stats["signal_count"] = m_dataManager->getSignalCount();
        stats["task_count"] = m_dataManager->getTaskCount();
        stats["timer_count"] = m_dataManager->getTimerCount();
    }

    if (m_taskManager) {
        DynamicJsonDocument taskStats = m_taskManager->getStatistics();
        stats["task_statistics"] = taskStats;
    }

    return stats;
}

DynamicJsonDocument SystemManager::getMemoryUsage() const {
    DynamicJsonDocument memory(512);

    memory["heap_total"] = ESP.getHeapSize();
    memory["heap_free"] = ESP.getFreeHeap();
    memory["heap_used"] = ESP.getHeapSize() - ESP.getFreeHeap();
    memory["heap_usage_percent"] = (float)(ESP.getHeapSize() - ESP.getFreeHeap()) / ESP.getHeapSize() * 100;

    if (PSRAMManager::isPSRAMAvailable()) {
        memory["psram_total"] = PSRAMManager::getPSRAMSize();
        memory["psram_free"] = PSRAMManager::getFreePSRAM();
        memory["psram_used"] = PSRAMManager::getPSRAMSize() - PSRAMManager::getFreePSRAM();
        memory["psram_usage_percent"] = PSRAMManager::getPSRAMUsage() * 100;
    }

    // 内存分配器统计
    memory["allocator_total"] = MemoryAllocator::getCurrentAllocatedSize();
    memory["allocator_peak"] = MemoryAllocator::getPeakMemoryUsage();
    memory["allocator_psram"] = MemoryAllocator::getPSRAMUsage();
    memory["allocator_heap"] = MemoryAllocator::getHeapUsage();

    return memory;
}

DynamicJsonDocument SystemManager::getSystemConfig() const {
    DynamicJsonDocument config(512);

    config["max_signals"] = m_capacity.maxSignals;
    config["max_tasks"] = m_capacity.maxTasks;
    config["max_timers"] = m_capacity.maxTimers;
    config["buffer_size"] = m_capacity.bufferSize;
    config["debug_mode"] = m_debugMode;

    return config;
}

// ==================== 组件管理 ====================
DataManager* SystemManager::getDataManager() const {
    return m_dataManager;
}

IRController* SystemManager::getIRController() const {
    return m_irController;
}

TaskManager* SystemManager::getTaskManager() const {
    return m_taskManager;
}

WebServerManager* SystemManager::getWebServerManager() const {
    return m_webServerManager;
}

WSManager* SystemManager::getWSManager() const {
    return m_wsManager;
}

NetworkSecurity* SystemManager::getNetworkSecurity() const {
    return m_networkSecurity;
}

// ==================== 消息广播 ====================
void SystemManager::broadcastMessage(const DynamicJsonDocument& message) {
    if (m_wsManager) {
        m_wsManager->broadcastMessage(message);
    }
}

void SystemManager::broadcastSystemStatus() {
    DynamicJsonDocument status = getSystemStatus();
    DynamicJsonDocument message(1024);

    message["type"] = "system_status";
    message["timestamp"] = millis();
    message["data"] = status;

    broadcastMessage(message);
}

void SystemManager::broadcastError(const String& error) {
    DynamicJsonDocument message(256);

    message["type"] = "system_error";
    message["timestamp"] = millis();
    message["error"] = error;
    message["critical"] = true;

    broadcastMessage(message);
}

// ==================== 错误处理 ====================
void SystemManager::handleSystemError(const String& error, bool critical) {
    m_lastError = error;

    logSystem("ERROR: " + error, "ERROR");

    if (critical) {
        Serial.printf("🚨 CRITICAL ERROR: %s\n", error.c_str());
        broadcastError(error);
    } else {
        Serial.printf("⚠️ WARNING: %s\n", error.c_str());
    }
}

String SystemManager::getLastError() const {
    return m_lastError;
}

void SystemManager::clearError() {
    m_lastError = "";
}

// ==================== 调试功能 ====================
void SystemManager::enableDebugMode(bool enable) {
    m_debugMode = enable;

    // 启用所有组件的调试模式
    if (m_irController) {
        m_irController->enableDebugMode(enable);
    }
    if (m_webServerManager) {
        m_webServerManager->enableDebugMode(enable);
    }
    if (m_wsManager) {
        m_wsManager->enableDebugMode(enable);
    }

    Serial.printf("🐛 System debug mode %s\n", enable ? "enabled" : "disabled");
}

String SystemManager::getDebugInfo() const {
    String info = "SystemManager Debug Info:\n";
    info += "  Initialized: " + String(m_initialized ? "Yes" : "No") + "\n";
    info += "  Started: " + String(m_started ? "Yes" : "No") + "\n";
    info += "  Healthy: " + String(isHealthy() ? "Yes" : "No") + "\n";
    info += "  WiFi Connected: " + String(m_wifiConnected ? "Yes" : "No") + "\n";
    info += "  File System Ready: " + String(m_fileSystemReady ? "Yes" : "No") + "\n";
    info += "  Components Healthy: " + String(m_componentsHealthy ? "Yes" : "No") + "\n";
    info += "  Uptime: " + String((millis() - m_startTime) / 1000) + " seconds\n";
    info += "  Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";

    if (PSRAMManager::isPSRAMAvailable()) {
        info += "  Free PSRAM: " + String(PSRAMManager::getFreePSRAM()) + " bytes\n";
    }

    return info;
}

DynamicJsonDocument SystemManager::performSystemSelfTest() {
    DynamicJsonDocument result(1024);

    result["timestamp"] = millis();
    result["test_name"] = "System Self Test";

    bool allPassed = true;
    JsonArray tests = result.createNestedArray("tests");

    // 测试1：内存
    JsonObject memTest = tests.createNestedObject();
    memTest["name"] = "Memory Test";
    bool memOK = (ESP.getFreeHeap() > 50000);
    memTest["passed"] = memOK;
    memTest["details"] = "Free heap: " + String(ESP.getFreeHeap()) + " bytes";
    if (!memOK) allPassed = false;

    // 测试2：文件系统
    JsonObject fsTest = tests.createNestedObject();
    fsTest["name"] = "File System Test";
    fsTest["passed"] = m_fileSystemReady;
    fsTest["details"] = m_fileSystemReady ? "SPIFFS OK" : "SPIFFS Failed";
    if (!m_fileSystemReady) allPassed = false;

    // 测试3：组件健康
    JsonObject compTest = tests.createNestedObject();
    compTest["name"] = "Components Health Test";
    bool compOK = checkComponentsHealth();
    compTest["passed"] = compOK;
    compTest["details"] = compOK ? "All components healthy" : "Some components unhealthy";
    if (!compOK) allPassed = false;

    // 测试4：WiFi连接
    JsonObject wifiTest = tests.createNestedObject();
    wifiTest["name"] = "WiFi Connection Test";
    wifiTest["passed"] = m_wifiConnected;
    wifiTest["details"] = m_wifiConnected ? "WiFi connected" : "WiFi not connected";
    // WiFi不是必需的，不影响总体结果

    result["overall_passed"] = allPassed;
    result["total_tests"] = tests.size();

    return result;
}

// ==================== 私有方法实现 ====================
bool SystemManager::initializeComponents() {
    Serial.println("🔧 Initializing components...");

    // 1. 创建数据管理器
    m_dataManager = new DataManager(m_capacity);
    if (!m_dataManager || !m_dataManager->initialize()) {
        logSystem("DataManager initialization failed", "ERROR");
        return false;
    }

    // 2. 创建红外控制器
    m_irController = new IRController();
    if (!m_irController || !m_irController->initialize()) {
        logSystem("IRController initialization failed", "ERROR");
        return false;
    }

    // 3. 创建任务管理器
    m_taskManager = new TaskManager();
    if (!m_taskManager || !m_taskManager->initialize()) {
        logSystem("TaskManager initialization failed", "ERROR");
        return false;
    }

    // 4. 创建网络安全管理器
    m_networkSecurity = new NetworkSecurity();
    if (!m_networkSecurity || !m_networkSecurity->initialize()) {
        logSystem("NetworkSecurity initialization failed", "ERROR");
        return false;
    }

    // 5. 创建Web服务器管理器
    m_webServerManager = new WebServerManager();
    if (!m_webServerManager || !m_webServerManager->initialize()) {
        logSystem("WebServerManager initialization failed", "ERROR");
        return false;
    }

    // 6. 创建WebSocket管理器
    m_wsManager = new WSManager();
    // WSManager需要Web服务器实例，在setupComponentDependencies中初始化

    Serial.println("✅ All components created");
    return true;
}

void SystemManager::setupComponentDependencies() {
    Serial.println("🔗 Setting up component dependencies...");

    // 设置红外控制器的数据管理器依赖
    if (m_irController && m_dataManager) {
        m_irController->setDataManager(m_dataManager);
    }

    // 设置任务管理器的依赖
    if (m_taskManager && m_dataManager && m_irController) {
        m_taskManager->setDependencies(m_dataManager, m_irController);
    }

    // 设置Web服务器管理器的依赖
    if (m_webServerManager && m_dataManager && m_irController && m_taskManager && m_networkSecurity) {
        m_webServerManager->setDependencies(m_dataManager, m_irController, m_taskManager, m_wsManager, m_networkSecurity);
    }

    // 设置WebSocket管理器的依赖
    if (m_wsManager && m_dataManager && m_irController && m_taskManager) {
        m_wsManager->setDependencies(m_dataManager, m_irController, m_taskManager);
    }

    Serial.println("✅ Component dependencies set up");
}

bool SystemManager::startServices() {
    Serial.println("🚀 Starting services...");

    // 1. 启动Web服务器
    if (m_webServerManager && !m_webServerManager->start(80)) {
        logSystem("Web server startup failed", "ERROR");
        return false;
    }

    // 2. 初始化WebSocket管理器（需要Web服务器实例）
    if (m_wsManager && m_webServerManager) {
        // 这里需要获取Web服务器实例，简化实现
        if (!m_wsManager->initialize(nullptr)) {
            logSystem("WebSocket manager initialization failed", "WARNING");
            // WebSocket失败不是致命错误
        }
    }

    Serial.println("✅ All services started");
    return true;
}

void SystemManager::stopServices() {
    Serial.println("🛑 Stopping services...");

    if (m_webServerManager) {
        m_webServerManager->stop();
    }

    Serial.println("✅ All services stopped");
}

void SystemManager::cleanupComponents() {
    Serial.println("🧹 Cleaning up components...");

    if (m_wsManager) {
        delete m_wsManager;
        m_wsManager = nullptr;
    }

    if (m_webServerManager) {
        delete m_webServerManager;
        m_webServerManager = nullptr;
    }

    if (m_taskManager) {
        delete m_taskManager;
        m_taskManager = nullptr;
    }

    if (m_irController) {
        delete m_irController;
        m_irController = nullptr;
    }

    if (m_dataManager) {
        delete m_dataManager;
        m_dataManager = nullptr;
    }

    if (m_networkSecurity) {
        delete m_networkSecurity;
        m_networkSecurity = nullptr;
    }

    Serial.println("✅ Component cleanup completed");
}

bool SystemManager::checkComponentsHealth() {
    bool healthy = true;

    if (m_dataManager && !m_dataManager->isHealthy()) {
        healthy = false;
    }

    if (m_irController && !m_irController->isHealthy()) {
        healthy = false;
    }

    if (m_taskManager && !m_taskManager->isHealthy()) {
        healthy = false;
    }

    if (m_webServerManager && !m_webServerManager->isHealthy()) {
        healthy = false;
    }

    if (m_wsManager && !m_wsManager->isHealthy()) {
        healthy = false;
    }

    if (m_networkSecurity && !m_networkSecurity->isHealthy()) {
        healthy = false;
    }

    return healthy;
}

void SystemManager::monitorSystemResources() {
    static unsigned long lastCheck = 0;
    unsigned long currentTime = millis();

    // 每分钟检查一次
    if (currentTime - lastCheck < 60000) {
        return;
    }

    lastCheck = currentTime;

    // 检查内存使用情况
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 20000) { // 少于20KB
        handleSystemError("Low heap memory: " + String(freeHeap) + " bytes", false);
    }

    // 检查PSRAM使用情况
    if (PSRAMManager::isPSRAMAvailable()) {
        size_t freePSRAM = PSRAMManager::getFreePSRAM();
        if (freePSRAM < 100000) { // 少于100KB
            handleSystemError("Low PSRAM: " + String(freePSRAM) + " bytes", false);
        }
    }
}

void SystemManager::logSystem(const String& message, const String& level) {
    Serial.printf("[%s] SystemManager: %s\n", level.c_str(), message.c_str());
}

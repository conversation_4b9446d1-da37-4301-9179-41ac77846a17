[env:esp32-s3-devkitc-1]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; 串口配置
monitor_speed = 115200
upload_speed = 921600

; 编译选项 - 禁用PSRAM，使用普通RAM模式
build_flags = 
    -DESP32
    -DESP32S3
    -DARDUINO_ESP32S3_DEV
    -DARDUINO_ARCH_ESP32
    -DCORE_DEBUG_LEVEL=3
    -DDISABLE_PSRAM=1
    -DRAM_ONLY_MODE=1
    -DBOARD_HAS_NO_PSRAM=1
    -DCONFIG_SPIRAM_SUPPORT=0

; 分区表配置 - 使用默认分区表，不需要PSRAM
board_build.partitions = default.csv

; Flash配置 - 适配GD25Q128 16MB Flash
board_build.flash_size = 16MB
board_build.flash_mode = qio
board_build.f_flash = 80000000L
board_build.f_cpu = 240000000L

; 库依赖
lib_deps = 
    esphome/ESPAsyncWebServer-esphome@^3.2.2
    esphome/AsyncTCP-esphome@^2.1.3
    bblanchon/Arduino<PERSON>son@^6.21.3
    crankyoldgit/IRremoteESP8266@^2.8.6
    paulstoffregen/Time@^1.6.1

; 调试配置
debug_tool = esp-builtin
debug_init_break = tbreak setup

; 上传配置
upload_protocol = esptool

; 文件系统配置
board_build.filesystem = spiffs

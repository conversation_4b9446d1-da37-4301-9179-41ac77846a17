:\Users\S\Desktop\Arduino_ESP32S3_IRSystem\PSRAMManager.cpp: In static member function 'static bool PSRAMManager::testPSRAMFunctionality()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\PSRAMManager.cpp:105:36: error: 'PSRAM_TEST_SIZE' was not declared in this scope
     if (!performPSRAMReadWriteTest(PSRAM_TEST_SIZE)) {
                                    ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\PSRAMManager.cpp:105:36: note: suggested alternative: 'XSHAL_RAM_SIZE'
     if (!performPSRAMReadWriteTest(PSRAM_TEST_SIZE)) {
                                    ^~~~~~~~~~~~~~~
                                    XSHAL_RAM_SIZE
"C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\xtensa-esp32s3-elf-gcc\\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++" -DHAVE_CONFIG_H "-DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE "-DIDF_VER=\"v4.4.7-dirty\"" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/heap/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/log/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/xtensa/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/driver/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_pm/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/efuse/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/vfs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_event/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_netif/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_eth/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_phy/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/app_trace/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_timer/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/app_update/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/spi_flash/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/pthread/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espcoredump/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ieee802154/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/console" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/asio/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/cbor/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/unity/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/unity/unity/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/coap/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-tls" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hid/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mdns/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/sdmmc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/expat/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/idf_test/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/jsmn/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json/cJSON" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/openssl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/perfmon/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/spiffs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/usb/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ulp/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/rtc_store/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_insights/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/qrcode/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ws2812_led" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/qio_qspi/include" -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -MMD -c -w -Os -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 "-DARDUINO_BOARD=\"ESP32S3_DEV\"" "-DARDUINO_VARIANT=\"esp32s3\"" -DARDUINO_PARTITION_default -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=0 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 "@C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43/build_opt.h" "@C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43/file_opts" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\cores\\esp32" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\WiFi\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\FS\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\SPIFFS\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\Async_TCP\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\ESP_Async_WebServer\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\ArduinoJson\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\IRremoteESP8266\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\Time" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\Preferences\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\Update\\src" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\WSManager.cpp" -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\WSManager.cpp.o"
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.cpp:1:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\TaskManager.cpp:4:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\TaskManager.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\TaskManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\TaskManager.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\TaskManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.cpp:1:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:325:10: error: 'void NetworkSecurity::recordRequest(const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& ipAddress, const String& endpoint = "");
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:93:10: note: previous declaration 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& clientIP, const String& endpoint);
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:355:10: error: 'bool NetworkSecurity::validateAPIKey(const String&)' cannot be overloaded with 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:49:10: note: previous declaration 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:390:10: error: 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& ipAddress, const String& description);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:174:10: note: previous declaration 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& clientIP, const String& details);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:473:10: error: redeclaration of 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                 // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:179:10: note: previous declaration 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                          // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:481:10: error: 'vector' in namespace 'std' does not name a template type
     std::vector<String> m_whitelist;    // IP白名单
          ^~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:481:5: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:8:1:
+#include <vector>
 
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:481:5:
     std::vector<String> m_whitelist;    // IP白名单
     ^~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:482:10: error: 'vector' in namespace 'std' does not name a template type
     std::vector<String> m_blacklist;    // IP黑名单
          ^~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:482:5: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
     std::vector<String> m_blacklist;    // IP黑名单
     ^~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:489:37: error: redeclaration of 'std::map<String, NetworkSecurity::RateLimitRule> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitRule> m_rateLimits;
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:197:37: note: previous declaration 'std::map<String, NetworkSecurity::RateLimitInfo> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitInfo> m_rateLimits; // 速率限制映射表
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:27: error: 'vector' is not a member of 'std'
     std::map<String, std::vector<RequestRecord>> m_requestHistory;
                           ^~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:27: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:27: error: 'vector' is not a member of 'std'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:27: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:34: error: template argument 2 is invalid
     std::map<String, std::vector<RequestRecord>> m_requestHistory;
                                  ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:34: error: template argument 4 is invalid
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:495:47: error: expected unqualified-id before '>' token
     std::map<String, std::vector<RequestRecord>> m_requestHistory;
                                               ^~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:506:10: error: 'vector' in namespace 'std' does not name a template type
     std::vector<APIKeyInfo> m_apiKeys;
          ^~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:506:5: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
     std::vector<APIKeyInfo> m_apiKeys;
     ^~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:515:10: error: 'vector' in namespace 'std' does not name a template type
     std::vector<SecurityEvent> m_securityEvents;
          ^~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:515:5: note: 'std::vector' is defined in header '<vector>'; did you forget to '#include <vector>'?
     std::vector<SecurityEvent> m_securityEvents;
     ^~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:518:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;      // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:209:19: note: previous declaration 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;               // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:519:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;    // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:210:19: note: previous declaration 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;             // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:520:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests; // 频率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:211:19: note: previous declaration 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests;         // 被速率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataManager.cpp:2:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataManager.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataManager.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:3:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp: In member function 'bool IRController::sendSignal(const String&, const String&)':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:128:19: error: 'sendNECSignal' was not declared in this scope
         success = sendNECSignal(signalCode);
                   ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:128:19: note: suggested alternative: 'sendSignal'
         success = sendNECSignal(signalCode);
                   ^~~~~~~~~~~~~
                   sendSignal
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:130:19: error: 'sendRC5Signal' was not declared in this scope
         success = sendRC5Signal(signalCode);
                   ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:130:19: note: suggested alternative: 'sendRawSignal'
         success = sendRC5Signal(signalCode);
                   ^~~~~~~~~~~~~
                   sendRawSignal
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:132:19: error: 'sendRC6Signal' was not declared in this scope
         success = sendRC6Signal(signalCode);
                   ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:132:19: note: suggested alternative: 'sendRawSignal'
         success = sendRC6Signal(signalCode);
                   ^~~~~~~~~~~~~
                   sendRawSignal
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:59:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:65:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:325:10: error: 'void NetworkSecurity::recordRequest(const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& ipAddress, const String& endpoint = "");
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:93:10: note: previous declaration 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& clientIP, const String& endpoint);
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:355:10: error: 'bool NetworkSecurity::validateAPIKey(const String&)' cannot be overloaded with 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:49:10: note: previous declaration 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:390:10: error: 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& ipAddress, const String& description);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:174:10: note: previous declaration 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& clientIP, const String& details);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:473:10: error: redeclaration of 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                 // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:179:10: note: previous declaration 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                          // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:489:37: error: redeclaration of 'std::map<String, NetworkSecurity::RateLimitRule> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitRule> m_rateLimits;
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:197:37: note: previous declaration 'std::map<String, NetworkSecurity::RateLimitInfo> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitInfo> m_rateLimits; // 速率限制映射表
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:518:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;      // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:209:19: note: previous declaration 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;               // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:519:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;    // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:210:19: note: previous declaration 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;             // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:520:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests; // 频率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:211:19: note: previous declaration 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests;         // 被速率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:138:16: error: redefinition of 'SystemCapacity getSystemCapacity(SystemMode)'
 SystemCapacity getSystemCapacity(SystemMode mode) {
                ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:54:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\system-config.h:196:23: note: 'SystemCapacity getSystemCapacity(SystemMode)' previously defined here
 inline SystemCapacity getSystemCapacity(SystemMode mode) {
                       ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:330:16: error: redefinition of 'SystemCapacity getSystemCapacity(SystemMode)'
 SystemCapacity getSystemCapacity(SystemMode mode) {
                ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:54:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\system-config.h:196:23: note: 'SystemCapacity getSystemCapacity(SystemMode)' previously defined here
 inline SystemCapacity getSystemCapacity(SystemMode mode) {
                       ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\library_compatibility_test.ino:49:6: error: redefinition of 'void setup()'
 void setup() {
      ^~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:76:6: note: 'void setup()' previously defined here
 void setup() {
      ^~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\library_compatibility_test.ino:94:6: error: redefinition of 'void loop()'
 void loop() {
      ^~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:302:6: note: 'void loop()' previously defined here
 void loop() {
      ^~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\library_compatibility_test.ino: In function 'void testESP32Core()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\library_compatibility_test.ino:104:56: error: 'class EspClass' has no member named 'getArduinoVersion'; did you mean 'getSdkVersion'?
   Serial.printf("   ✅ Arduino Core版本: %s\n", ESP.getArduinoVersion().c_str());
                                                        ^~~~~~~~~~~~~~~~~
                                                        getSdkVersion
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:38:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\Arduino_ESP32S3_IRSystem.ino:38:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.h:12,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:1:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: error: 'PSRAM_THRESHOLD_SIZE' was not declared in this scope
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\MemoryAllocator.h:305:43: note: suggested alternative: 'ESP_THREAD_SAFE'
     static const size_t PSRAM_THRESHOLD = PSRAM_THRESHOLD_SIZE;
                                           ^~~~~~~~~~~~~~~~~~~~
                                           ESP_THREAD_SAFE
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:7:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:325:10: error: 'void NetworkSecurity::recordRequest(const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& ipAddress, const String& endpoint = "");
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:93:10: note: previous declaration 'void NetworkSecurity::recordRequest(const String&, const String&)'
     void recordRequest(const String& clientIP, const String& endpoint);
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:355:10: error: 'bool NetworkSecurity::validateAPIKey(const String&)' cannot be overloaded with 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:49:10: note: previous declaration 'bool NetworkSecurity::validateAPIKey(const String&)'
     bool validateAPIKey(const String& apiKey);
          ^~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:390:10: error: 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)' cannot be overloaded with 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& ipAddress, const String& description);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:174:10: note: previous declaration 'void NetworkSecurity::logSecurityEvent(const String&, const String&, const String&)'
     void logSecurityEvent(const String& eventType, const String& clientIP, const String& details);
          ^~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:473:10: error: redeclaration of 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                 // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:179:10: note: previous declaration 'bool NetworkSecurity::m_initialized'
     bool m_initialized;                          // 是否已初始化
          ^~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:489:37: error: redeclaration of 'std::map<String, NetworkSecurity::RateLimitRule> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitRule> m_rateLimits;
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:197:37: note: previous declaration 'std::map<String, NetworkSecurity::RateLimitInfo> NetworkSecurity::m_rateLimits'
     std::map<String, RateLimitInfo> m_rateLimits; // 速率限制映射表
                                     ^~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:518:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;      // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:209:19: note: previous declaration 'long unsigned int NetworkSecurity::m_totalRequests'
     unsigned long m_totalRequests;               // 总请求数
                   ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:519:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;    // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:210:19: note: previous declaration 'long unsigned int NetworkSecurity::m_blockedRequests'
     unsigned long m_blockedRequests;             // 被阻止的请求数
                   ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:520:19: error: redeclaration of 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests; // 频率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\NetworkSecurity.h:211:19: note: previous declaration 'long unsigned int NetworkSecurity::m_rateLimitedRequests'
     unsigned long m_rateLimitedRequests;         // 被速率限制的请求数
                   ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp: In member function 'bool SystemManager::isHealthy() const':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:164:57: error: passing 'const SystemManager' as 'this' argument discards qualifiers [-fpermissive]
     return m_componentsHealthy && checkComponentsHealth();
                                                         ^
In file included from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:1:
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.h:344:10: note:   in call to 'bool SystemManager::checkComponentsHealth()'
     bool checkComponentsHealth();
          ^~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp: In member function 'ArduinoJson::V6213PB2::DynamicJsonDocument SystemManager::getSystemStatus() const':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:370:79: error: 'class NetworkSecurity' has no member named 'isHealthy'
     components["network_security"] = (m_networkSecurity && m_networkSecurity->isHealthy());
                                                                               ^~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp: In member function 'bool SystemManager::checkComponentsHealth()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:762:50: error: 'class NetworkSecurity' has no member named 'isHealthy'
     if (m_networkSecurity && !m_networkSecurity->isHealthy()) {
                                                  ^~~~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\SystemManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\WSManager.h:6,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\WSManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonArray; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonArray]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonArray]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\DataStructures.h:117:48:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\WSManager.h:6,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\WSManager.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
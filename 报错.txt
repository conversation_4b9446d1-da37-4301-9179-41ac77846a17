C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\xtensa-esp32s3-elf-gcc\\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++" "-Wl,--Map=C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43/Arduino_ESP32S3_IRSystem.ino.map" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/lib" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/ld" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/qio_opi" -T memory.ld -T sections.ld -T esp32s3.rom.ld -T esp32s3.rom.api.ld -T esp32s3.rom.libgcc.ld -T esp32s3.rom.newlib.ld -T esp32s3.rom.version.ld -T esp32s3.rom.newlib-time.ld -T esp32s3.peripherals.ld -mlongcalls -Wl,--cref -Wl,--gc-sections -fno-rtti -fno-lto -Wl,--wrap=esp_log_write -Wl,--wrap=esp_log_writev -Wl,--wrap=log_printf -u _Z5setupv -u _Z4loopv -u esp_app_desc -u pthread_include_pthread_impl -u pthread_include_pthread_cond_var_impl -u pthread_include_pthread_local_storage_impl -u pthread_include_pthread_rwlock_impl -u include_esp_phy_override -u ld_include_highint_hdl -u start_app -u start_app_other_cores -u __ubsan_include -Wl,--wrap=longjmp -u __assert_func -u vfs_include_syscalls_impl -Wl,--undefined=uxTopUsedPriority -u app_main -u newlib_include_heap_impl -u newlib_include_syscalls_impl -u newlib_include_pthread_impl -u newlib_include_assert_impl -u __cxa_guard_dummy -mfix-esp32-psram-cache-issue -Wl,--start-group "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\Arduino_ESP32S3_IRSystem.ino.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\DataManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\IRController.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\MemoryAllocator.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\NetworkSecurity.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\PSRAMManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\SystemManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\TaskManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\WSManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\sketch\\WebServerManager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiAP.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiClient.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiGeneric.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiMulti.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiSTA.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiScan.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiServer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\WiFi\\WiFiUdp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\FS\\FS.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\FS\\vfs_api.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\SPIFFS\\SPIFFS.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Async_TCP\\AsyncTCP.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\AsyncEventSource.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\AsyncJson.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\AsyncMessagePack.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\AsyncWebHeader.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\AsyncWebSocket.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\BackPort_SHA1Builder.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\ChunkPrint.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\Middleware.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\WebAuthentication.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\WebHandlers.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\WebRequest.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\WebResponses.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\ESP_Async_WebServer\\WebServer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRac.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRrecv.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRsend.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRtext.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRtimer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\IRutils.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Airton.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Airwell.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Aiwa.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Amcor.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Argo.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Arris.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Bosch.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Bose.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Carrier.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_ClimaButler.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Coolix.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Corona.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Daikin.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Delonghi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Denon.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Dish.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Doshisha.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Ecoclim.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Electra.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_EliteScreens.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Epson.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Fujitsu.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_GICable.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_GlobalCache.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Goodweather.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Gorenje.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Gree.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Haier.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Hitachi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Inax.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_JVC.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Kelon.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Kelvinator.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_LG.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Lasertag.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Lego.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Lutron.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_MWM.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Magiquest.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Metz.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Midea.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_MilesTag2.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Mirage.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Mitsubishi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_MitsubishiHeavy.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Multibrackets.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_NEC.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Neoclima.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Nikai.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Panasonic.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Pioneer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Pronto.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_RC5_RC6.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_RCMM.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Rhoss.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Samsung.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Sanyo.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Sharp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Sherwood.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Sony.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Symphony.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Tcl.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Technibel.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Teco.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Teknopoint.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Toshiba.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Toto.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Transcold.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Trotec.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Truma.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Vestel.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Voltas.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Whirlpool.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Whynter.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Wowwee.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Xmp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_York.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\IRremoteESP8266\\ir_Zepeal.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Time\\DateStrings.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Time\\Time.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Preferences\\Preferences.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Update\\HttpsOTAUpdate.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43\\libraries\\Update\\Updater.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\cores\\2d163426eab7bdfd991375911f724b29\\core.a" -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lasio -lbt -lcbor -lunity -lcmock -lcoap -lnghttp -lesp-tls -lesp_adc_cal -lesp_hid -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lesp_https_server -lesp_lcd -lprotobuf-c -lprotocomm -lmdns -lesp_local_ctrl -lsdmmc -lesp_serial_slave_link -lesp_websocket_client -lexpat -lwear_levelling -lfatfs -lfreemodbus -ljsmn -ljson -llibsodium -lmqtt -lopenssl -lperfmon -lspiffs -lusb -lulp -lwifi_provisioning -lrmaker_common -lesp_diagnostics -lrtc_store -lesp_insights -ljson_parser -ljson_generator -lesp_schedule -lespressif__esp_secure_cert_mgr -lesp_rainmaker -lgpio_button -lqrcode -lws2812_led -lesp32-camera -lesp_littlefs -lespressif__esp-dsp -lfb_gfx -lasio -lcmock -lunity -lcoap -lesp_lcd -lesp_websocket_client -lexpat -lfreemodbus -ljsmn -llibsodium -lperfmon -lusb -lesp_adc_cal -lesp_hid -lfatfs -lwear_levelling -lopenssl -lspiffs -lesp_insights -lcbor -lesp_diagnostics -lrtc_store -lesp_rainmaker -lesp_local_ctrl -lesp_https_server -lwifi_provisioning -lprotocomm -lbt -lbtdm_app -lprotobuf-c -lmdns -ljson -ljson_parser -ljson_generator -lesp_schedule -lespressif__esp_secure_cert_mgr -lqrcode -lrmaker_common -lmqtt -larduino_tinyusb -lcat_face_detect -lhuman_face_detect -lcolor_detect -lmfn -ldl -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lphy -lbtbb -lesp_phy -lphy -lbtbb -lesp_phy -lphy -lbtbb -lxt_hal -lc -lm -lnewlib -lstdc++ -lpthread -lgcc -lcxx -Wl,--end-group -Wl,-EL -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\761F9EE8DC005164447ED3D2D6C74A43/Arduino_ESP32S3_IRSystem.ino.elf"
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\TaskManager.cpp.o:(.literal._ZN11TaskManager8shutdownEv+0x8): undefined reference to `TaskManager::clearTaskQueue()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\TaskManager.cpp.o:(.literal._ZN11TaskManager4loopEv+0x0): undefined reference to `TaskManager::processTaskQueue()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\TaskManager.cpp.o:(.literal._ZN11TaskManager4loopEv+0x4): undefined reference to `TaskManager::checkScheduledTasks()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\TaskManager.cpp.o: in function `TaskManager::shutdown()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem/TaskManager.cpp:59: undefined reference to `TaskManager::clearTaskQueue()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\TaskManager.cpp.o: in function `TaskManager::loop()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem/TaskManager.cpp:360: undefined reference to `TaskManager::processTaskQueue()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem/TaskManager.cpp:363: undefined reference to `TaskManager::checkScheduledTasks()'
collect2.exe: error: ld returned 1 exit status
Using library WiFi at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi 
Using library FS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS 
Using library SPIFFS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS 
Using library Async TCP at version 3.4.4 in folder: C:\Users\<USER>\Documents\Arduino\libraries\Async_TCP 
Using library ESP Async WebServer at version 3.7.8 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer 
Using library ArduinoJson at version 6.21.3 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson 
Using library IRremoteESP8266 at version 2.8.6 in folder: C:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266 
Using library Time at version 1.6.1 in folder: C:\Users\<USER>\Documents\Arduino\libraries\Time 
Using library Preferences at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences 
Using library Update at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Update 

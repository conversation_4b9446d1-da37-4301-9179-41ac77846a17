FQBN: esp32:esp32:esp32s3:UploadSpeed=115200,CDCOnBoot=cdc,FlashSize=16M,PartitionScheme=app3M_fat9M_16MB,PSRAM=opi
Using board 'esp32s3' from platform in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17
Using core 'esp32' from platform in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17

cmd /c if exist "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\partitions.csv" COPY /y "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\partitions.csv" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\partitions.csv"
cmd /c if not exist "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\partitions.csv" if exist "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3\\partitions.csv" COPY "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3\\partitions.csv" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\partitions.csv"
cmd /c if not exist "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\partitions.csv" COPY "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\tools\\partitions\\app3M_fat9M_16MB.csv" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\partitions.csv"
cmd /c IF EXIST "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\bootloader.bin" ( COPY /y "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\bootloader.bin" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\ESP32_S3_IR_System_Arduino.ino.bootloader.bin" ) ELSE ( IF EXIST "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3\\bootloader.bin" ( COPY "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3\\bootloader.bin" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\ESP32_S3_IR_System_Arduino.ino.bootloader.bin" ) ELSE ( "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\esptool_py\\4.5.1\\esptool.exe" --chip esp32s3 elf2image --flash_mode dio --flash_freq 80m --flash_size 16MB -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\ESP32_S3_IR_System_Arduino.ino.bootloader.bin" "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\tools\\sdk\\esp32s3\\bin\\bootloader_qio_80m.elf" ) )
esptool.py v4.5.1
Creating esp32s3 image...
Merged 1 ELF section
Successfully created esp32s3 image.
cmd /c if exist "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\build_opt.h" COPY /y "C:\\Users\\<USER>\\Desktop\\ESP32_S3_IR_System_Arduino\\build_opt.h" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\build_opt.h"
�Ѹ���         1 ���ļ���
cmd /c if not exist "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\build_opt.h" type nul > "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\build_opt.h"
cmd /c type nul > "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/file_opts"
Detecting libraries used...
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for WiFi.h: [WiFi@2.0.0]
ResolveLibrary(WiFi.h)
  -> candidates: [WiFi@2.0.0]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for SPIFFS.h: [SPIFFS@2.0.0]
ResolveLibrary(SPIFFS.h)
  -> candidates: [SPIFFS@2.0.0]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for FS.h: [FS@2.0.0]
ResolveLibrary(FS.h)
  -> candidates: [FS@2.0.0]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for ESPAsyncWebServer.h: [ESP Async WebServer@3.7.8]
ResolveLibrary(ESPAsyncWebServer.h)
  -> candidates: [ESP Async WebServer@3.7.8]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for AsyncTCP.h: [Async TCP@3.4.4]
ResolveLibrary(AsyncTCP.h)
  -> candidates: [Async TCP@3.4.4]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src -Ic:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for ArduinoJson.h: [ArduinoJson@6.21.3]
ResolveLibrary(ArduinoJson.h)
  -> candidates: [ArduinoJson@6.21.3]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src -Ic:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for Preferences.h: [Preferences@2.0.0]
ResolveLibrary(Preferences.h)
  -> candidates: [Preferences@2.0.0]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src -Ic:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Alternatives for IRremoteESP8266.h: [IRremoteESP8266@2.8.6]
ResolveLibrary(IRremoteESP8266.h)
  -> candidates: [IRremoteESP8266@2.8.6]
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src -Ic:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences\src -Ic:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o nul
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\data_manager.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ir_controller.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\system_monitor.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\task_manager.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\web_server_manager.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\websocket_manager.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\wifi_manager.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFi.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiAP.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiClient.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiGeneric.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiMulti.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiSTA.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiScan.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiServer.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src\WiFiUdp.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src\SPIFFS.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src\FS.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src\vfs_api.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\AsyncEventSource.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\AsyncJson.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\AsyncMessagePack.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\AsyncWebHeader.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\AsyncWebSocket.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\BackPort_SHA1Builder.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\ChunkPrint.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\Middleware.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebAuthentication.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebHandlers.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebRequest.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebResponses.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebServer.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src\AsyncTCP.cpp
Using cached library dependencies for file: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences\src\Preferences.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRac.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRrecv.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRsend.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRtext.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRtimer.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\IRutils.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Airton.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Airwell.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Aiwa.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Amcor.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Argo.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Arris.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Bosch.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Bose.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Carrier.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_ClimaButler.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Coolix.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Corona.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Daikin.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Delonghi.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Denon.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Dish.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Doshisha.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Ecoclim.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Electra.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_EliteScreens.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Epson.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Fujitsu.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_GICable.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_GlobalCache.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Goodweather.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Gorenje.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Gree.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Haier.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Hitachi.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Inax.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_JVC.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Kelon.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Kelvinator.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_LG.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Lasertag.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Lego.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Lutron.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_MWM.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Magiquest.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Metz.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Midea.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_MilesTag2.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Mirage.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Mitsubishi.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_MitsubishiHeavy.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Multibrackets.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_NEC.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Neoclima.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Nikai.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Panasonic.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Pioneer.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Pronto.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_RC5_RC6.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_RCMM.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Rhoss.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Samsung.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Sanyo.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Sharp.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Sherwood.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Sony.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Symphony.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Tcl.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Technibel.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Teco.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Teknopoint.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Toshiba.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Toto.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Transcold.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Trotec.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Truma.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Vestel.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Voltas.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Whirlpool.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Whynter.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Wowwee.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Xmp.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_York.cpp
Using cached library dependencies for file: c:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src\ir_Zepeal.cpp
Generating function prototypes...
C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\tools\xtensa-esp32s3-elf-gcc\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++ -DHAVE_CONFIG_H -DMBEDTLS_CONFIG_FILE="mbedtls/esp_config.h" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE -DIDF_VER="v4.4.7-dirty" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/heap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/log/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_pm/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/vfs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_event/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_netif/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_eth/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_trace/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_timer/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/app_update/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spi_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/pthread/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ieee802154/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/console -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/asio/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cbor/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/unity/unity/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_hid/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mdns/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/sdmmc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/expat/port/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fatfs/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/jsmn/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json/cJSON -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/openssl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/perfmon/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/spiffs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/usb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ulp/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/rtc_store/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_insights/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/qrcode/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/ws2812_led -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17/tools/sdk/esp32s3/qio_opi/include -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -c -w -Os -w -x c++ -E -CC -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 -DARDUINO_BOARD="ESP32S3_DEV" -DARDUINO_VARIANT="esp32s3" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/build_opt.h @C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77/file_opts -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\cores\esp32 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\variants\esp32s3 -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src -Ic:\Users\<USER>\Documents\Arduino\libraries\Async_TCP\src -Ic:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src -IC:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences\src -Ic:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266\src C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ESP32_S3_IR_System_Arduino.ino.cpp -o C:\Users\<USER>\AppData\Local\Temp\3809017260\sketch_merged.cpp
C:\Users\<USER>\AppData\Local\Arduino15\packages\builtin\tools\ctags\5.8-arduino11/ctags -u --language-force=c++ -f - --c++-kinds=svpf --fields=KSTtzns --line-directives C:\Users\<USER>\AppData\Local\Temp\3809017260\sketch_merged.cpp

Compiling sketch...
"C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\xtensa-esp32s3-elf-gcc\\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++" -DHAVE_CONFIG_H "-DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"" -DUNITY_INCLUDE_CONFIG_H -DWITH_POSIX -D_GNU_SOURCE "-DIDF_VER=\"v4.4.7-dirty\"" -DESP_PLATFORM -D_POSIX_READER_WRITER_LOCKS "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/newlib/platform_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/port/xtensa/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/esp_additions" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/heap/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/log/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/include/apps/sntp" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/lwip/src/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/soc/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/hal/platform_port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/include/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rom/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/port/soc" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_system/port/public_compat" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/xtensa/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/xtensa/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/driver/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/driver/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_pm/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_ringbuf/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/efuse/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/efuse/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/vfs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_wifi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_event/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_netif/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_eth/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/tcpip_adapter/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_phy/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_phy/esp32s3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_ipc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/app_trace/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_timer/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/mbedtls/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/app_update/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/spi_flash/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bootloader_support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nvs_flash/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/pthread/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/xtensa" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espcoredump/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ieee802154/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/console" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/asio/asio/asio/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/asio/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/osi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/include/esp32c3/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/api/include/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/cbor/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/unity/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/unity/unity/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/cmock/CMock/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/coap/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/coap/libcoap/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nghttp/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-tls" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_adc_cal/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_hid/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/tcp_transport/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_http_client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_http_server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_https_ota/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_https_server/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_lcd/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_lcd/interface" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protobuf-c/protobuf-c" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/common" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/security" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/protocomm/include/transports" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mdns/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_local_ctrl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/sdmmc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_serial_slave_link/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_websocket_client/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/expat/expat/expat/lib" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/expat/port/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wear_levelling/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/diskio" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/vfs" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fatfs/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/idf_test/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/idf_test/include/esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/jsmn/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json/cJSON" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/libsodium/port_include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/openssl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/perfmon/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/spiffs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/usb/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ulp/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/wifi_provisioning/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/rmaker_common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_diagnostics/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/rtc_store/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_insights/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_parser/upstream" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/json_generator/upstream" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_schedule/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_rainmaker/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/gpio_button/button/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/qrcode/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/ws2812_led" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/freertos/include/freertos" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/arduino_tinyusb/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp_littlefs/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/tool" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/typedef" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/image" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/math" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/nn" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/layer" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/detect" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp-dl/include/model_zoo" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp32-camera/driver/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/esp32-camera/conversions/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/include/fb_gfx/include" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/qio_opi/include" -mlongcalls -ffunction-sections -fdata-sections -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wno-unused-parameter -Wno-sign-compare -ggdb -freorder-blocks -Wwrite-strings -fstack-protector -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -std=gnu++11 -fexceptions -fno-rtti -MMD -c -w -Os -DF_CPU=240000000L -DARDUINO=10607 -DARDUINO_ESP32S3_DEV -DARDUINO_ARCH_ESP32 "-DARDUINO_BOARD=\"ESP32S3_DEV\"" "-DARDUINO_VARIANT=\"esp32s3\"" -DARDUINO_PARTITION_app3M_fat9M_16MB -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 "@C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/build_opt.h" "@C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/file_opts" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\cores\\esp32" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\variants\\esp32s3" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\WiFi\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\SPIFFS\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\FS\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\ESP_Async_WebServer\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\Async_TCP\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\ArduinoJson\\src" "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\libraries\\Preferences\\src" "-Ic:\\Users\\<USER>\\Documents\\Arduino\\libraries\\IRremoteESP8266\\src" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\ESP32_S3_IR_System_Arduino.ino.cpp" -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\ESP32_S3_IR_System_Arduino.ino.cpp.o"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\websocket_manager.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\task_manager.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\data_manager.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\wifi_manager.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\web_server_manager.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\ir_controller.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\sketch\system_monitor.cpp.o
Compiling libraries...
Compiling library "WiFi"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiServer.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiMulti.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFi.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiGeneric.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiScan.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiClient.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiAP.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiSTA.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\WiFi\WiFiUdp.cpp.o
Compiling library "SPIFFS"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\SPIFFS\SPIFFS.cpp.o
Compiling library "FS"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\FS\FS.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\FS\vfs_api.cpp.o
Compiling library "ESP Async WebServer"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\ChunkPrint.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\AsyncEventSource.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\AsyncWebSocket.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\AsyncMessagePack.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\Middleware.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\BackPort_SHA1Builder.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\WebAuthentication.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\WebHandlers.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\AsyncWebHeader.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\WebResponses.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\WebServer.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\WebRequest.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\ESP_Async_WebServer\AsyncJson.cpp.o
Compiling library "Async TCP"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\Async_TCP\AsyncTCP.cpp.o
Compiling library "ArduinoJson"
Compiling library "Preferences"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\Preferences\Preferences.cpp.o
Compiling library "IRremoteESP8266"
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRac.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRutils.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRrecv.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Airwell.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRtext.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Airton.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRtimer.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\IRsend.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Aiwa.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Argo.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Bosch.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Arris.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Amcor.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Bose.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Carrier.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_ClimaButler.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Coolix.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Daikin.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Delonghi.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Corona.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Dish.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Doshisha.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Electra.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_GlobalCache.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_EliteScreens.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Denon.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_GICable.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Fujitsu.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Epson.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Goodweather.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Ecoclim.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Haier.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Gree.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Gorenje.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Inax.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Hitachi.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Kelon.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Kelvinator.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Lego.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_LG.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Lutron.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_MWM.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Midea.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Lasertag.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Metz.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Mirage.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_JVC.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Mitsubishi.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_MilesTag2.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_MitsubishiHeavy.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Multibrackets.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Magiquest.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Nikai.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Pronto.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Panasonic.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_NEC.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Neoclima.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_RC5_RC6.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Sanyo.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Pioneer.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Sherwood.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_RCMM.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Samsung.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Sony.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Rhoss.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Sharp.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Tcl.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Technibel.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Toshiba.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Symphony.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Teknopoint.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Toto.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Trotec.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Truma.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Teco.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Voltas.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Vestel.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Transcold.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Wowwee.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Whirlpool.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Whynter.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_York.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Xmp.cpp.o
Using previously compiled file: C:\Users\<USER>\AppData\Local\arduino\sketches\DA42588DD93F5258BAF0CB722215AC77\libraries\IRremoteESP8266\ir_Zepeal.cpp.o
Compiling core...
cmd /c echo -DARDUINO_CORE_BUILD > "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/file_opts"
Using precompiled core: C:\Users\<USER>\AppData\Local\arduino\cores\a9026acf4f1aed3862acef0498beb7be\core.a
cmd /c type nul > "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/file_opts"
Linking everything together...
"C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\xtensa-esp32s3-elf-gcc\\esp-2021r2-patch5-8.4.0/bin/xtensa-esp32s3-elf-g++" "-Wl,--Map=C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/ESP32_S3_IR_System_Arduino.ino.map" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/lib" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/ld" "-LC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17/tools/sdk/esp32s3/qio_opi" -T memory.ld -T sections.ld -T esp32s3.rom.ld -T esp32s3.rom.api.ld -T esp32s3.rom.libgcc.ld -T esp32s3.rom.newlib.ld -T esp32s3.rom.version.ld -T esp32s3.rom.newlib-time.ld -T esp32s3.peripherals.ld -mlongcalls -Wl,--cref -Wl,--gc-sections -fno-rtti -fno-lto -Wl,--wrap=esp_log_write -Wl,--wrap=esp_log_writev -Wl,--wrap=log_printf -u _Z5setupv -u _Z4loopv -u esp_app_desc -u pthread_include_pthread_impl -u pthread_include_pthread_cond_var_impl -u pthread_include_pthread_local_storage_impl -u pthread_include_pthread_rwlock_impl -u include_esp_phy_override -u ld_include_highint_hdl -u start_app -u start_app_other_cores -u __ubsan_include -Wl,--wrap=longjmp -u __assert_func -u vfs_include_syscalls_impl -Wl,--undefined=uxTopUsedPriority -u app_main -u newlib_include_heap_impl -u newlib_include_syscalls_impl -u newlib_include_pthread_impl -u newlib_include_assert_impl -u __cxa_guard_dummy -DESP32 -DCORE_DEBUG_LEVEL=0 -DARDUINO_RUNNING_CORE=1 -DARDUINO_EVENT_RUNNING_CORE=1 -DBOARD_HAS_PSRAM -DARDUINO_USB_MODE=1 -DARDUINO_USB_CDC_ON_BOOT=1 -DARDUINO_USB_MSC_ON_BOOT=0 -DARDUINO_USB_DFU_ON_BOOT=0 -Wl,--start-group "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\ESP32_S3_IR_System_Arduino.ino.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\data_manager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\ir_controller.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\system_monitor.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\task_manager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\web_server_manager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\websocket_manager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\sketch\\wifi_manager.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiAP.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiClient.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiGeneric.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiMulti.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiSTA.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiScan.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiServer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\WiFi\\WiFiUdp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\SPIFFS\\SPIFFS.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\FS\\FS.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\FS\\vfs_api.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\AsyncEventSource.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\AsyncJson.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\AsyncMessagePack.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\AsyncWebHeader.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\AsyncWebSocket.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\BackPort_SHA1Builder.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\ChunkPrint.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\Middleware.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\WebAuthentication.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\WebHandlers.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\WebRequest.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\WebResponses.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\ESP_Async_WebServer\\WebServer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\Async_TCP\\AsyncTCP.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\Preferences\\Preferences.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRac.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRrecv.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRsend.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRtext.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRtimer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\IRutils.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Airton.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Airwell.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Aiwa.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Amcor.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Argo.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Arris.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Bosch.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Bose.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Carrier.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_ClimaButler.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Coolix.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Corona.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Daikin.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Delonghi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Denon.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Dish.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Doshisha.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Ecoclim.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Electra.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_EliteScreens.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Epson.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Fujitsu.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_GICable.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_GlobalCache.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Goodweather.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Gorenje.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Gree.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Haier.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Hitachi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Inax.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_JVC.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Kelon.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Kelvinator.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_LG.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Lasertag.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Lego.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Lutron.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_MWM.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Magiquest.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Metz.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Midea.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_MilesTag2.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Mirage.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Mitsubishi.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_MitsubishiHeavy.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Multibrackets.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_NEC.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Neoclima.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Nikai.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Panasonic.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Pioneer.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Pronto.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_RC5_RC6.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_RCMM.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Rhoss.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Samsung.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Sanyo.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Sharp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Sherwood.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Sony.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Symphony.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Tcl.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Technibel.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Teco.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Teknopoint.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Toshiba.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Toto.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Transcold.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Trotec.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Truma.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Vestel.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Voltas.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Whirlpool.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Whynter.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Wowwee.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Xmp.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_York.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77\\libraries\\IRremoteESP8266\\ir_Zepeal.cpp.o" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\cores\\a9026acf4f1aed3862acef0498beb7be\\core.a" -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lasio -lbt -lcbor -lunity -lcmock -lcoap -lnghttp -lesp-tls -lesp_adc_cal -lesp_hid -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lesp_https_server -lesp_lcd -lprotobuf-c -lprotocomm -lmdns -lesp_local_ctrl -lsdmmc -lesp_serial_slave_link -lesp_websocket_client -lexpat -lwear_levelling -lfatfs -lfreemodbus -ljsmn -ljson -llibsodium -lmqtt -lopenssl -lperfmon -lspiffs -lusb -lulp -lwifi_provisioning -lrmaker_common -lesp_diagnostics -lrtc_store -lesp_insights -ljson_parser -ljson_generator -lesp_schedule -lespressif__esp_secure_cert_mgr -lesp_rainmaker -lgpio_button -lqrcode -lws2812_led -lesp32-camera -lesp_littlefs -lespressif__esp-dsp -lfb_gfx -lasio -lcmock -lunity -lcoap -lesp_lcd -lesp_websocket_client -lexpat -lfreemodbus -ljsmn -llibsodium -lperfmon -lusb -lesp_adc_cal -lesp_hid -lfatfs -lwear_levelling -lopenssl -lspiffs -lesp_insights -lcbor -lesp_diagnostics -lrtc_store -lesp_rainmaker -lesp_local_ctrl -lesp_https_server -lwifi_provisioning -lprotocomm -lbt -lbtdm_app -lprotobuf-c -lmdns -ljson -ljson_parser -ljson_generator -lesp_schedule -lespressif__esp_secure_cert_mgr -lqrcode -lrmaker_common -lmqtt -larduino_tinyusb -lcat_face_detect -lhuman_face_detect -lcolor_detect -lmfn -ldl -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lesp_ringbuf -lefuse -lesp_ipc -ldriver -lesp_pm -lmbedtls -lapp_update -lbootloader_support -lspi_flash -lnvs_flash -lpthread -lesp_gdbstub -lespcoredump -lesp_phy -lesp_system -lesp_rom -lhal -lvfs -lesp_eth -ltcpip_adapter -lesp_netif -lesp_event -lwpa_supplicant -lesp_wifi -lconsole -llwip -llog -lheap -lsoc -lesp_hw_support -lxtensa -lesp_common -lesp_timer -lfreertos -lnewlib -lcxx -lapp_trace -lnghttp -lesp-tls -ltcp_transport -lesp_http_client -lesp_http_server -lesp_https_ota -lsdmmc -lesp_serial_slave_link -lulp -lmbedtls_2 -lmbedcrypto -lmbedx509 -lcoexist -lcore -lespnow -lmesh -lnet80211 -lpp -lsmartconfig -lwapi -lphy -lbtbb -lesp_phy -lphy -lbtbb -lesp_phy -lphy -lbtbb -lxt_hal -lc -lm -lnewlib -lstdc++ -lpthread -lgcc -lcxx -Wl,--end-group -Wl,-EL -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/ESP32_S3_IR_System_Arduino.ino.elf"
"C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\tools\\esptool_py\\4.5.1/esptool.exe" --chip esp32s3 elf2image --flash_mode dio --flash_freq 80m --flash_size 16MB --elf-sha256-offset 0xb0 -o "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/ESP32_S3_IR_System_Arduino.ino.bin" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/ESP32_S3_IR_System_Arduino.ino.elf"
esptool.py v4.5.1
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
"C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\esp32\\hardware\\esp32\\2.0.17\\tools\\gen_esp32part.exe" -q "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/partitions.csv" "C:\\Users\\<USER>\\AppData\\Local\\arduino\\sketches\\DA42588DD93F5258BAF0CB722215AC77/ESP32_S3_IR_System_Arduino.ino.partitions.bin"
Traceback (most recent call last):
  File "gen_esp32part.py", line 534, in <module>
  File "gen_esp32part.py", line 480, in main
UnicodeDecodeError: 'ascii' codec can't decode byte 0xe8 in position 21: ordinal not in range(128)
Failed to execute script gen_esp32part
Using library WiFi at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi 
Using library SPIFFS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS 
Using library FS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS 
Using library ESP Async WebServer at version 3.7.8 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer 
Using library Async TCP at version 3.4.4 in folder: C:\Users\<USER>\Documents\Arduino\libraries\Async_TCP 
Using library ArduinoJson at version 6.21.3 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson 
Using library Preferences at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences 
Using library IRremoteESP8266 at version 2.8.6 in folder: C:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266 
exit status 0xffffffff

Compilation error: exit status 0xffffffff
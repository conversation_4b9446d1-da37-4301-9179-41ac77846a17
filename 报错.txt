C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp: In constructor 'IRController::IRController()':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:17:18: error: 'IR_TRANSMIT_PIN' was not declared in this scope
     , m_irSender(IR_TRANSMIT_PIN)
                  ^~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:17:18: note: suggested alternative: 'IR_SEND_PIN'
     , m_ir<PERSON><PERSON>(IR_TRANSMIT_PIN)
                  ^~~~~~~~~~~~~~~
                  IR_SEND_PIN
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:9,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp: In instantiation of 'typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type ArduinoJson::V6213PB2::JsonVariantConst::as() const [with T = ArduinoJson::V6213PB2::JsonVariant; typename ArduinoJson::V6213PB2::detail::enable_if<((! ArduinoJson::V6213PB2::detail::is_same<T, char*>::value) && (! ArduinoJson::V6213PB2::detail::is_same<T, char>::value)), T>::type = ArduinoJson::V6213PB2::JsonVariant]':
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:90:18:   required from 'ArduinoJson::V6213PB2::JsonVariantConst::operator T() const [with T = ArduinoJson::V6213PB2::JsonVariant]'
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:246:31:   required from here
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/JsonVariantConst.hpp:75:34: error: invalid use of incomplete type 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonVariant>'
     return Converter<T>::fromJson(*this);
            ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Array/JsonArray.hpp:7,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.hpp:29,
                 from c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson.h:9,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.h:5,
                 from C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem\IRController.cpp:1:
c:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson\src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V6213PB2::detail::InvalidConversion<ArduinoJson::V6213PB2::JsonVariantConst, ArduinoJson::V6213PB2::JsonVariant>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v6/invalid-conversion/
       ^~~~~~~~~~~~~~~~~
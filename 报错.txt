c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\WebServerManager.cpp.o:(.literal._ZN16WebServerManager14handleTasksAPIEP21AsyncWebServerRequest+0x8): undefined reference to `TaskManager::getAllTasksStatus()'
c:/users/<USER>/appdata/local/arduino15/packages/esp32/tools/xtensa-esp32s3-elf-gcc/esp-2021r2-patch5-8.4.0/bin/../lib/gcc/xtensa-esp32s3-elf/8.4.0/../../../../xtensa-esp32s3-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\761F9EE8DC005164447ED3D2D6C74A43\sketch\WebServerManager.cpp.o: in function `WebServerManager::handleTasksAPI(AsyncWebServerRequest*)':
C:\Users\<USER>\Desktop\Arduino_ESP32S3_IRSystem/WebServerManager.cpp:370: undefined reference to `TaskManager::getAllTasksStatus()'
collect2.exe: error: ld returned 1 exit status
Using library WiFi at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\WiFi 
Using library FS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\FS 
Using library SPIFFS at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\SPIFFS 
Using library Async TCP at version 3.4.4 in folder: C:\Users\<USER>\Documents\Arduino\libraries\Async_TCP 
Using library ESP Async WebServer at version 3.7.8 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer 
Using library ArduinoJson at version 6.21.3 in folder: C:\Users\<USER>\Documents\Arduino\libraries\ArduinoJson 
Using library IRremoteESP8266 at version 2.8.6 in folder: C:\Users\<USER>\Documents\Arduino\libraries\IRremoteESP8266 
Using library Time at version 1.6.1 in folder: C:\Users\<USER>\Documents\Arduino\libraries\Time 
Using library Preferences at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Preferences 
Using library Update at version 2.0.0 in folder: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\2.0.17\libraries\Update 
exit status 1

Compilation error: exit status 1
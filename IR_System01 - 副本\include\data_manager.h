/*
 * Data Manager Header - ESP32-S3 IR Control System
 * Fully compatible with ArduinoJson 6.21.3 API
 * Handles signal, timer, and task data with SPIFFS storage
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <vector>

// Signal Data Structure (Frontend Matching)
struct SignalData {
    String id;
    String name;
    String type;           // "tv", "ac", "fan", "light", "other"
    String protocol;       // "NEC", "SONY", "SAMSUNG", etc.
    String data;           // Hex data
    String rawData;        // Raw timing data
    int frequency;         // Carrier frequency
    unsigned long timestamp;
    bool isActive;
    int useCount;
    String description;
    
    SignalData() : frequency(38000), timestamp(0), isActive(true), useCount(0) {}
    
    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

// Timer Data Structure (Frontend Matching)
struct TimerData {
    String id;
    String name;
    String signalId;
    String schedule;       // Cron-like schedule
    bool isActive;
    unsigned long timestamp;
    unsigned long lastExecuted;
    int executionCount;
    
    TimerData() : isActive(false), timestamp(0), lastExecuted(0), executionCount(0) {}
    
    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

// Task Data Structure (Frontend Matching)
struct TaskData {
    String id;
    String name;
    String type;           // "signal_send", "batch_send", "timer_execute"
    String status;         // "pending", "running", "completed", "failed"
    DynamicJsonDocument parameters;
    unsigned long timestamp;
    unsigned long startTime;
    unsigned long endTime;
    String result;
    String error;
    
    TaskData() : parameters(256), timestamp(0), startTime(0), endTime(0) {}
    
    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

class DataManager {
public:
    // Constructor and Destructor
    DataManager();
    ~DataManager();
    
    // Core Methods
    bool initialize();
    
    // Signal Management (Frontend API Matching)
    DynamicJsonDocument getSignalsJSON();
    DynamicJsonDocument getAllSignalsJSON();
    SignalData* getSignal(const String& id);
    String createSignal(const DynamicJsonDocument& signalData);
    bool updateSignal(const String& id, const DynamicJsonDocument& signalData);
    bool deleteSignal(const String& id);
    bool signalExists(const String& id);
    
    // Timer Management (Frontend API Matching)
    DynamicJsonDocument getTimersJSON();
    DynamicJsonDocument getAllTimersJSON();
    TimerData* getTimer(const String& id);
    String createTimer(const String& name, const String& signalId, const String& schedule);
    String createTimer(const DynamicJsonDocument& timerData);
    bool updateTimer(const String& id, const DynamicJsonDocument& timerData);
    bool deleteTimer(const String& id);
    bool timerExists(const String& id);
    bool executeTimer(const String& id);
    
    // Task Management (Frontend API Matching)
    DynamicJsonDocument getTasksJSON();
    TaskData* getTask(const String& id);
    String createTask(const DynamicJsonDocument& taskData);
    bool updateTask(const String& id, const DynamicJsonDocument& taskData);
    bool deleteTask(const String& id);
    bool taskExists(const String& id);
    
    // Statistics (Frontend API Matching)
    int getSignalCount() const;
    int getTimerCount() const;
    int getTaskCount() const;
    int getActiveTimerCount() const;
    int getPendingTaskCount() const;
    int getCompletedTaskCount() const;
    
    // System Configuration
    DynamicJsonDocument getSystemConfig();
    bool setSystemConfig(const DynamicJsonDocument& config);
    String getConfigValue(const String& key, const String& defaultValue = "");
    bool setConfigValue(const String& key, const String& value);
    
    // Data Management
    bool backupData();
    bool restoreData();
    DynamicJsonDocument exportAllData();
    bool importAllData(const DynamicJsonDocument& data);
    void clearAllSignals();
    void clearAllTimers();
    void clearAllTasks();
    void clearAllData();
    
    // File System
    size_t getUsedSpace();
    size_t getFreeSpace();

private:
    // Data Storage
    std::vector<SignalData> signals;
    std::vector<TimerData> timers;
    std::vector<TaskData> tasks;
    DynamicJsonDocument systemConfig;
    
    // Preferences for configuration
    Preferences* prefs;
    
    // File Paths
    static const char* SIGNALS_FILE;
    static const char* TIMERS_FILE;
    static const char* TASKS_FILE;
    static const char* CONFIG_FILE;
    
    // File Operations
    bool checkFileSystem();
    bool createDataDirectories();
    bool loadSignalsFromFile();
    bool saveSignalsToFile();
    bool loadTimersFromFile();
    bool saveTimersToFile();
    bool loadTasksFromFile();
    bool saveTasksToFile();
    bool loadSystemConfig();
    bool saveSystemConfig();
    
    // Helper Methods
    int findSignalIndex(const String& id);
    int findTimerIndex(const String& id);
    int findTaskIndex(const String& id);
    bool validateSignalData(const SignalData& signal);
    bool validateTimerData(const TimerData& timer);
    bool validateTaskData(const TaskData& task);
    String generateUniqueId(const String& prefix);
};

#endif // DATA_MANAGER_H

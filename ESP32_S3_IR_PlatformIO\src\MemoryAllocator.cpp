#include "MemoryAllocator.h"

// ==================== 静态成员初始化 ====================
size_t MemoryAllocator::s_totalAllocations = 0;
size_t MemoryAllocator::s_totalDeallocations = 0;
size_t MemoryAllocator::s_currentAllocatedSize = 0;
size_t MemoryAllocator::s_peakMemoryUsage = 0;
size_t MemoryAllocator::s_heapUsage = 0;

MemoryAllocator::MemoryPool* MemoryAllocator::s_memoryPools = nullptr;
size_t MemoryAllocator::s_poolCount = 0;

#ifdef DEBUG_MODE
bool MemoryAllocator::s_debugMode = false;
MemoryAllocator::AllocationInfo* MemoryAllocator::s_allocations = nullptr;
size_t MemoryAllocator::s_allocationCount = 0;
size_t MemoryAllocator::s_maxAllocations = 100;
#endif

// ==================== 初始化方法 ====================
bool MemoryAllocator::initialize() {
    Serial.println("🧠 Initializing MemoryAllocator (RAM Only Mode)...");
    
    // 初始化内存池
    if (!initializeMemoryPools()) {
        Serial.println("❌ Failed to initialize memory pools");
        return false;
    }
    
#ifdef DEBUG_MODE
    // 初始化调试分配跟踪
    if (!s_allocations) {
        s_allocations = (AllocationInfo*)malloc(s_maxAllocations * sizeof(AllocationInfo));
        if (!s_allocations) {
            Serial.println("❌ Failed to initialize allocation tracking");
            return false;
        }
        s_allocationCount = 0;
    }
#endif
    
    Serial.println("✅ MemoryAllocator initialized successfully (RAM Only)");
    return true;
}

// ==================== 核心内存分配接口 ====================
void* MemoryAllocator::smartAlloc(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    
    // 纯RAM分配策略 - 只使用普通RAM
    ptr = malloc(size);
    if (ptr) {
        s_heapUsage += size;
    }
    
    // 更新统计信息
    if (ptr) {
        s_totalAllocations++;
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        recordAllocation(ptr, size);
        DEBUG_ALLOC(ptr, size);
    }
    
    return ptr;
}

void MemoryAllocator::smartFree(void* ptr) {
    if (!ptr) {
        return;
    }
    
    // 记录释放信息
    recordDeallocation(ptr);
    DEBUG_FREE(ptr);
    
    // 释放内存
    free(ptr);
    
    s_totalDeallocations++;
}

void* MemoryAllocator::smartRealloc(void* ptr, size_t newSize) {
    if (newSize == 0) {
        smartFree(ptr);
        return nullptr;
    }
    
    if (!ptr) {
        return smartAlloc(newSize);
    }
    
    void* newPtr = realloc(ptr, newSize);
    if (newPtr) {
        // 更新统计信息
        recordDeallocation(ptr);
        recordAllocation(newPtr, newSize);
    }
    
    return newPtr;
}

void* MemoryAllocator::smartCalloc(size_t count, size_t size) {
    size_t totalSize = count * size;
    void* ptr = smartAlloc(totalSize);
    if (ptr) {
        memset(ptr, 0, totalSize);
    }
    return ptr;
}

// ==================== 系统生命周期 ====================
void MemoryAllocator::shutdown() {
    Serial.println("🧠 Shutting down MemoryAllocator...");
    
    // 清理内存池
    cleanupMemoryPools();
    
#ifdef DEBUG_MODE
    // 清理调试信息
    if (s_allocations) {
        free(s_allocations);
        s_allocations = nullptr;
    }
#endif
    
    Serial.println("✅ MemoryAllocator shutdown completed");
}

// ==================== 内存统计接口 ====================
size_t MemoryAllocator::getTotalAllocations() {
    return s_totalAllocations;
}

size_t MemoryAllocator::getTotalDeallocations() {
    return s_totalDeallocations;
}

size_t MemoryAllocator::getCurrentAllocatedSize() {
    return s_currentAllocatedSize;
}

size_t MemoryAllocator::getPeakMemoryUsage() {
    return s_peakMemoryUsage;
}

size_t MemoryAllocator::getHeapUsage() {
    return s_heapUsage;
}

// ==================== 内存清理接口 ====================
void MemoryAllocator::performCleanup() {
    Serial.println("🧹 Performing memory cleanup...");
    
    // 强制垃圾回收
    forceGarbageCollection();
    
    // 清理内存池
    cleanupMemoryPools();
    
    Serial.printf("✅ Memory cleanup completed. Free heap: %d KB\n", 
                 ESP.getFreeHeap() / 1024);
}

void MemoryAllocator::defragmentMemory() {
    // 简化实现：在RAM模式下内存碎片整理有限
    Serial.println("🔧 Memory defragmentation (limited in RAM mode)");
}

void MemoryAllocator::forceGarbageCollection() {
    // 简化实现：ESP32没有显式垃圾回收
    Serial.println("🗑️ Force garbage collection (no-op on ESP32)");
}

// ==================== 内存池管理 ====================
void* MemoryAllocator::allocFromPool(size_t size) {
    // 简化实现：直接使用smartAlloc
    return smartAlloc(size);
}

void MemoryAllocator::freeToPool(void* ptr, size_t size) {
    // 简化实现：直接释放
    smartFree(ptr);
}

void MemoryAllocator::cleanupMemoryPools() {
    // 简化实现：无需清理
}

// ==================== 内存健康检查 ====================
bool MemoryAllocator::checkMemoryLeaks() {
    return s_totalAllocations == s_totalDeallocations;
}

float MemoryAllocator::getFragmentationLevel() {
    // 简化实现：返回估算值
    size_t totalFree = ESP.getFreeHeap();
    size_t largestBlock = ESP.getMaxAllocHeap();
    
    if (totalFree == 0) return 0.0f;
    return 1.0f - (float)largestBlock / totalFree;
}

bool MemoryAllocator::checkMemoryIntegrity() {
    // 简化实现：假设内存总是完整的
    return true;
}

// ==================== 调试接口 ====================
void MemoryAllocator::printMemoryStats() {
    Serial.println("📊 Memory Statistics:");
    Serial.printf("  Total Allocations: %d\n", s_totalAllocations);
    Serial.printf("  Total Deallocations: %d\n", s_totalDeallocations);
    Serial.printf("  Current Allocated: %d KB\n", s_currentAllocatedSize / 1024);
    Serial.printf("  Peak Usage: %d KB\n", s_peakMemoryUsage / 1024);
    Serial.printf("  Heap Usage: %d KB\n", s_heapUsage / 1024);
    Serial.printf("  Free Heap: %d KB\n", ESP.getFreeHeap() / 1024);
    Serial.printf("  Fragmentation: %.1f%%\n", getFragmentationLevel() * 100);
}

void MemoryAllocator::printMemoryDetails() {
    printMemoryStats();
    Serial.printf("  Largest Free Block: %d KB\n", ESP.getMaxAllocHeap() / 1024);
    Serial.printf("  Memory Leaks: %s\n", checkMemoryLeaks() ? "None" : "Detected");
}

void MemoryAllocator::enableDebugMode(bool enable) {
#ifdef DEBUG_MODE
    s_debugMode = enable;
    Serial.printf("🐛 Debug mode %s\n", enable ? "enabled" : "disabled");
#endif
}

// ==================== 私有方法实现 ====================
int MemoryAllocator::getBestAllocationStrategy(size_t size) {
    // 纯RAM模式：总是返回堆分配策略
    return 0; // Heap
}

bool MemoryAllocator::isMemoryUnderPressure() {
    size_t freeHeap = ESP.getFreeHeap();
    return freeHeap < MIN_FREE_HEAP;
}

bool MemoryAllocator::initializeMemoryPools() {
    // 简化实现：不使用内存池
    s_memoryPools = nullptr;
    s_poolCount = 0;
    return true;
}

void MemoryAllocator::recordAllocation(void* ptr, size_t size) {
#ifdef DEBUG_MODE
    if (s_debugMode && s_allocations && s_allocationCount < s_maxAllocations) {
        s_allocations[s_allocationCount] = {
            .ptr = ptr,
            .size = size,
            .timestamp = millis(),
            .file = nullptr,
            .line = 0
        };
        s_allocationCount++;
    }
#endif
}

void MemoryAllocator::recordDeallocation(void* ptr) {
#ifdef DEBUG_MODE
    if (s_debugMode && s_allocations) {
        // 查找并移除分配记录
        for (size_t i = 0; i < s_allocationCount; i++) {
            if (s_allocations[i].ptr == ptr) {
                // 移除记录
                for (size_t j = i; j < s_allocationCount - 1; j++) {
                    s_allocations[j] = s_allocations[j + 1];
                }
                s_allocationCount--;
                break;
            }
        }
    }
#endif
}

void MemoryAllocator::updateStats(size_t size, bool isAllocation) {
    if (isAllocation) {
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
    } else {
        if (s_currentAllocatedSize >= size) {
            s_currentAllocatedSize -= size;
        }
    }
}

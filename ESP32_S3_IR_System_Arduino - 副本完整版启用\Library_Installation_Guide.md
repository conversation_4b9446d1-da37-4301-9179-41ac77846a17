# Arduino IDE Library Installation Guide

## 📚 Required Libraries Installation

### Method 1: Arduino IDE Library Manager (Recommended)

1. **Open Arduino IDE**
2. **Go to:** Sketch → Include Library → Manage Libraries
3. **Install the following libraries:**

#### 1. Ard<PERSON>o<PERSON>son (Version 6.21.3)
- **Search:** "ArduinoJson"
- **Author:** <PERSON><PERSON>
- **Version:** 6.21.3 (exact version required)
- **Click:** Install

#### 2. ESPAsyncWebServer
- **Search:** "ESP Async WebServer"
- **Author:** lacamera
- **Version:** 3.7.8 or higher
- **Click:** Install

#### 3. AsyncTCP
- **Search:** "AsyncTCP"
- **Author:** dvarrel
- **Version:** 3.4.4 or higher
- **Click:** Install

#### 4. IRremoteESP8266
- **Search:** "IRremoteESP8266"
- **Author:** <PERSON>
- **Version:** 2.8.6
- **Click:** Install

### Method 2: Manual Installation (If Library Manager fails)

#### Arduino<PERSON>son 6.21.3
1. Download from: https://github.com/bblanchon/ArduinoJson/releases/tag/v6.21.3
2. Extract to: `Documents/Arduino/libraries/ArduinoJson`

#### ESPAsyncWebServer
1. Download from: https://github.com/lacamera/ESPAsyncWebServer
2. Extract to: `Documents/Arduino/libraries/ESPAsyncWebServer`

#### AsyncTCP
1. Download from: https://github.com/dvarrel/AsyncTCP
2. Extract to: `Documents/Arduino/libraries/AsyncTCP`

#### IRremoteESP8266
1. Download from: https://github.com/crankyoldgit/IRremoteESP8266/releases/tag/v2.8.6
2. Extract to: `Documents/Arduino/libraries/IRremoteESP8266`

### 🔧 ESP32 Board Package Installation

1. **Open Arduino IDE**
2. **Go to:** File → Preferences
3. **Add to Additional Board Manager URLs:**
   ```
   https://espressif.github.io/arduino-esp32/package_esp32_index.json
   ```
4. **Go to:** Tools → Board → Boards Manager
5. **Search:** "esp32"
6. **Install:** "esp32 by Espressif Systems" (Version 2.0.11 or higher)

### 📁 SPIFFS Upload Tool Installation

1. **Download:** ESP32 Sketch Data Upload tool
   - GitHub: https://github.com/lorol/arduino-esp32fs-plugin
2. **Extract to:** `Documents/Arduino/tools/ESP32FS/tool/esp32fs.jar`
3. **Restart Arduino IDE**
4. **Verify:** Tools menu should show "ESP32 Sketch Data Upload"

### ✅ Verification Steps

After installation, verify by:

1. **Open:** ESP32_S3_IR_System_Arduino.ino
2. **Go to:** Sketch → Verify/Compile
3. **Check:** No compilation errors
4. **Expected output:** "Done compiling"

### 🚨 Common Issues and Solutions

#### Issue: "ArduinoJson.h: No such file or directory"
**Solution:** Reinstall ArduinoJson library, ensure version 6.21.3

#### Issue: "AsyncTCP.h: No such file or directory"
**Solution:** Install AsyncTCP library, restart Arduino IDE

#### Issue: "ESPAsyncWebServer.h: No such file or directory"
**Solution:** Install ESPAsyncWebServer library

#### Issue: "IRremoteESP8266.h: No such file or directory"
**Solution:** Install IRremoteESP8266 library version 2.8.6

#### Issue: Board not found
**Solution:** Install ESP32 board package, restart Arduino IDE

#### Issue: SPIFFS upload option missing
**Solution:** Install ESP32FS plugin, restart Arduino IDE

### 📋 Library Compatibility Matrix

| Library | Version | ESP32-S3 | Arduino IDE |
|---------|---------|----------|-------------|
| ArduinoJson | 6.21.3 | ✅ | 1.8.19+ |
| ESPAsyncWebServer | 3.7.8+ | ✅ | 1.8.19+ |
| AsyncTCP | 3.4.4+ | ✅ | 1.8.19+ |
| IRremoteESP8266 | 2.8.6 | ✅ | 1.8.19+ |
| ESP32 Core | 2.0.11+ | ✅ | 1.8.19+ |

### 🔄 Update Process

To update libraries:
1. **Go to:** Sketch → Include Library → Manage Libraries
2. **Filter:** "Updatable"
3. **Update:** Required libraries to latest compatible versions
4. **Restart:** Arduino IDE after updates

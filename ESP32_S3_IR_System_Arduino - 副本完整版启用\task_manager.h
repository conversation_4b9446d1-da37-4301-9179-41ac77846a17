/*
 * Task Manager Header - ESP32-S3 IR Control System
 * Fully compatible with ArduinoJson 6.21.3 API
 * Handles task creation, execution, and management with priority queuing
 */

#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <functional>

// Forward declarations
class DataManager;
class IRController;
class WebSocketManager;

// Task Types (Frontend Matching)
enum class TaskType {
    SIGNAL_SEND = 1,
    BATCH_SEND = 2,
    TIMER_EXECUTE = 3,
    SYSTEM_COMMAND = 4,
    CUSTOM = 5
};

// Task Status (Frontend Matching)
enum class TaskStatus {
    PENDING = 1,
    RUNNING = 2,
    COMPLETED = 3,
    FAILED = 4,
    CANCELLED = 5,
    PAUSED = 6
};

// Task Priority (Avoiding ESP32 macro conflicts)
enum class TaskPriority {
    TASK_LOW = 1,      // Renamed to avoid conflict with ESP32 LOW macro
    NORMAL = 2,
    TASK_HIGH = 3,     // Renamed to avoid conflict with ESP32 HIGH macro
    URGENT = 4
};

// Task Structure (Frontend Matching)
struct Task {
    String id;
    String name;
    TaskType type;
    TaskStatus status;
    TaskPriority priority;
    DynamicJsonDocument parameters;
    unsigned long createdAt;
    unsigned long startedAt;
    unsigned long completedAt;
    String result;
    String error;
    
    Task() : type(TaskType::CUSTOM), status(TaskStatus::PENDING), 
             priority(TaskPriority::NORMAL), parameters(512),
             createdAt(0), startedAt(0), completedAt(0) {}
    
    // ArduinoJson 6.21.3 Compatible Methods
    DynamicJsonDocument toJson() const;
    bool fromJson(const DynamicJsonDocument& doc);
};

class TaskManager {
public:
    // Constructor and Destructor
    TaskManager();
    ~TaskManager();
    
    // Core Methods
    bool initialize();
    void handleLoop();
    
    // Module Reference Setters
    void setDataManager(DataManager* dm);
    void setIRController(IRController* ir);
    void setWebSocketManager(WebSocketManager* wsm);
    
    // Task Creation (Frontend API Matching)
    String createTask(const String& name, TaskType type, const DynamicJsonDocument& parameters, 
                     TaskPriority priority = TaskPriority::NORMAL);
    String createSignalSendTask(const String& signalId, TaskPriority priority = TaskPriority::NORMAL);
    String createBatchSendTask(const std::vector<String>& signalIds, int delayMs = 500, 
                              TaskPriority priority = TaskPriority::NORMAL);
    String createTimerTask(const String& timerId, TaskPriority priority = TaskPriority::NORMAL);
    String createSystemCommandTask(const String& command, TaskPriority priority = TaskPriority::NORMAL);
    
    // Task Control (Frontend API Matching)
    bool startTask(const String& taskId);
    bool pauseTask(const String& taskId);
    bool resumeTask(const String& taskId);
    bool cancelTask(const String& taskId);
    bool deleteTask(const String& taskId);
    
    // Task Queries (Frontend API Matching)
    Task* getTask(const String& taskId);
    std::vector<Task> getAllTasks();
    std::vector<Task> getTasksByStatus(TaskStatus status);
    std::vector<Task> getTasksByPriority(TaskPriority priority);
    DynamicJsonDocument getTasksJSON();
    
    // Queue Management
    void clearQueue();
    void clearCompletedTasks();
    void clearAllTasks();
    
    // Status Queries (Frontend API Matching)
    int getActiveTaskCount() const;
    int getPendingTaskCount() const;
    int getCompletedTaskCount() const;
    int getFailedTaskCount() const;
    int getQueueSize() const { return taskQueue.size(); }
    bool isProcessing() const { return m_isProcessing; }
    
    // Statistics
    DynamicJsonDocument getStatistics() const;
    
    // Event Callbacks (Frontend Integration)
    void setOnTaskStarted(std::function<void(const String&)> callback);
    void setOnTaskCompleted(std::function<void(const String&, bool)> callback);
    void setOnTaskFailed(std::function<void(const String&, const String&)> callback);
    
    // Configuration
    void setMaxConcurrentTasks(int maxTasks);
    void setTaskTimeout(unsigned long timeoutMs);

private:
    // Task Storage
    std::vector<Task> tasks;
    std::vector<Task> taskQueue;
    Task* currentTask;
    bool m_isProcessing;
    
    // Module References
    DataManager* dataManager;
    IRController* irController;
    WebSocketManager* wsManager;
    
    // Statistics
    unsigned long totalTasksCreated;
    unsigned long totalTasksCompleted;
    unsigned long totalTasksFailed;
    
    // Configuration
    int maxConcurrentTasks;
    unsigned long taskTimeoutMs;
    
    // Event Callbacks
    std::function<void(const String&)> onTaskStarted;
    std::function<void(const String&, bool)> onTaskCompleted;
    std::function<void(const String&, const String&)> onTaskFailed;
    
    // Internal Methods
    String generateTaskId();
    bool validateTask(const Task& task);
    void sortTaskQueue();
    bool executeTask(Task& task);
    void completeTask(Task& task, bool success);
    void failTask(Task& task, const String& error);
    
    // Task Execution Methods
    bool executeSignalSendTask(const Task& task);
    bool executeBatchSendTask(const Task& task);
    bool executeTimerTask(const Task& task);
    bool executeSystemCommandTask(const Task& task);
    bool executeCustomTask(const Task& task);
    
    // Helper Methods
    int findTaskIndex(const String& taskId);
    bool isValidTaskType(TaskType type);
    bool isValidTaskStatus(TaskStatus status);
    bool isValidTaskPriority(TaskPriority priority);
    
    // Constants
    static const int DEFAULT_MAX_CONCURRENT_TASKS = 1;
    static const unsigned long DEFAULT_TASK_TIMEOUT = 30000; // 30 seconds
    static const size_t MAX_TASK_HISTORY = 100;
};

#endif // TASK_MANAGER_H

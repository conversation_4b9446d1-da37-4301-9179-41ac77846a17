# ESP32板包全新安装指南

## 🎯 重置后的完整安装步骤

### 第一步：启动Arduino IDE
1. **启动Arduino IDE**
2. **等待完全加载**

### 第二步：配置板包URL
1. **File → Preferences**
2. **Additional Board Manager URLs** 添加：
   ```
   https://espressif.github.io/arduino-esp32/package_esp32_index.json
   ```
3. **点击 OK**

### 第三步：安装ESP32板包
1. **Tools → Board → Boards Manager**
2. **搜索：** `esp32`
3. **选择：** `esp32 by Espressif Systems`
4. **版本选择：** 
   - **推荐：** `2.0.17` (稳定版本)
   - **或：** `3.2.0` (最新版本)
5. **点击 Install**
6. **等待安装完成**

### 第四步：验证安装
1. **Tools → Board → ESP32 Arduino**
2. **确认看到：** `ESP32S3 Dev Module`

### 第五步：配置ESP32-S3设置
```
Tools → Board → ESP32 Arduino → ESP32S3 Dev Module
Tools → Upload Speed → 460800
Tools → USB Mode → Hardware CDC and JTAG
Tools → USB CDC On Boot → Enabled
Tools → CPU Frequency → 240MHz (WiFi)
Tools → Flash Mode → QIO 80MHz  ⚠️ 关键
Tools → Flash Size → 16MB (128Mb)  ⚠️ 关键
Tools → PSRAM → OPI PSRAM  ⚠️ 关键
Tools → Partition Scheme → Default 4MB with spiffs
Tools → Core Debug Level → None
```

### 第六步：测试PSRAM
使用您的测试代码：
```cpp
void setup() {
  Serial.begin(115200);
  delay(1000);
  if (psramFound()) {
    Serial.println("✅ PSRAM FOUND!");
    Serial.printf("PSRAM size: %d bytes\n", ESP.getPsramSize());
  } else {
    Serial.println("❌ PSRAM NOT FOUND!");
  }
}

void loop() {}
```

### 第七步：验证编译参数
编译时应该看到：
```
--flash_mode qio
--flash_freq 80m
PSRAM=opi
```

## 🚨 如果仍然有问题

### 问题1：Flash Mode仍然是DIO
**解决方案：**
1. 尝试ESP32板包版本 `2.0.17`
2. 或创建 `build_opt.h` 文件强制QIO

### 问题2：PSRAM仍然检测不到
**解决方案：**
1. 检查硬件连接
2. 尝试不同的USB线
3. 检查COM端口

### 问题3：编译错误
**解决方案：**
1. 重启Arduino IDE
2. 重新选择板子
3. 清理项目缓存

## 📞 支持
如果按照此指南操作后仍有问题，请提供：
1. ESP32板包版本
2. 编译输出日志
3. 具体错误信息

/*
 * WiFi Manager Implementation - ESP32-S3 IR Control System
 * Handles WiFi connection, AP mode, and network configuration
 * Compatible with ESP32-S3 hardware and latest WiFi libraries
 */

#include "wifi_manager.h"

WiFiManager::WiFiManager() {
    isConnected = false;
    isAPMode = false;
    lastConnectionAttempt = 0;
    connectionAttempts = 0;
    
    // Default AP credentials
    apSSID = "ESP32-S3-IR-System";
    apPassword = "12345678";
    
    // Clear callbacks
    onConnected = nullptr;
    onDisconnected = nullptr;
    onAPStarted = nullptr;
}

WiFiManager::~WiFiManager() {
    disconnect();
}

bool WiFiManager::initialize() {
    Serial.println("Initializing WiFi Manager...");
    
    // Set WiFi mode to dual mode (STA + AP)
    WiFi.mode(WIFI_AP_STA);
    
    // <PERSON>ad saved WiFi configuration
    loadWiFiConfig();
    
    Serial.println("WiFi Manager initialized successfully");
    return true;
}

void WiFiManager::handleLoop() {
    // Handle WiFi events
    handleWiFiEvent();
    
    // Auto-reconnection logic
    if (!isConnected && !ssid.isEmpty() && !isAPMode) {
        unsigned long currentTime = millis();
        if (currentTime - lastConnectionAttempt > RETRY_INTERVAL) {
            if (connectionAttempts < MAX_CONNECTION_ATTEMPTS) {
                Serial.printf("Attempting WiFi reconnection (%d/%d)...\n", 
                             connectionAttempts + 1, MAX_CONNECTION_ATTEMPTS);
                connectToWiFi();
            } else {
                Serial.println("Max connection attempts reached, starting AP mode");
                startAPMode();
            }
        }
    }
}

bool WiFiManager::startConnection() {
    Serial.println("Starting WiFi connection process...");
    
    // Try to connect to saved WiFi first
    if (!ssid.isEmpty()) {
        Serial.printf("Attempting to connect to saved WiFi: %s\n", ssid.c_str());
        return connectToWiFi();
    } else {
        Serial.println("No saved WiFi credentials, starting AP mode");
        return startAPMode();
    }
}

void WiFiManager::disconnect() {
    if (isConnected) {
        WiFi.disconnect();
        isConnected = false;
        Serial.println("WiFi disconnected");
        
        if (onDisconnected) {
            onDisconnected();
        }
    }
}

void WiFiManager::reconnect() {
    if (!ssid.isEmpty()) {
        disconnect();
        delay(1000);
        connectToWiFi();
    }
}

bool WiFiManager::setWiFiCredentials(const String& newSSID, const String& newPassword) {
    if (!validateCredentials(newSSID, newPassword)) {
        return false;
    }
    
    ssid = newSSID;
    password = newPassword;
    
    // Save to preferences
    saveWiFiConfig();
    
    Serial.printf("WiFi credentials updated: %s\n", ssid.c_str());
    return true;
}

bool WiFiManager::setAPCredentials(const String& newSSID, const String& newPassword) {
    if (!validateCredentials(newSSID, newPassword)) {
        return false;
    }
    
    apSSID = newSSID;
    apPassword = newPassword;
    
    Serial.printf("AP credentials updated: %s\n", apSSID.c_str());
    return true;
}

bool WiFiManager::connectToWiFi() {
    if (ssid.isEmpty()) {
        Serial.println("ERROR: No WiFi SSID configured");
        return false;
    }
    
    Serial.printf("Connecting to WiFi: %s\n", ssid.c_str());
    
    lastConnectionAttempt = millis();
    connectionAttempts++;
    
    // Configure WiFi
    WiFi.begin(ssid.c_str(), password.c_str());
    
    // Wait for connection with timeout
    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - startTime < CONNECTION_TIMEOUT) {
        delay(500);
        Serial.print(".");
    }
    Serial.println();
    
    if (WiFi.status() == WL_CONNECTED) {
        isConnected = true;
        connectionAttempts = 0; // Reset attempts on success
        
        Serial.println("WiFi connected successfully!");
        Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
        
        if (onConnected) {
            onConnected();
        }
        
        return true;
    } else {
        Serial.printf("WiFi connection failed (Status: %d)\n", WiFi.status());
        return false;
    }
}

bool WiFiManager::startAPMode() {
    Serial.printf("Starting AP mode: %s\n", apSSID.c_str());
    
    // Configure AP
    bool success = WiFi.softAP(apSSID.c_str(), apPassword.c_str());
    
    if (success) {
        isAPMode = true;
        
        Serial.println("AP mode started successfully!");
        Serial.printf("AP SSID: %s\n", apSSID.c_str());
        Serial.printf("AP Password: %s\n", apPassword.c_str());
        Serial.printf("AP IP Address: %s\n", WiFi.softAPIP().toString().c_str());
        
        if (onAPStarted) {
            onAPStarted();
        }
        
        return true;
    } else {
        Serial.println("ERROR: Failed to start AP mode");
        return false;
    }
}

void WiFiManager::stopAPMode() {
    if (isAPMode) {
        WiFi.softAPdisconnect(true);
        isAPMode = false;
        Serial.println("AP mode stopped");
    }
}

String WiFiManager::getLocalIP() const {
    if (isConnected) {
        return WiFi.localIP().toString();
    }
    return "";
}

String WiFiManager::getAPIP() const {
    if (isAPMode) {
        return WiFi.softAPIP().toString();
    }
    return "";
}

String WiFiManager::getSSID() const {
    if (isConnected) {
        return WiFi.SSID();
    }
    return ssid;
}

int WiFiManager::getRSSI() const {
    if (isConnected) {
        return WiFi.RSSI();
    }
    return 0;
}

DynamicJsonDocument WiFiManager::getNetworkInfo() const {
    DynamicJsonDocument doc(512);
    
    doc["wifi_connected"] = isConnected;
    doc["ap_mode"] = isAPMode;
    
    if (isConnected) {
        doc["wifi_ssid"] = WiFi.SSID();
        doc["wifi_ip"] = WiFi.localIP().toString();
        doc["wifi_rssi"] = WiFi.RSSI();
        doc["wifi_channel"] = WiFi.channel();
        doc["wifi_mac"] = WiFi.macAddress();
    }
    
    if (isAPMode) {
        doc["ap_ssid"] = apSSID;
        doc["ap_ip"] = WiFi.softAPIP().toString();
        doc["ap_clients"] = WiFi.softAPgetStationNum();
    }
    
    return doc;
}

DynamicJsonDocument WiFiManager::getWiFiStatus() const {
    DynamicJsonDocument doc(256);
    
    doc["connected"] = isConnected;
    doc["status"] = WiFi.status();
    doc["attempts"] = connectionAttempts;
    doc["last_attempt"] = lastConnectionAttempt;
    
    if (isConnected) {
        doc["ip"] = WiFi.localIP().toString();
        doc["rssi"] = WiFi.RSSI();
        doc["ssid"] = WiFi.SSID();
    }
    
    return doc;
}

DynamicJsonDocument WiFiManager::scanNetworks() {
    DynamicJsonDocument doc(2048);
    
    Serial.println("Scanning for WiFi networks...");
    int networkCount = WiFi.scanNetworks();
    
    if (networkCount == 0) {
        Serial.println("No networks found");
        doc["count"] = 0;
        return doc;
    }
    
    Serial.printf("Found %d networks\n", networkCount);
    
    JsonArray networks = doc.createNestedArray("networks");
    doc["count"] = networkCount;
    
    for (int i = 0; i < networkCount; i++) {
        DynamicJsonDocument network(128);
        network["ssid"] = WiFi.SSID(i);
        network["rssi"] = WiFi.RSSI(i);
        network["encryption"] = (WiFi.encryptionType(i) == WIFI_AUTH_OPEN) ? "open" : "encrypted";
        network["channel"] = WiFi.channel(i);
        
        networks.add(network);
    }
    
    // Clean up scan results
    WiFi.scanDelete();
    
    return doc;
}

void WiFiManager::loadWiFiConfig() {
    Preferences prefs;
    if (prefs.begin("wifi", true)) {
        ssid = prefs.getString("ssid", "");
        password = prefs.getString("password", "");
        prefs.end();
        
        if (!ssid.isEmpty()) {
            Serial.printf("Loaded WiFi config: %s\n", ssid.c_str());
        }
    }
}

void WiFiManager::saveWiFiConfig() {
    Preferences prefs;
    if (prefs.begin("wifi", false)) {
        prefs.putString("ssid", ssid);
        prefs.putString("password", password);
        prefs.end();
        
        Serial.println("WiFi configuration saved");
    }
}

void WiFiManager::resetWiFiSettings() {
    Preferences prefs;
    if (prefs.begin("wifi", false)) {
        prefs.clear();
        prefs.end();
    }
    
    ssid = "";
    password = "";
    
    Serial.println("WiFi settings reset");
}

void WiFiManager::handleWiFiEvent() {
    static bool wasConnected = false;
    
    bool currentlyConnected = (WiFi.status() == WL_CONNECTED);
    
    // Check for connection state changes
    if (currentlyConnected != wasConnected) {
        if (currentlyConnected) {
            isConnected = true;
            connectionAttempts = 0;
            
            if (onConnected) {
                onConnected();
            }
        } else {
            isConnected = false;
            
            if (onDisconnected) {
                onDisconnected();
            }
        }
        
        wasConnected = currentlyConnected;
    }
}

bool WiFiManager::validateCredentials(const String& ssid, const String& password) {
    if (ssid.isEmpty()) {
        Serial.println("ERROR: SSID cannot be empty");
        return false;
    }
    
    if (ssid.length() > 32) {
        Serial.println("ERROR: SSID too long (max 32 characters)");
        return false;
    }
    
    if (password.length() > 0 && password.length() < 8) {
        Serial.println("ERROR: Password too short (min 8 characters)");
        return false;
    }
    
    if (password.length() > 63) {
        Serial.println("ERROR: Password too long (max 63 characters)");
        return false;
    }
    
    return true;
}

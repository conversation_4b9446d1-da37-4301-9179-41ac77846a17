/*
 * WiFiManager.h - WiFi Management with Zero Global Memory Allocation
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <ArduinoJson.h>

class WiFiManager {
public:
    WiFiManager();
    ~WiFiManager();
    
    bool initialize();
    void handleLoop();
    
    // Connection management
    void startConnection();
    void disconnect();
    bool isConnected() const;
    
    // AP mode
    void startAP();
    void stopAP();
    bool isAPActive() const;
    
    // Status
    DynamicJsonDocument getStatus() const;
    String getLocalIP() const;
    int getRSSI() const;

private:
    bool m_initialized;
    bool m_apMode;
    unsigned long m_lastConnectionAttempt;
    int m_connectionAttempts;
    
    static const int MAX_CONNECTION_ATTEMPTS = 3;
    static const unsigned long CONNECTION_TIMEOUT = 30000;
    
    void attemptConnection();
    void handleConnectionTimeout();
};

#endif // WIFI_MANAGER_H

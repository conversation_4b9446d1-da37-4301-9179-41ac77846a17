#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>

#include "PSRAMManager.h"
#include "MemoryAllocator.h"
#include "system-config.h"

// Forward declarations
class DataManager;
class IRController;
class WebServerManager;
class TaskManager;
class WSManager;
class NetworkSecurity;

/**
 * 系统管理器类
 * 
 * 负责整个ESP32-S3 IR系统的核心管理
 * 协调各个组件的初始化、运行和销毁
 * 
 * 核心功能：
 * - 系统初始化和启动
 * - 组件生命周期管理
 * - 系统模式管理（高性能/标准）
 * - 错误处理和恢复
 * - 内存监控和管理
 * - 系统状态监控
 */
class SystemManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param capacity 系统容量配置
     */
    explicit SystemManager(const SystemCapacity& capacity);
    
    /**
     * 析构函数
     */
    ~SystemManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化系统
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 启动系统
     * @return bool 启动是否成功
     */
    bool start();
    
    /**
     * 停止系统
     */
    void stop();
    
    /**
     * 关闭系统
     */
    void shutdown();
    
    /**
     * 重启系统
     * @return bool 重启是否成功
     */
    bool restart();
    
    /**
     * 检查系统健康状态
     * @return bool 系统是否健康
     */
    bool isHealthy() const;
    
    /**
     * 紧急停止
     */
    void emergencyStop();
    
    // ==================== 主循环 ====================
    
    /**
     * 主循环处理
     * 应在Arduino主循环中调用
     */
    void loop();
    
    /**
     * 更新系统状态
     */
    void updateSystemStatus();
    
    /**
     * 处理系统事件
     */
    void processSystemEvents();
    
    // ==================== 网络管理 ====================
    
    /**
     * 初始化WiFi连接
     * @return bool WiFi初始化是否成功
     */
    bool initializeWiFi();
    
    /**
     * 连接到WiFi
     * @param ssid WiFi名称
     * @param password WiFi密码
     * @return bool 连接是否成功
     */
    bool connectToWiFi(const String& ssid, const String& password);
    
    /**
     * 断开WiFi连接
     */
    void disconnectWiFi();
    
    /**
     * 检查WiFi连接状态
     * @return bool WiFi是否已连接
     */
    bool isWiFiConnected() const;
    
    /**
     * 获取WiFi状态信息
     * @return DynamicJsonDocument WiFi状态
     */
    DynamicJsonDocument getWiFiStatus() const;
    
    // ==================== 文件系统管理 ====================
    
    /**
     * 初始化文件系统
     * @return bool 初始化是否成功
     */
    bool initializeFileSystem();
    
    /**
     * 检查文件系统状态
     * @return bool 文件系统是否正常
     */
    bool checkFileSystemHealth();
    
    /**
     * 获取文件系统信息
     * @return DynamicJsonDocument 文件系统信息
     */
    DynamicJsonDocument getFileSystemInfo() const;
    
    // ==================== 系统状态 ====================
    
    /**
     * 获取系统状态
     * @return DynamicJsonDocument 系统状态信息
     */
    DynamicJsonDocument getSystemStatus() const;
    
    /**
     * 获取系统统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getSystemStatistics() const;
    
    /**
     * 获取内存使用情况
     * @return DynamicJsonDocument 内存使用信息
     */
    DynamicJsonDocument getMemoryUsage() const;
    
    /**
     * 获取系统配置
     * @return DynamicJsonDocument 系统配置
     */
    DynamicJsonDocument getSystemConfig() const;
    
    // ==================== 组件管理 ====================
    
    /**
     * 获取数据管理器
     * @return DataManager* 数据管理器指针
     */
    DataManager* getDataManager() const;
    
    /**
     * 获取红外控制器
     * @return IRController* 红外控制器指针
     */
    IRController* getIRController() const;
    
    /**
     * 获取任务管理器
     * @return TaskManager* 任务管理器指针
     */
    TaskManager* getTaskManager() const;
    
    /**
     * 获取Web服务器管理器
     * @return WebServerManager* Web服务器管理器指针
     */
    WebServerManager* getWebServerManager() const;
    
    /**
     * 获取WebSocket管理器
     * @return WSManager* WebSocket管理器指针
     */
    WSManager* getWSManager() const;
    
    /**
     * 获取网络安全管理器
     * @return NetworkSecurity* 网络安全管理器指针
     */
    NetworkSecurity* getNetworkSecurity() const;
    
    // ==================== 消息广播 ====================
    
    /**
     * 广播消息给所有客户端
     * @param message JSON消息
     */
    void broadcastMessage(const DynamicJsonDocument& message);
    
    /**
     * 广播系统状态更新
     */
    void broadcastSystemStatus();
    
    /**
     * 广播错误消息
     * @param error 错误信息
     */
    void broadcastError(const String& error);
    
    // ==================== 错误处理 ====================
    
    /**
     * 处理系统错误
     * @param error 错误信息
     * @param critical 是否为严重错误
     */
    void handleSystemError(const String& error, bool critical = false);
    
    /**
     * 获取最后的错误信息
     * @return String 错误信息
     */
    String getLastError() const;
    
    /**
     * 清除错误状态
     */
    void clearError();
    
    // ==================== 调试功能 ====================
    
    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    void enableDebugMode(bool enable);
    
    /**
     * 获取调试信息
     * @return String 调试信息
     */
    String getDebugInfo() const;
    
    /**
     * 执行系统自检
     * @return DynamicJsonDocument 自检结果
     */
    DynamicJsonDocument performSystemSelfTest();

private:
    // ==================== 私有成员变量 ====================
    
    SystemCapacity m_capacity;          // 系统容量配置
    bool m_initialized;                 // 是否已初始化
    bool m_started;                     // 是否已启动
    bool m_debugMode;                   // 调试模式
    String m_lastError;                 // 最后的错误信息
    unsigned long m_startTime;          // 启动时间
    unsigned long m_lastStatusUpdate;   // 最后状态更新时间
    
    // 组件指针
    DataManager* m_dataManager;
    IRController* m_irController;
    TaskManager* m_taskManager;
    WebServerManager* m_webServerManager;
    WSManager* m_wsManager;
    NetworkSecurity* m_networkSecurity;
    
    // 系统状态
    bool m_wifiConnected;
    bool m_fileSystemReady;
    bool m_componentsHealthy;
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化所有组件
     * @return bool 初始化是否成功
     */
    bool initializeComponents();
    
    /**
     * 设置组件依赖关系
     */
    void setupComponentDependencies();
    
    /**
     * 启动所有服务
     * @return bool 启动是否成功
     */
    bool startServices();
    
    /**
     * 停止所有服务
     */
    void stopServices();
    
    /**
     * 清理所有组件
     */
    void cleanupComponents();
    
    /**
     * 检查组件健康状态
     * @return bool 所有组件是否健康
     */
    bool checkComponentsHealth();
    
    /**
     * 监控系统资源
     */
    void monitorSystemResources();
    
    /**
     * 记录系统日志
     * @param message 日志信息
     * @param level 日志级别
     */
    void logSystem(const String& message, const String& level = "INFO");
};

#endif // SYSTEM_MANAGER_H

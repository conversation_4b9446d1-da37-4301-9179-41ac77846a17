/*
 * ESP32-S3 IR Control System - Arduino IDE Version
 * Hardware: ESP32-S3-WROOM-1-N16R8 (16MB Flash + 8MB OPI PSRAM)
 * GPIO: IR_TX=21, IR_RX=14
 * 
 * Arduino IDE Configuration:
 * - Board: ESP32S3 Dev Module
 * - Flash Mode: QIO
 * - Flash Size: 16MB (128Mb)
 * - PSRAM: OPI PSRAM
 * - Flash Frequency: 80MHz
 * - Upload Speed: 460800
 * - Core Debug Level: None
 */

#include <Arduino.h>
#include <WiFi.h>
#include <SPIFFS.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

// IRremoteESP8266 Arduino Core 3.x 兼容性补丁
#include "IRremoteESP8266_Core3_Fix.h"

// Include all manager headers
#include "wifi_manager.h"
#include "websocket_manager.h"
#include "data_manager.h"
#include "ir_controller.h"
#include "web_server_manager.h"
#include "task_manager.h"
#include "system_monitor.h"

// ==================== Global Objects ====================

// Core Server Components
AsyncWebServer server(80);
AsyncWebSocket webSocket("/ws");

// Manager Instances
WiFiManager wifiManager;
WebSocketManager wsManager;
DataManager dataManager;
IRController irController;
WebServerManager webServerManager;
TaskManager taskManager;
SystemMonitor systemMonitor;

// System State
bool systemInitialized = false;
unsigned long systemStartTime = 0;

// ==================== Function Declarations ====================
void setupEventCallbacks();
void performPSRAMTest();

// ==================== Setup Function ====================

void setup() {
    // Initialize Serial Communication
    Serial.begin(115200);
    delay(1000);

    Serial.println();
    Serial.println("========================================");
    Serial.println("ESP32-S3 IR Control System Starting...");
    Serial.println("Arduino IDE Version with OPI PSRAM");
    Serial.println("========================================");

    systemStartTime = millis();

    // Step 0: System Information
    Serial.println("Step 0: System Information");
    Serial.printf("Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash Size: %d bytes (%.2f MB)\n", ESP.getFlashChipSize(), ESP.getFlashChipSize() / 1024.0 / 1024.0);
    Serial.printf("Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    Serial.printf("SDK Version: %s\n", ESP.getSdkVersion());

    Serial.println("========================================");

    // Step 1: PSRAM Detection and Testing (Critical for Arduino IDE)
    Serial.println("Step 1: PSRAM Detection and Testing");
    performPSRAMTest();

    Serial.println("========================================");

    // Step 2: Initialize File System
    Serial.println("Step 2: Initializing SPIFFS...");
    if (!SPIFFS.begin(true)) {
        Serial.println("⚠️ SPIFFS initialization failed - continuing without file system");
    } else {
        Serial.println("✅ SPIFFS initialized successfully");
        Serial.printf("📁 Total space: %d bytes (%.2f KB)\n", SPIFFS.totalBytes(), SPIFFS.totalBytes() / 1024.0);
        Serial.printf("📁 Used space: %d bytes (%.2f KB)\n", SPIFFS.usedBytes(), SPIFFS.usedBytes() / 1024.0);
    }

    // Step 3: Initialize Data Manager
    Serial.println("Step 3: Initializing Data Manager...");
    if (!dataManager.initialize()) {
        Serial.println("⚠️ Data Manager initialization failed - continuing");
    } else {
        Serial.println("✅ Data Manager initialized successfully");
    }

    // Step 4: Initialize IR Controller
    Serial.println("Step 4: Initializing IR Controller...");
    irController.setDataManager(&dataManager);
    if (!irController.initialize()) {
        Serial.println("ERROR: IR Controller initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ IR Controller initialized successfully");

    // Step 5: Initialize WiFi Manager
    Serial.println("Step 5: Initializing WiFi Manager...");
    if (!wifiManager.initialize()) {
        Serial.println("ERROR: WiFi Manager initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ WiFi Manager initialized successfully");

    // Step 6: Initialize WebSocket Manager
    Serial.println("Step 6: Initializing WebSocket Manager...");
    if (!wsManager.initialize(&webSocket)) {
        Serial.println("ERROR: WebSocket Manager initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ WebSocket Manager initialized successfully");

    // Step 7: Initialize Task Manager
    Serial.println("Step 7: Initializing Task Manager...");
    taskManager.setDataManager(&dataManager);
    taskManager.setIRController(&irController);
    taskManager.setWebSocketManager(&wsManager);
    if (!taskManager.initialize()) {
        Serial.println("ERROR: Task Manager initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ Task Manager initialized successfully");

    // Step 8: Initialize Web Server Manager
    Serial.println("Step 8: Initializing Web Server Manager...");
    webServerManager.setDataManager(&dataManager);
    webServerManager.setIRController(&irController);
    webServerManager.setTaskManager(&taskManager);
    webServerManager.setWebSocketManager(&wsManager);
    if (!webServerManager.initialize(&server, &webSocket)) {
        Serial.println("ERROR: Web Server Manager initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ Web Server Manager initialized successfully");

    // Step 9: Initialize System Monitor
    Serial.println("Step 9: Initializing System Monitor...");
    systemMonitor.setDataManager(&dataManager);
    systemMonitor.setIRController(&irController);
    systemMonitor.setTaskManager(&taskManager);
    systemMonitor.setWebSocketManager(&wsManager);
    if (!systemMonitor.initialize()) {
        Serial.println("ERROR: System Monitor initialization failed!");
        while (true) {
            delay(1000);
        }
    }
    Serial.println("✅ System Monitor initialized successfully");

    // Step 10: Setup Event Callbacks
    Serial.println("Step 10: Setting up event callbacks...");
    setupEventCallbacks();
    Serial.println("✅ Event callbacks configured");

    // Step 11: Start WiFi Connection
    Serial.println("Step 11: Starting WiFi connection...");
    wifiManager.startConnection();

    // Step 12: Start Web Server
    Serial.println("Step 12: Starting Web Server...");
    server.begin();
    Serial.println("✅ Web Server started on port 80");

    // Step 13: Start System Monitoring
    Serial.println("Step 13: Starting System Monitor...");
    systemMonitor.startMonitoring();
    Serial.println("✅ System Monitor started");

    // System Ready
    systemInitialized = true;
    unsigned long initTime = millis() - systemStartTime;

    Serial.println();
    Serial.println("🎉 SYSTEM INITIALIZATION COMPLETED! 🎉");
    Serial.printf("⏱️  Initialization time: %lu ms\n", initTime);
    Serial.println("📡 IR Transmitter: GPIO21");
    Serial.println("📡 IR Receiver: GPIO14");
    Serial.println("🌐 Web Server: http://192.168.4.1 (AP mode)");
    Serial.println("🔌 WebSocket: ws://192.168.4.1/ws");
    Serial.println("📊 Serial Monitor: 115200 baud");
    Serial.println();
    Serial.println("System is ready for operation!");
    Serial.println("========================================");

    // Broadcast system ready message
    if (systemInitialized) {
        DynamicJsonDocument readyMsg(256);
        readyMsg["message"] = "System initialization completed";
        readyMsg["uptime"] = initTime;
        readyMsg["timestamp"] = millis();
        wsManager.broadcastMessage("system_ready", readyMsg);
    }
}

// ==================== Main Loop ====================

void loop() {
    // Only run main loop if system is initialized
    if (!systemInitialized) {
        delay(100);
        return;
    }

    // Handle all manager loops
    wifiManager.handleLoop();
    wsManager.handleLoop();
    irController.handleLoop();
    taskManager.handleLoop();
    systemMonitor.handleLoop();

    // Small delay to prevent watchdog issues
    delay(1);
}

// ==================== PSRAM Test Function ====================

void performPSRAMTest() {
    Serial.println("🔬 Comprehensive PSRAM Testing (Arduino IDE)");
    Serial.println("=============================================");
    
    if (!psramFound()) {
        Serial.println("❌ PSRAM NOT FOUND!");
        Serial.println("⚠️  Check Arduino IDE configuration:");
        Serial.println("   - Board: ESP32S3 Dev Module");
        Serial.println("   - PSRAM: OPI PSRAM");
        Serial.println("   - Flash Mode: QIO");
        Serial.println("   - Flash Size: 16MB");
        Serial.println("System will continue but with limited memory");
        return;
    }
    
    Serial.println("✅ PSRAM FOUND - Starting comprehensive tests");
    Serial.printf("📊 Total PSRAM: %d bytes (%.2f MB)\n", ESP.getPsramSize(), ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("🧠 Free PSRAM: %d bytes (%.2f MB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0 / 1024.0);
    
    // Test 1: Basic allocation test
    Serial.println("\n🧪 Test 1: Basic Allocation Test");
    void* ptr1 = ps_malloc(1024);
    if (ptr1) {
        Serial.printf("✅ 1KB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr1);
        free(ptr1);
    } else {
        Serial.println("❌ 1KB allocation: FAILED");
    }
    
    // Test 2: Large allocation test
    Serial.println("\n🧪 Test 2: Large Allocation Test");
    size_t largeSize = 1024 * 1024; // 1MB
    void* ptr2 = ps_malloc(largeSize);
    if (ptr2) {
        Serial.printf("✅ 1MB allocation: SUCCESS at 0x%08X\n", (uint32_t)ptr2);
        free(ptr2);
    } else {
        Serial.println("❌ 1MB allocation: FAILED");
    }
    
    // Test 3: Read/Write integrity test
    Serial.println("\n🧪 Test 3: Read/Write Integrity Test");
    size_t testSize = 256 * 1024; // 256KB
    uint8_t* testBuffer = (uint8_t*)ps_malloc(testSize);
    if (testBuffer) {
        // Write test pattern
        for (size_t i = 0; i < testSize; i++) {
            testBuffer[i] = (uint8_t)(i % 256);
        }
        
        // Verify test pattern
        bool integrity = true;
        for (size_t i = 0; i < testSize; i++) {
            if (testBuffer[i] != (uint8_t)(i % 256)) {
                integrity = false;
                break;
            }
        }
        
        if (integrity) {
            Serial.println("✅ Read/Write integrity: SUCCESS");
        } else {
            Serial.println("❌ Read/Write integrity: FAILED");
        }
        
        free(testBuffer);
    } else {
        Serial.println("❌ Read/Write test: Allocation failed");
    }
    
    Serial.println("=============================================");
    Serial.println("🏁 PSRAM Testing Suite Completed");
}

// ==================== Event Callback Setup ====================

void setupEventCallbacks() {
    // WiFi Manager Callbacks
    wifiManager.setOnConnected([]() {
        Serial.println("📶 WiFi Connected!");
        Serial.printf("🌐 IP Address: %s\n", WiFi.localIP().toString().c_str());

        DynamicJsonDocument wifiMsg(256);
        wifiMsg["status"] = "connected";
        wifiMsg["ip"] = WiFi.localIP().toString();
        wifiMsg["rssi"] = WiFi.RSSI();
        wsManager.broadcastMessage("wifi_status", wifiMsg);
    });

    wifiManager.setOnDisconnected([]() {
        Serial.println("📶 WiFi Disconnected!");

        DynamicJsonDocument wifiMsg(256);
        wifiMsg["status"] = "disconnected";
        wsManager.broadcastMessage("wifi_status", wifiMsg);
    });

    wifiManager.setOnAPStarted([]() {
        Serial.println("📡 AP Mode Started!");
        Serial.printf("🌐 AP IP: %s\n", WiFi.softAPIP().toString().c_str());

        DynamicJsonDocument apMsg(256);
        apMsg["status"] = "ap_started";
        apMsg["ip"] = WiFi.softAPIP().toString();
        wsManager.broadcastMessage("ap_status", apMsg);
    });

    // IR Controller Callbacks
    irController.setOnSignalLearned([](const DynamicJsonDocument& signal) {
        Serial.println("📡 Signal learned successfully!");
        wsManager.broadcastMessage("signal_learned", signal);
    });

    irController.setOnSignalSent([](const String& signalId, bool success) {
        Serial.printf("📡 Signal sent: %s - %s\n", signalId.c_str(), success ? "SUCCESS" : "FAILED");

        DynamicJsonDocument sendMsg(256);
        sendMsg["signal_id"] = signalId;
        sendMsg["success"] = success;
        sendMsg["timestamp"] = millis();
        wsManager.broadcastMessage("signal_sent", sendMsg);
    });

    // Task Manager Callbacks
    taskManager.setOnTaskCompleted([](const String& taskId, bool success) {
        Serial.printf("📋 Task completed: %s - %s\n", taskId.c_str(), success ? "SUCCESS" : "FAILED");

        DynamicJsonDocument taskMsg(256);
        taskMsg["task_id"] = taskId;
        taskMsg["success"] = success;
        taskMsg["timestamp"] = millis();
        wsManager.broadcastMessage("task_completed", taskMsg);
    });

    // System Monitor Callbacks
    systemMonitor.setOnHealthChanged([](SystemHealth health, const String& reason) {
        Serial.printf("🏥 System health changed: %s - %s\n",
                     systemMonitor.getHealthString(health).c_str(), reason.c_str());

        DynamicJsonDocument healthMsg(256);
        healthMsg["health"] = static_cast<int>(health);
        healthMsg["health_text"] = systemMonitor.getHealthString(health);
        healthMsg["reason"] = reason;
        healthMsg["timestamp"] = millis();
        wsManager.broadcastMessage("health_changed", healthMsg);
    });

    systemMonitor.setOnAlert([](const String& alert) {
        Serial.printf("⚠️  System Alert: %s\n", alert.c_str());
        wsManager.sendNotification(alert, "warning");
    });
}

#ifndef DATA_STRUCTURES_H
#define DATA_STRUCTURES_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>

/**
 * 数据结构定义文件 (纯RAM版本)
 * 
 * 定义系统中使用的所有数据结构
 * 确保数据格式的一致性和标准化
 * 针对纯RAM模式优化内存使用
 */

// ==================== 信号数据结构 ====================

/**
 * 信号数据结构
 */
struct SignalData {
    String id;                          // 信号唯一ID
    String name;                        // 信号名称
    String signalCode;                  // 信号代码
    String protocol;                    // 信号协议 (NEC, RC5, RC6, RAW等)
    String type;                        // 设备类型 (TV, AC, STB等)
    String brand;                       // 品牌
    String model;                       // 型号
    String description;                 // 描述
    unsigned long frequency;            // 载波频率
    unsigned long createdTime;          // 创建时间
    unsigned long lastUsedTime;         // 最后使用时间
    int useCount;                       // 使用次数
    bool isValid;                       // 是否有效
    
    // 原始数据（用于RAW协议）- 减少缓冲区大小
    uint16_t* rawData;                  // 原始时序数据
    size_t rawDataLength;               // 原始数据长度
    
    /**
     * 构造函数
     */
    SignalData() 
        : id(""), name(""), signalCode(""), protocol("NEC"), type(""), 
          brand(""), model(""), description(""), frequency(38000),
          createdTime(0), lastUsedTime(0), useCount(0), isValid(false),
          rawData(nullptr), rawDataLength(0) {}
    
    /**
     * 析构函数
     */
    ~SignalData() {
        if (rawData) {
            free(rawData);
            rawData = nullptr;
        }
    }
    
    /**
     * 拷贝构造函数
     */
    SignalData(const SignalData& other) 
        : id(other.id), name(other.name), signalCode(other.signalCode),
          protocol(other.protocol), type(other.type), brand(other.brand),
          model(other.model), description(other.description),
          frequency(other.frequency), createdTime(other.createdTime),
          lastUsedTime(other.lastUsedTime), useCount(other.useCount),
          isValid(other.isValid), rawData(nullptr), rawDataLength(other.rawDataLength) {
        
        if (other.rawData && other.rawDataLength > 0) {
            rawData = (uint16_t*)malloc(other.rawDataLength * sizeof(uint16_t));
            if (rawData) {
                memcpy(rawData, other.rawData, other.rawDataLength * sizeof(uint16_t));
            } else {
                rawDataLength = 0;
            }
        }
    }
    
    /**
     * 赋值操作符
     */
    SignalData& operator=(const SignalData& other) {
        if (this != &other) {
            // 清理现有数据
            if (rawData) {
                free(rawData);
                rawData = nullptr;
            }
            
            // 复制基本数据
            id = other.id;
            name = other.name;
            signalCode = other.signalCode;
            protocol = other.protocol;
            type = other.type;
            brand = other.brand;
            model = other.model;
            description = other.description;
            frequency = other.frequency;
            createdTime = other.createdTime;
            lastUsedTime = other.lastUsedTime;
            useCount = other.useCount;
            isValid = other.isValid;
            rawDataLength = other.rawDataLength;
            
            // 复制原始数据
            if (other.rawData && other.rawDataLength > 0) {
                rawData = (uint16_t*)malloc(other.rawDataLength * sizeof(uint16_t));
                if (rawData) {
                    memcpy(rawData, other.rawData, other.rawDataLength * sizeof(uint16_t));
                } else {
                    rawDataLength = 0;
                }
            }
        }
        return *this;
    }
    
    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["name"] = name;
        obj["signalCode"] = signalCode;
        obj["protocol"] = protocol;
        obj["type"] = type;
        obj["brand"] = brand;
        obj["model"] = model;
        obj["description"] = description;
        obj["frequency"] = frequency;
        obj["createdTime"] = createdTime;
        obj["lastUsedTime"] = lastUsedTime;
        obj["useCount"] = useCount;
        obj["isValid"] = isValid;
        
        // 原始数据转换为数组（仅在需要时）
        if (rawData && rawDataLength > 0 && rawDataLength < 100) { // 限制大小
            JsonArray rawArray = obj.createNestedArray("rawData");
            for (size_t i = 0; i < rawDataLength; i++) {
                rawArray.add(rawData[i]);
            }
        }
        
        return obj;
    }
    
    /**
     * 从JSON加载
     */
    bool fromJson(const JsonObject& obj) {
        if (!obj.containsKey("id") || !obj.containsKey("name")) {
            return false;
        }
        
        id = obj["id"].as<String>();
        name = obj["name"].as<String>();
        signalCode = obj["signalCode"].as<String>();
        protocol = obj["protocol"] | "NEC";
        type = obj["type"].as<String>();
        brand = obj["brand"].as<String>();
        model = obj["model"].as<String>();
        description = obj["description"].as<String>();
        frequency = obj["frequency"] | 38000;
        createdTime = obj["createdTime"] | 0;
        lastUsedTime = obj["lastUsedTime"] | 0;
        useCount = obj["useCount"] | 0;
        isValid = obj["isValid"] | false;
        
        // 加载原始数据
        if (obj.containsKey("rawData")) {
            JsonArray rawArray = obj["rawData"];
            rawDataLength = rawArray.size();
            
            if (rawDataLength > 0 && rawDataLength < 500) { // 限制大小
                rawData = (uint16_t*)malloc(rawDataLength * sizeof(uint16_t));
                if (rawData) {
                    for (size_t i = 0; i < rawDataLength; i++) {
                        rawData[i] = rawArray[i];
                    }
                } else {
                    rawDataLength = 0;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据有效性
     */
    bool validate() const {
        return !id.isEmpty() && !name.isEmpty() && !signalCode.isEmpty() && 
               frequency > 0 && frequency <= 56000;
    }
    
    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(SignalData);
        usage += id.length() + name.length() + signalCode.length();
        usage += protocol.length() + type.length() + brand.length();
        usage += model.length() + description.length();
        if (rawData) {
            usage += rawDataLength * sizeof(uint16_t);
        }
        return usage;
    }
};

// ==================== 任务数据结构 ====================

/**
 * 任务数据结构
 */
struct TaskData {
    String id;                          // 任务唯一ID
    String name;                        // 任务名称
    String description;                 // 任务描述
    std::vector<String> signalIds;      // 信号ID列表
    std::vector<int> delays;            // 延迟列表（毫秒）
    unsigned long scheduledTime;        // 计划执行时间
    bool isRepeating;                   // 是否重复执行
    unsigned long repeatInterval;       // 重复间隔（毫秒）
    int repeatCount;                    // 重复次数（-1为无限）
    int executedCount;                  // 已执行次数
    bool isEnabled;                     // 是否启用
    bool isExecuting;                   // 是否正在执行
    unsigned long createdTime;          // 创建时间
    unsigned long lastExecutedTime;     // 最后执行时间
    
    /**
     * 构造函数
     */
    TaskData() 
        : id(""), name(""), description(""), scheduledTime(0),
          isRepeating(false), repeatInterval(0), repeatCount(0),
          executedCount(0), isEnabled(true), isExecuting(false),
          createdTime(0), lastExecutedTime(0) {
        signalIds.reserve(5);  // 预分配空间
        delays.reserve(5);
    }
    
    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["name"] = name;
        obj["description"] = description;
        obj["scheduledTime"] = scheduledTime;
        obj["isRepeating"] = isRepeating;
        obj["repeatInterval"] = repeatInterval;
        obj["repeatCount"] = repeatCount;
        obj["executedCount"] = executedCount;
        obj["isEnabled"] = isEnabled;
        obj["isExecuting"] = isExecuting;
        obj["createdTime"] = createdTime;
        obj["lastExecutedTime"] = lastExecutedTime;
        
        // 信号ID数组
        JsonArray signalArray = obj.createNestedArray("signalIds");
        for (const String& signalId : signalIds) {
            signalArray.add(signalId);
        }
        
        // 延迟数组
        JsonArray delayArray = obj.createNestedArray("delays");
        for (int delay : delays) {
            delayArray.add(delay);
        }
        
        return obj;
    }
    
    /**
     * 从JSON加载
     */
    bool fromJson(const JsonObject& obj) {
        if (!obj.containsKey("id") || !obj.containsKey("name")) {
            return false;
        }
        
        id = obj["id"].as<String>();
        name = obj["name"].as<String>();
        description = obj["description"].as<String>();
        scheduledTime = obj["scheduledTime"] | 0;
        isRepeating = obj["isRepeating"] | false;
        repeatInterval = obj["repeatInterval"] | 0;
        repeatCount = obj["repeatCount"] | 0;
        executedCount = obj["executedCount"] | 0;
        isEnabled = obj["isEnabled"] | true;
        isExecuting = obj["isExecuting"] | false;
        createdTime = obj["createdTime"] | 0;
        lastExecutedTime = obj["lastExecutedTime"] | 0;
        
        // 加载信号ID数组
        signalIds.clear();
        if (obj.containsKey("signalIds")) {
            JsonArray signalArray = obj["signalIds"];
            for (JsonVariant signal : signalArray) {
                signalIds.push_back(signal.as<String>());
            }
        }
        
        // 加载延迟数组
        delays.clear();
        if (obj.containsKey("delays")) {
            JsonArray delayArray = obj["delays"];
            for (JsonVariant delay : delayArray) {
                delays.push_back(delay.as<int>());
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据有效性
     */
    bool validate() const {
        return !id.isEmpty() && !name.isEmpty() && 
               signalIds.size() == delays.size();
    }
    
    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(TaskData);
        usage += id.length() + name.length() + description.length();
        usage += signalIds.size() * sizeof(String);
        for (const String& signalId : signalIds) {
            usage += signalId.length();
        }
        usage += delays.size() * sizeof(int);
        return usage;
    }
};

// ==================== 定时器数据结构 ====================

/**
 * 定时器数据结构
 */
struct TimerData {
    String id;                          // 定时器唯一ID
    String name;                        // 定时器名称
    String description;                 // 定时器描述
    String taskId;                      // 关联的任务ID
    unsigned long triggerTime;          // 触发时间
    bool isRepeating;                   // 是否重复
    unsigned long repeatInterval;       // 重复间隔（毫秒）
    int repeatCount;                    // 重复次数（-1为无限）
    int executedCount;                  // 已执行次数
    bool isEnabled;                     // 是否启用
    bool isActive;                      // 是否激活
    unsigned long createdTime;          // 创建时间
    unsigned long lastTriggeredTime;    // 最后触发时间

    /**
     * 构造函数
     */
    TimerData()
        : id(""), name(""), description(""), taskId(""), triggerTime(0),
          isRepeating(false), repeatInterval(0), repeatCount(0),
          executedCount(0), isEnabled(true), isActive(false),
          createdTime(0), lastTriggeredTime(0) {}

    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["name"] = name;
        obj["description"] = description;
        obj["taskId"] = taskId;
        obj["triggerTime"] = triggerTime;
        obj["isRepeating"] = isRepeating;
        obj["repeatInterval"] = repeatInterval;
        obj["repeatCount"] = repeatCount;
        obj["executedCount"] = executedCount;
        obj["isEnabled"] = isEnabled;
        obj["isActive"] = isActive;
        obj["createdTime"] = createdTime;
        obj["lastTriggeredTime"] = lastTriggeredTime;
        return obj;
    }

    /**
     * 从JSON加载
     */
    bool fromJson(const JsonObject& obj) {
        if (!obj.containsKey("id") || !obj.containsKey("name")) {
            return false;
        }

        id = obj["id"].as<String>();
        name = obj["name"].as<String>();
        description = obj["description"].as<String>();
        taskId = obj["taskId"].as<String>();
        triggerTime = obj["triggerTime"] | 0;
        isRepeating = obj["isRepeating"] | false;
        repeatInterval = obj["repeatInterval"] | 0;
        repeatCount = obj["repeatCount"] | 0;
        executedCount = obj["executedCount"] | 0;
        isEnabled = obj["isEnabled"] | true;
        isActive = obj["isActive"] | false;
        createdTime = obj["createdTime"] | 0;
        lastTriggeredTime = obj["lastTriggeredTime"] | 0;

        return true;
    }

    /**
     * 验证数据有效性
     */
    bool validate() const {
        return !id.isEmpty() && !name.isEmpty() && !taskId.isEmpty();
    }

    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(TimerData);
        usage += id.length() + name.length() + description.length() + taskId.length();
        return usage;
    }
};

// ==================== WebSocket客户端数据结构 ====================

/**
 * WebSocket客户端数据结构
 */
struct WSClientData {
    uint32_t id;                        // 客户端ID
    String ipAddress;                   // IP地址
    unsigned long connectTime;          // 连接时间
    unsigned long lastPingTime;         // 最后ping时间
    unsigned long lastActivityTime;     // 最后活动时间
    bool isAuthenticated;               // 是否已认证
    String userAgent;                   // 用户代理
    int messageCount;                   // 消息计数
    bool isActive;                      // 是否活跃

    /**
     * 构造函数
     */
    WSClientData()
        : id(0), ipAddress(""), connectTime(0), lastPingTime(0),
          lastActivityTime(0), isAuthenticated(false), userAgent(""),
          messageCount(0), isActive(true) {}

    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["ipAddress"] = ipAddress;
        obj["connectTime"] = connectTime;
        obj["lastPingTime"] = lastPingTime;
        obj["lastActivityTime"] = lastActivityTime;
        obj["isAuthenticated"] = isAuthenticated;
        obj["userAgent"] = userAgent;
        obj["messageCount"] = messageCount;
        obj["isActive"] = isActive;
        return obj;
    }

    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(WSClientData);
        usage += ipAddress.length() + userAgent.length();
        return usage;
    }
};

// ==================== 系统状态数据结构 ====================

/**
 * 系统状态数据结构
 */
struct SystemStatusData {
    unsigned long uptime;               // 运行时间
    size_t freeHeap;                    // 空闲堆内存
    size_t totalHeap;                   // 总堆内存
    size_t maxAllocHeap;                // 最大可分配堆内存
    float cpuUsage;                     // CPU使用率
    int wifiSignalStrength;             // WiFi信号强度
    String wifiSSID;                    // WiFi SSID
    String ipAddress;                   // IP地址
    int connectedClients;               // 连接的客户端数量
    int totalSignals;                   // 总信号数量
    int totalTasks;                     // 总任务数量
    int totalTimers;                    // 总定时器数量
    bool isHealthy;                     // 系统是否健康

    /**
     * 构造函数
     */
    SystemStatusData()
        : uptime(0), freeHeap(0), totalHeap(0), maxAllocHeap(0),
          cpuUsage(0.0f), wifiSignalStrength(0), wifiSSID(""),
          ipAddress(""), connectedClients(0), totalSignals(0),
          totalTasks(0), totalTimers(0), isHealthy(true) {}

    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["uptime"] = uptime;
        obj["freeHeap"] = freeHeap;
        obj["totalHeap"] = totalHeap;
        obj["maxAllocHeap"] = maxAllocHeap;
        obj["cpuUsage"] = cpuUsage;
        obj["wifiSignalStrength"] = wifiSignalStrength;
        obj["wifiSSID"] = wifiSSID;
        obj["ipAddress"] = ipAddress;
        obj["connectedClients"] = connectedClients;
        obj["totalSignals"] = totalSignals;
        obj["totalTasks"] = totalTasks;
        obj["totalTimers"] = totalTimers;
        obj["isHealthy"] = isHealthy;
        return obj;
    }

    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(SystemStatusData);
        usage += wifiSSID.length() + ipAddress.length();
        return usage;
    }
};

// ==================== 错误信息数据结构 ====================

/**
 * 错误信息数据结构
 */
struct ErrorData {
    String id;                          // 错误ID
    String message;                     // 错误消息
    String source;                      // 错误源
    int level;                          // 错误级别 (0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR)
    unsigned long timestamp;            // 时间戳
    String details;                     // 详细信息

    /**
     * 构造函数
     */
    ErrorData()
        : id(""), message(""), source(""), level(0), timestamp(0), details("") {}

    /**
     * 构造函数
     */
    ErrorData(const String& msg, const String& src, int lvl)
        : id(String(millis())), message(msg), source(src), level(lvl),
          timestamp(millis()), details("") {}

    /**
     * 转换为JSON
     */
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["message"] = message;
        obj["source"] = source;
        obj["level"] = level;
        obj["timestamp"] = timestamp;
        obj["details"] = details;
        return obj;
    }

    /**
     * 获取内存使用量
     */
    size_t getMemoryUsage() const {
        size_t usage = sizeof(ErrorData);
        usage += id.length() + message.length() + source.length() + details.length();
        return usage;
    }
};

// ==================== 辅助函数 ====================

/**
 * 生成唯一ID
 */
inline String generateUniqueId() {
    return String(millis()) + "_" + String(random(1000, 9999));
}

/**
 * 获取当前时间戳
 */
inline unsigned long getCurrentTimestamp() {
    return millis();
}

/**
 * 格式化时间戳
 */
inline String formatTimestamp(unsigned long timestamp) {
    unsigned long seconds = timestamp / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;
    unsigned long days = hours / 24;

    if (days > 0) {
        return String(days) + "d " + String(hours % 24) + "h " + String(minutes % 60) + "m";
    } else if (hours > 0) {
        return String(hours) + "h " + String(minutes % 60) + "m " + String(seconds % 60) + "s";
    } else if (minutes > 0) {
        return String(minutes) + "m " + String(seconds % 60) + "s";
    } else {
        return String(seconds) + "s";
    }
}

#endif // DATA_STRUCTURES_H

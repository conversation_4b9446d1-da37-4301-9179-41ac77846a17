@echo off
echo === ESP_Async_WebServer MD5.H 兼容性修复工具 ===
echo.

REM 设置可能的库路径
set "PATH1=%USERPROFILE%\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebAuthentication.cpp"
set "PATH2=%USERPROFILE%\Documents\Arduino\libraries\ESPAsyncWebServer\src\WebAuthentication.cpp"
set "PATH3=%USERPROFILE%\Documents\Arduino\libraries\ESP32AsyncWebServer\src\WebAuthentication.cpp"

set "TARGETFILE="

REM 检查文件是否存在
if exist "%PATH1%" (
    set "TARGETFILE=%PATH1%"
    echo ✓ 找到目标文件: %PATH1%
    goto :found
)

if exist "%PATH2%" (
    set "TARGETFILE=%PATH2%"
    echo ✓ 找到目标文件: %PATH2%
    goto :found
)

if exist "%PATH3%" (
    set "TARGETFILE=%PATH3%"
    echo ✓ 找到目标文件: %PATH3%
    goto :found
)

echo ❌ 错误: 未找到 WebAuthentication.cpp 文件
echo 请确保已安装 ESP_Async_WebServer 库
echo.
echo 可能的安装位置:
echo   - %PATH1%
echo   - %PATH2%
echo   - %PATH3%
pause
exit /b 1

:found
echo.

REM 创建备份
if not exist "%TARGETFILE%.backup" (
    copy "%TARGETFILE%" "%TARGETFILE%.backup" >nul
    echo ✓ 已创建备份文件: %TARGETFILE%.backup
)

REM 检查是否已经修复
findstr /C:"mbedtls/md5.h" "%TARGETFILE%" >nul
if %errorlevel% equ 0 (
    echo ✓ 文件已经修复，无需重复操作
    pause
    exit /b 0
)

echo 🔧 正在修复 md5.h 引用...

REM 使用PowerShell进行文本替换
powershell -Command "(Get-Content '%TARGETFILE%') -replace '#include \"md5\.h\"', '#include \"mbedtls/md5.h\"' + [Environment]::NewLine + '#include \"mbedtls/compat-2.x.h\"' | Set-Content '%TARGETFILE%'"

echo ✓ 修复完成!
echo.
echo 修复详情:
echo   - 文件: %TARGETFILE%
echo   - 替换: #include "md5.h" → #include "mbedtls/md5.h"
echo   - 添加: #include "mbedtls/compat-2.x.h"
echo.
echo 现在可以重新编译您的 ESP32 项目了!
echo.

REM 验证修复
echo === 验证修复结果 ===
findstr /C:"mbedtls" "%TARGETFILE%"
if %errorlevel% equ 0 (
    echo ✓ 验证成功: 已正确引用 mbedtls 库
) else (
    echo ❌ 验证失败: 修复可能未成功
)

echo.
echo 如果需要恢复原文件，请运行:
echo   copy "%TARGETFILE%.backup" "%TARGETFILE%"
echo.
pause

#include "WebServerManager.h"
#include "../data/DataManager.h"
#include "../hardware/IRController.h"
#include "../tasks/TaskManager.h"
#include "WSManager.h"
#include "NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
WebServerManager::WebServerManager()
    : m_initialized(false)
    , m_serverRunning(false)
    , m_server(nullptr)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_taskManager(nullptr)
    , m_wsManager(nullptr)
    , m_networkSecurity(nullptr)
    , m_totalRequests(0)
    , m_successfulRequests(0)
    , m_failedRequests(0)
{
    Serial.println("🌐 WebServerManager created");
}

WebServerManager::~WebServerManager() {
    shutdown();
    if (m_server) {
        delete m_server;
        m_server = nullptr;
    }
    Serial.println("🌐 WebServerManager destroyed");
}

// ==================== 系统生命周期 ====================
bool WebServerManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  WebServerManager already initialized");
        return true;
    }
    
    Serial.println("🌐 Initializing WebServerManager...");
    
    // 1. 创建Web服务器实例
    m_server = new AsyncWebServer(WEB_SERVER_PORT);
    if (!m_server) {
        logError("Failed to create web server instance");
        return false;
    }
    
    // 2. 集成WebSocket
    if (m_wsManager && m_wsManager->getWebSocket()) {
        m_server->addHandler(m_wsManager->getWebSocket());
        Serial.println("🌐 WebSocket integrated with web server");
    }

    // 3. 设置路由
    setupRoutes();
    
    // 3. 启动服务器
    if (!startServer()) {
        logError("Failed to start web server");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ WebServerManager initialization completed");
    return true;
}

void WebServerManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🌐 Shutting down WebServerManager...");
    
    stopServer();
    
    m_initialized = false;
    Serial.println("✅ WebServerManager shutdown completed");
}

bool WebServerManager::isHealthy() const {
    return m_initialized && m_server && m_serverRunning;
}

void WebServerManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // 第一阶段：基本循环处理占位符
    // 第二阶段将实现完整的请求处理逻辑
}

// ==================== 依赖设置 ====================
void WebServerManager::setDataManager(DataManager* dataManager) {
    m_dataManager = dataManager;
    Serial.println("🌐 WebServerManager: DataManager dependency set");
}

void WebServerManager::setIRController(IRController* irController) {
    m_irController = irController;
    Serial.println("🌐 WebServerManager: IRController dependency set");
}

void WebServerManager::setTaskManager(TaskManager* taskManager) {
    m_taskManager = taskManager;
    Serial.println("🌐 WebServerManager: TaskManager dependency set");
}

void WebServerManager::setWSManager(WSManager* wsManager) {
    m_wsManager = wsManager;
    Serial.println("🌐 WebServerManager: WSManager dependency set");
}

void WebServerManager::setNetworkSecurity(NetworkSecurity* networkSecurity) {
    m_networkSecurity = networkSecurity;
    Serial.println("🌐 WebServerManager: NetworkSecurity dependency set");
}

// ==================== 服务器控制 ====================
bool WebServerManager::startServer() {
    if (!m_server) {
        logError("Server instance not created");
        return false;
    }
    
    if (m_serverRunning) {
        Serial.println("⚠️  Server already running");
        return true;
    }
    
    try {
        m_server->begin();
        m_serverRunning = true;
        Serial.printf("✅ Web server started on port %d\n", WEB_SERVER_PORT);
        return true;
    } catch (const std::exception& e) {
        logError("Failed to start server: " + String(e.what()));
        return false;
    }
}

void WebServerManager::stopServer() {
    if (m_server && m_serverRunning) {
        m_server->end();
        m_serverRunning = false;
        Serial.println("🌐 Web server stopped");
    }
}

bool WebServerManager::restartServer() {
    Serial.println("🌐 Restarting web server...");
    stopServer();
    delay(1000);
    return startServer();
}

DynamicJsonDocument WebServerManager::getServerStatus() {
    DynamicJsonDocument status(512);
    
    status["initialized"] = m_initialized;
    status["running"] = m_serverRunning;
    status["port"] = WEB_SERVER_PORT;
    status["total_requests"] = m_totalRequests;
    status["successful_requests"] = m_successfulRequests;
    status["failed_requests"] = m_failedRequests;
    status["success_rate"] = m_totalRequests > 0 ? (float)m_successfulRequests / m_totalRequests * 100 : 0;
    
    return status;
}

// ==================== 私有方法 ====================
void WebServerManager::setupRoutes() {
    if (!m_server) {
        return;
    }
    
    Serial.println("🌐 Setting up web server routes...");
    
    // 设置CORS
    setupCORS();
    
    // 设置静态文件服务
    setupStaticFiles();
    
    // 设置API路由
    setupAPIRoutes();
    
    Serial.println("✅ Web server routes configured");
}

void WebServerManager::setupStaticFiles() {
    // 第一阶段：基本静态文件服务
    m_server->serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // 处理404
    m_server->onNotFound([this](AsyncWebServerRequest* request) {
        logRequest(request, false);
        sendErrorResponse(request, "Not Found", 404);
    });
}

void WebServerManager::setupAPIRoutes() {
    Serial.println("🌐 Setting up comprehensive API routes...");

    // ==================== 系统API ====================
    m_server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        if (!validateRequestSecurity(request)) return;
        applyCORSHeaders(request);
        handleSystemAPI(request);
    });

    m_server->on("/api/system/info", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleSystemInfoAPI(request);
    });

    m_server->on("/api/system/restart", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSystemRestartAPI(request);
    });

    // ==================== 信号API ====================
    // 获取所有信号
    m_server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetAllSignalsAPI(request);
    });

    // 获取单个信号
    m_server->on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignalAPI(request);
    });

    // 创建信号
    m_server->on("/api/signals", HTTP_POST, [this](AsyncWebServerRequest* request) {
        // 需要处理请求体
    }, nullptr, [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
        handleCreateSignalAPI(request, data, len, index, total);
    });

    // 更新信号
    m_server->on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)$", HTTP_PUT, [this](AsyncWebServerRequest* request) {
        // 需要处理请求体
    }, nullptr, [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
        handleUpdateSignalAPI(request, data, len, index, total);
    });

    // 删除信号
    m_server->on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteSignalAPI(request);
    });

    // 发送信号
    m_server->on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)\\/send$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSendSignalAPI(request);
    });

    // ==================== 任务API ====================
    // 获取所有任务
    m_server->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetAllTasksAPI(request);
    });

    // 获取当前任务
    m_server->on("/api/tasks/current", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetCurrentTaskAPI(request);
    });

    // 创建任务
    m_server->on("/api/tasks", HTTP_POST, [this](AsyncWebServerRequest* request) {
        // 需要处理请求体
    }, nullptr, [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
        handleCreateTaskAPI(request, data, len, index, total);
    });

    // 执行所有信号任务
    m_server->on("/api/tasks/execute-all", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleExecuteAllSignalsAPI(request);
    });

    // 停止任务
    m_server->on("^\\/api\\/tasks\\/([a-zA-Z0-9_]+)\\/stop$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStopTaskAPI(request);
    });

    // 暂停任务
    m_server->on("^\\/api\\/tasks\\/([a-zA-Z0-9_]+)\\/pause$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handlePauseTaskAPI(request);
    });

    // 恢复任务
    m_server->on("^\\/api\\/tasks\\/([a-zA-Z0-9_]+)\\/resume$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleResumeTaskAPI(request);
    });

    // ==================== 定时器API ====================
    // 获取所有定时器
    m_server->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetAllTimersAPI(request);
    });

    // 创建定时器
    m_server->on("/api/timers", HTTP_POST, [this](AsyncWebServerRequest* request) {
        // 需要处理请求体
    }, nullptr, [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
        handleCreateTimerAPI(request, data, len, index, total);
    });

    // 删除定时器
    m_server->on("^\\/api\\/timers\\/([a-zA-Z0-9_]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteTimerAPI(request);
    });

    // ==================== 学习API ====================
    // 开始学习
    m_server->on("/api/learning/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStartLearningAPI(request);
    });

    // 停止学习
    m_server->on("/api/learning/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStopLearningAPI(request);
    });

    // 获取学习状态
    m_server->on("/api/learning/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetLearningStatusAPI(request);
    });

    // 获取学习到的信号
    m_server->on("/api/learning/result", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetLearnedSignalAPI(request);
    });

    Serial.println("✅ Comprehensive API routes configured");
}

void WebServerManager::setupCORS() {
    // 设置CORS头
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", CORS_ORIGIN);
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", CORS_METHODS);
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", CORS_HEADERS);
    
    // 处理OPTIONS请求
    m_server->on("/*", HTTP_OPTIONS, [this](AsyncWebServerRequest* request) {
        request->send(200);
    });
}

// ==================== API处理方法 ====================
void WebServerManager::handleSystemAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);
    
    DynamicJsonDocument response(512);
    response["status"] = "ok";
    response["uptime"] = millis();
    response["free_heap"] = ESP.getFreeHeap();
    response["system_mode"] = "Standard"; // 第一阶段固定值
    
    sendJSONResponse(request, response);
}

void WebServerManager::handleSignalAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);
    
    if (m_dataManager) {
        DynamicJsonDocument signals = m_dataManager->getAllSignals();
        sendJSONResponse(request, signals);
    } else {
        sendErrorResponse(request, "DataManager not available", 503);
    }
}

void WebServerManager::handleTaskAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);
    
    if (m_taskManager) {
        DynamicJsonDocument tasks = m_taskManager->getTaskQueue();
        sendJSONResponse(request, tasks);
    } else {
        sendErrorResponse(request, "TaskManager not available", 503);
    }
}

void WebServerManager::handleTimerAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);
    
    if (m_dataManager) {
        DynamicJsonDocument timers = m_dataManager->getAllTimers();
        sendJSONResponse(request, timers);
    } else {
        sendErrorResponse(request, "DataManager not available", 503);
    }
}

// ==================== 响应处理 ====================
void WebServerManager::sendJSONResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& response, int statusCode) {
    String jsonString;
    serializeJson(response, jsonString);
    
    request->send(statusCode, "application/json", jsonString);
    updateStatistics(true);
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, const String& error, int statusCode) {
    DynamicJsonDocument response(256);
    response["error"] = error;
    response["status"] = "error";
    response["code"] = statusCode;
    
    sendJSONResponse(request, response, statusCode);
    updateStatistics(false);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest* request, const String& message, const DynamicJsonDocument& data) {
    DynamicJsonDocument response(512);
    response["status"] = "success";
    response["message"] = message;
    
    if (!data.isNull()) {
        response["data"] = data;
    }
    
    sendJSONResponse(request, response);
}

// ==================== 工具方法 ====================
bool WebServerManager::validateRequestParams(AsyncWebServerRequest* request, const std::vector<String>& requiredParams) {
    for (const String& param : requiredParams) {
        if (!request->hasParam(param)) {
            return false;
        }
    }
    return true;
}

DynamicJsonDocument WebServerManager::parseRequestBody(AsyncWebServerRequest* request, const String& body) {
    DynamicJsonDocument doc(1024);
    
    DeserializationError error = deserializeJson(doc, body);
    if (error) {
        logError("JSON parsing error: " + String(error.c_str()));
        return DynamicJsonDocument(0);
    }
    
    return doc;
}

void WebServerManager::logRequest(AsyncWebServerRequest* request, bool success) {
    String clientIP = request->client()->remoteIP().toString();
    String method = request->methodToString();
    String url = request->url();

    Serial.printf("🌐 %s %s from %s - %s\n",
                 method.c_str(), url.c_str(), clientIP.c_str(),
                 success ? "SUCCESS" : "FAILED");

    // 记录安全相关信息
    if (m_networkSecurity) {
        m_networkSecurity->recordRequest(clientIP, url);
    }
}

void WebServerManager::logError(const String& error) {
    Serial.printf("❌ WebServerManager Error: %s\n", error.c_str());
}

void WebServerManager::updateStatistics(bool success) {
    m_totalRequests++;
    if (success) {
        m_successfulRequests++;
    } else {
        m_failedRequests++;
    }
}

// ==================== 安全中间件实现 ====================
bool WebServerManager::validateRequestSecurity(AsyncWebServerRequest* request) {
    if (!m_networkSecurity) {
        return true;  // 如果没有安全管理器，允许所有请求
    }

    String clientIP = request->client()->remoteIP().toString();

    // 1. 检查IP是否被阻止
    if (m_networkSecurity->isIPBlocked(clientIP)) {
        Serial.printf("🔒 Blocked IP attempted access: %s\n", clientIP.c_str());
        sendErrorResponse(request, "Access denied", 403);
        return false;
    }

    // 2. 检查速率限制
    if (!checkRateLimit(request)) {
        return false;
    }

    // 3. 检查CORS
    String origin = "";
    if (request->hasHeader("Origin")) {
        origin = request->getHeader("Origin")->value();
    }

    String method = request->methodToString();
    if (!m_networkSecurity->validateCORSRequest(origin, method)) {
        sendErrorResponse(request, "CORS policy violation", 403);
        return false;
    }

    // 4. 检查API认证（对于需要认证的端点）
    String url = request->url();
    if (url.startsWith("/api/") && !checkAPIAuthentication(request)) {
        return false;
    }

    return true;
}

bool WebServerManager::checkAPIAuthentication(AsyncWebServerRequest* request) {
    if (!m_networkSecurity || !ENABLE_AUTHENTICATION) {
        return true;  // 如果未启用认证，允许所有请求
    }

    // 检查API密钥
    String apiKey = "";
    if (request->hasHeader("X-API-Key")) {
        apiKey = request->getHeader("X-API-Key")->value();
    } else if (request->hasParam("api_key")) {
        apiKey = request->getParam("api_key")->value();
    }

    if (!m_networkSecurity->validateAPIKey(apiKey)) {
        sendErrorResponse(request, "Invalid or missing API key", 401);
        return false;
    }

    // 检查会话令牌（如果提供）
    String token = "";
    if (request->hasHeader("Authorization")) {
        String authHeader = request->getHeader("Authorization")->value();
        if (authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }
    }

    if (!token.isEmpty() && !m_networkSecurity->validateSessionToken(token)) {
        sendErrorResponse(request, "Invalid or expired session token", 401);
        return false;
    }

    return true;
}

bool WebServerManager::checkRateLimit(AsyncWebServerRequest* request) {
    if (!m_networkSecurity) {
        return true;
    }

    String clientIP = request->client()->remoteIP().toString();
    String endpoint = request->url();

    if (!m_networkSecurity->checkRateLimit(clientIP, endpoint)) {
        DynamicJsonDocument rateLimitStatus = m_networkSecurity->getRateLimitStatus(clientIP);

        DynamicJsonDocument response(256);
        response["error"] = "Rate limit exceeded";
        response["retry_after"] = 60;  // 建议1分钟后重试
        response["limit_info"] = rateLimitStatus;

        sendJSONResponse(request, response, 429);  // 429 Too Many Requests
        return false;
    }

    return true;
}

void WebServerManager::applyCORSHeaders(AsyncWebServerRequest* request) {
    if (!m_networkSecurity || !ENABLE_CORS) {
        return;
    }

    String origin = "";
    if (request->hasHeader("Origin")) {
        origin = request->getHeader("Origin")->value();
    }

    auto corsHeaders = m_networkSecurity->getCORSHeaders(origin);
    for (const auto& header : corsHeaders) {
        request->addInterestingHeader(header.first.c_str());
    }
}

// ==================== 具体API处理方法实现 ====================

// 系统API实现
void WebServerManager::handleSystemInfoAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    DynamicJsonDocument response(1024);
    response["system_mode"] = PSRAMManager::getSystemModeString(PSRAMManager::detectSystemMode());
    response["psram_available"] = PSRAMManager::isPSRAMAvailable();
    response["psram_size"] = PSRAMManager::getPSRAMSize();
    response["free_psram"] = PSRAMManager::getFreePSRAM();
    response["free_heap"] = ESP.getFreeHeap();
    response["chip_model"] = ESP.getChipModel();
    response["chip_revision"] = ESP.getChipRevision();
    response["cpu_frequency"] = ESP.getCpuFreqMHz();
    response["flash_size"] = ESP.getFlashChipSize();
    response["uptime"] = millis();
    response["version"] = FIRMWARE_VERSION;

    sendJSONResponse(request, response);
}

void WebServerManager::handleSystemRestartAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    DynamicJsonDocument response(256);
    response["status"] = "success";
    response["message"] = "System will restart in 3 seconds";

    sendJSONResponse(request, response);

    // 延迟重启
    delay(3000);
    ESP.restart();
}

// 信号API实现
void WebServerManager::handleGetAllSignalsAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        return;
    }

    DynamicJsonDocument signals = m_dataManager->getAllSignals();
    DynamicJsonDocument response(signals.memoryUsage() + 256);

    response["status"] = "success";
    response["count"] = m_dataManager->getSignalCount();
    response["signals"] = signals;

    sendJSONResponse(request, response);
}

void WebServerManager::handleGetSignalAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_dataManager) {
        sendErrorResponse(request, "DataManager not available", 503);
        return;
    }

    // 从URL路径中提取信号ID
    String signalId = request->pathArg(0);

    if (signalId.isEmpty()) {
        sendErrorResponse(request, "Signal ID is required", 400);
        return;
    }

    DynamicJsonDocument signal = m_dataManager->getSignal(signalId);

    if (signal["found"] == false) {
        sendErrorResponse(request, "Signal not found", 404);
        return;
    }

    DynamicJsonDocument response(signal.memoryUsage() + 256);
    response["status"] = "success";
    response["signal"] = signal;

    sendJSONResponse(request, response);
}

void WebServerManager::handleCreateSignalAPI(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    // 处理分块上传的请求体
    static String requestBody = "";

    if (index == 0) {
        requestBody = "";
    }

    for (size_t i = 0; i < len; i++) {
        requestBody += (char)data[i];
    }

    if (index + len == total) {
        // 请求体接收完成，开始处理
        logRequest(request, true);

        if (!m_dataManager) {
            sendErrorResponse(request, "DataManager not available", 503);
            return;
        }

        DynamicJsonDocument signalData = parseRequestBody(request, requestBody);
        if (signalData.isNull()) {
            sendErrorResponse(request, "Invalid JSON data", 400);
            return;
        }

        // 验证必需字段
        if (!signalData.containsKey("name") || !signalData.containsKey("signalCode")) {
            sendErrorResponse(request, "Missing required fields: name, signalCode", 400);
            return;
        }

        String signalId = m_dataManager->createSignal(signalData);
        if (signalId.isEmpty()) {
            sendErrorResponse(request, "Failed to create signal", 500);
            return;
        }

        DynamicJsonDocument response(256);
        response["status"] = "success";
        response["message"] = "Signal created successfully";
        response["signal_id"] = signalId;

        sendJSONResponse(request, response, 201);

        // 通知WebSocket客户端
        if (m_wsManager) {
            DynamicJsonDocument wsData(256);
            wsData["signal_id"] = signalId;
            wsData["action"] = "created";
            m_wsManager->notifySignalLearned(wsData);
        }
    }
}

void WebServerManager::handleSendSignalAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_dataManager || !m_irController) {
        sendErrorResponse(request, "Required services not available", 503);
        return;
    }

    // 从URL路径中提取信号ID
    String signalId = request->pathArg(0);

    if (signalId.isEmpty()) {
        sendErrorResponse(request, "Signal ID is required", 400);
        return;
    }

    // 获取信号数据
    DynamicJsonDocument signal = m_dataManager->getSignal(signalId);
    if (signal["found"] == false) {
        sendErrorResponse(request, "Signal not found", 404);
        return;
    }

    // 发送信号
    String signalCode = signal["signalCode"];
    String protocol = signal["protocol"] | "NEC";

    bool success = m_irController->sendSignal(signalCode, protocol);

    if (success) {
        DynamicJsonDocument response(256);
        response["status"] = "success";
        response["message"] = "Signal sent successfully";
        response["signal_id"] = signalId;

        sendJSONResponse(request, response);

        // 通知WebSocket客户端
        if (m_wsManager) {
            DynamicJsonDocument wsData(256);
            wsData["signal_id"] = signalId;
            wsData["signal_name"] = signal["name"];
            wsData["signal_code"] = signalCode;
            wsData["status"] = "sent";
            m_wsManager->notifySignalSending(wsData);
        }
    } else {
        sendErrorResponse(request, "Failed to send signal", 500);
    }
}

// 任务API实现
void WebServerManager::handleGetAllTasksAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_taskManager) {
        sendErrorResponse(request, "TaskManager not available", 503);
        return;
    }

    DynamicJsonDocument tasks = m_taskManager->getTaskQueue();
    DynamicJsonDocument response(tasks.memoryUsage() + 256);

    response["status"] = "success";
    response["tasks"] = tasks;

    sendJSONResponse(request, response);
}

void WebServerManager::handleGetCurrentTaskAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_taskManager) {
        sendErrorResponse(request, "TaskManager not available", 503);
        return;
    }

    DynamicJsonDocument currentTask = m_taskManager->getCurrentTask();
    DynamicJsonDocument response(currentTask.memoryUsage() + 256);

    response["status"] = "success";
    response["current_task"] = currentTask;

    sendJSONResponse(request, response);
}

void WebServerManager::handleExecuteAllSignalsAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_taskManager) {
        sendErrorResponse(request, "TaskManager not available", 503);
        return;
    }

    // 获取查询参数
    bool loopMode = request->hasParam("loop") ? request->getParam("loop")->value() == "true" : false;
    int interval = request->hasParam("interval") ? request->getParam("interval")->value().toInt() : 100;

    String taskId = m_taskManager->executeAllSignals(loopMode, interval);

    if (taskId.isEmpty()) {
        sendErrorResponse(request, "Failed to start task", 500);
        return;
    }

    DynamicJsonDocument response(256);
    response["status"] = "success";
    response["message"] = "Task started successfully";
    response["task_id"] = taskId;
    response["loop_mode"] = loopMode;
    response["interval"] = interval;

    sendJSONResponse(request, response);

    // 通知WebSocket客户端
    if (m_wsManager) {
        DynamicJsonDocument wsData(256);
        wsData["task_id"] = taskId;
        wsData["task_name"] = "Execute All Signals";
        wsData["task_type"] = "all";
        wsData["loop_mode"] = loopMode;
        m_wsManager->notifyTaskStarted(wsData);
    }
}

void WebServerManager::handleStopTaskAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_taskManager) {
        sendErrorResponse(request, "TaskManager not available", 503);
        return;
    }

    String taskId = request->pathArg(0);

    if (taskId.isEmpty()) {
        sendErrorResponse(request, "Task ID is required", 400);
        return;
    }

    bool success = m_taskManager->stopTask(taskId);

    if (success) {
        DynamicJsonDocument response(256);
        response["status"] = "success";
        response["message"] = "Task stopped successfully";
        response["task_id"] = taskId;

        sendJSONResponse(request, response);

        // 通知WebSocket客户端
        if (m_wsManager) {
            DynamicJsonDocument wsData(256);
            wsData["task_id"] = taskId;
            wsData["result"] = "stopped";
            m_wsManager->notifyTaskCompleted(wsData);
        }
    } else {
        sendErrorResponse(request, "Failed to stop task or task not found", 404);
    }
}

// 学习API实现
void WebServerManager::handleStartLearningAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        return;
    }

    // 获取超时参数
    unsigned long timeout = request->hasParam("timeout") ?
                           request->getParam("timeout")->value().toInt() : 10000;

    bool success = m_irController->startLearning(timeout);

    if (success) {
        DynamicJsonDocument response(256);
        response["status"] = "success";
        response["message"] = "Learning mode started";
        response["timeout"] = timeout;

        sendJSONResponse(request, response);

        // 通知WebSocket客户端
        if (m_wsManager) {
            DynamicJsonDocument wsData(256);
            wsData["is_learning"] = true;
            wsData["timeout"] = timeout;
            m_wsManager->notifyLearningStatusChanged(wsData);
        }
    } else {
        sendErrorResponse(request, "Failed to start learning mode", 500);
    }
}

void WebServerManager::handleStopLearningAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        return;
    }

    m_irController->stopLearning();

    DynamicJsonDocument response(256);
    response["status"] = "success";
    response["message"] = "Learning mode stopped";

    sendJSONResponse(request, response);

    // 通知WebSocket客户端
    if (m_wsManager) {
        DynamicJsonDocument wsData(256);
        wsData["is_learning"] = false;
        m_wsManager->notifyLearningStatusChanged(wsData);
    }
}

void WebServerManager::handleGetLearningStatusAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        return;
    }

    DynamicJsonDocument response(256);
    response["status"] = "success";
    response["is_learning"] = m_irController->isLearning();

    sendJSONResponse(request, response);
}

void WebServerManager::handleGetLearnedSignalAPI(AsyncWebServerRequest* request) {
    logRequest(request, true);

    if (!m_irController) {
        sendErrorResponse(request, "IRController not available", 503);
        return;
    }

    DynamicJsonDocument learnedSignal = m_irController->getLearnedSignal();
    DynamicJsonDocument response(learnedSignal.memoryUsage() + 256);

    response["status"] = "success";
    response["learned_signal"] = learnedSignal;

    sendJSONResponse(request, response);
}

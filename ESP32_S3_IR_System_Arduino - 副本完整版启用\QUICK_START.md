# 🚀 Quick Start Guide - ESP32-S3 IR Control System

## ⚡ 5-Minute Setup

### Step 1: Arduino IDE Configuration (2 minutes)

1. **Open Arduino IDE**
2. **Select Board:**
   - Tools → Board → ESP32 Arduino → **ESP32S3 Dev Module**
3. **Configure Settings:**
   - Flash Mode: **QIO** ⚠️ Critical for PSRAM
   - Flash Size: **16MB (128Mb)**
   - PSRAM: **OPI PSRAM** ⚠️ Critical
   - Flash Frequency: **80MHz**
   - Upload Speed: **460800**
   - Core Debug Level: **None**

### Step 2: Install Libraries (2 minutes)

**Quick Install via Library Manager:**
1. Sketch → Include Library → Manage Libraries
2. Install these 4 libraries:
   - **ArduinoJson** (6.21.3) by <PERSON><PERSON>
   - **ESP Async WebServer** (3.7.8+) by lacamera
   - **AsyncTCP** (3.4.4+) by dvarrel
   - **IRremoteESP8266** (2.8.6) by <PERSON>

### Step 3: Upload Project (1 minute)

1. **Open:** `ESP32_S3_IR_System_Arduino.ino`
2. **Select COM Port:** Tools → Port → COM6 (or your port)
3. **Upload:** Click Upload button (→)
4. **Upload SPIFFS:** Tools → ESP32 Sketch Data Upload

## ✅ Success Verification

### Expected Serial Output:
```
ESP32-S3 IR Control System Starting...
✅ PSRAM FOUND - Starting comprehensive tests
📊 Total PSRAM: 8388608 bytes (8.00 MB)
✅ 1KB allocation: SUCCESS
✅ 1MB allocation: SUCCESS
✅ Read/Write integrity: SUCCESS
🎉 SYSTEM INITIALIZATION COMPLETED! 🎉
📡 IR Transmitter: GPIO21
📡 IR Receiver: GPIO14
🌐 Web Server: http://***********
```

### Web Interface Access:
- **URL:** http://***********
- **Features:** IR signal learning, sending, scheduling

## 🔧 Hardware Setup

### Required Connections:
- **IR LED (Transmitter):** GPIO 21
- **IR Receiver:** GPIO 14
- **Power:** USB-C or 5V external

### Optional:
- **Status LED:** GPIO 2 (built-in)
- **Reset Button:** EN pin

## 🌐 Network Configuration

### Default Mode: Access Point (AP)
- **SSID:** ESP32-S3-IR-System
- **Password:** 12345678
- **IP:** ***********

### Station Mode (Optional):
- Modify WiFi credentials in code
- Connect to existing network

## 📱 Web Interface Features

### Main Dashboard:
- **System Status:** PSRAM, WiFi, uptime
- **IR Control:** Send learned signals
- **Signal Manager:** Learn new IR signals
- **Timer Settings:** Schedule automatic actions
- **System Monitor:** Real-time diagnostics

### API Endpoints:
- `GET /api/signals` - List all signals
- `POST /api/signals/send` - Send IR signal
- `POST /api/signals/learn` - Learn new signal
- `GET /api/system/status` - System information

## 🚨 Troubleshooting

### PSRAM Not Found:
```
❌ PSRAM NOT FOUND!
```
**Solution:** Check Arduino IDE settings:
- Board: ESP32S3 Dev Module
- PSRAM: OPI PSRAM
- Flash Mode: QIO

### Compilation Errors:
```
fatal error: ArduinoJson.h: No such file
```
**Solution:** Install missing libraries via Library Manager

### Upload Errors:
```
Failed to connect to ESP32
```
**Solution:** 
- Check COM port selection
- Press and hold BOOT button during upload
- Try different upload speed (115200)

### Web Interface Not Loading:
```
Connection refused
```
**Solution:**
- Check serial output for IP address
- Ensure SPIFFS data uploaded
- Try http://***********

## 📊 Performance Metrics

### Memory Usage:
- **PSRAM:** 8MB available
- **Flash:** ~2MB used, 14MB available
- **Heap:** ~200KB available

### Network Performance:
- **WiFi Range:** 50-100 meters
- **Web Response:** <100ms
- **WebSocket Latency:** <50ms

### IR Performance:
- **Range:** 5-10 meters
- **Protocols:** NEC, Sony, Samsung, LG, RC5, RC6
- **Learning Time:** 1-3 seconds
- **Send Latency:** <10ms

## 🔄 Next Steps

1. **Test PSRAM:** Verify 8MB PSRAM detection
2. **Learn IR Signals:** Use web interface to capture remote controls
3. **Create Schedules:** Set up automated IR commands
4. **Monitor System:** Check real-time status and logs
5. **Customize:** Modify code for specific requirements

## 📞 Support

### Documentation:
- `README_Arduino_Setup.md` - Detailed setup guide
- `Library_Installation_Guide.md` - Library installation help

### Common Issues:
- PSRAM configuration problems
- Library compatibility issues
- Network connectivity problems
- IR signal learning difficulties

### Debug Mode:
Enable detailed logging by changing:
```cpp
// In Arduino IDE: Tools → Core Debug Level → Verbose
```

/*
 * Web Server Manager Implementation - ESP32-S3 IR Control System
 * Fully compatible with ESPAsyncWebServer 3.7.8 API
 * Handles HTTP routes, API endpoints, and static file serving
 */

#include "web_server_manager.h"
#include "data_manager.h"
#include "ir_controller.h"
#include "task_manager.h"
#include "websocket_manager.h"
#include <WiFi.h>

WebServerManager::WebServerManager() {
    server = nullptr;
    webSocket = nullptr;
    isInitialized = false;
    
    dataManager = nullptr;
    irController = nullptr;
    taskManager = nullptr;
    wsManager = nullptr;
    
    totalRequests = 0;
    totalErrors = 0;
    startTime = 0;
}

WebServerManager::~WebServerManager() {
    cleanup();
}

bool WebServerManager::initialize(AsyncWebServer* serverPtr, AsyncWebSocket* wsPtr) {
    if (!serverPtr) {
        Serial.println("ERROR: Server pointer is null");
        return false;
    }
    
    server = serverPtr;
    webSocket = wsPtr;
    startTime = millis();
    
    Serial.println("Initializing Web Server Manager...");
    
    // Setup all routes
    setupRoutes();
    
    isInitialized = true;
    
    Serial.println("✅ Web Server Manager initialized successfully");
    Serial.println("🌐 Available endpoints:");
    Serial.println("   GET  /                    - Main page");
    Serial.println("   GET  /api/system/status   - System status");
    Serial.println("   GET  /api/signals         - Get all signals");
    Serial.println("   POST /api/signals/send    - Send signal");
    Serial.println("   POST /api/signals/learn   - Learn signal");
    Serial.println("   GET  /api/tasks           - Get all tasks");
    Serial.println("   POST /api/tasks           - Create task");
    Serial.println("   GET  /api/timers          - Get all timers");
    Serial.println("   POST /api/timers          - Create timer");
    
    return true;
}

void WebServerManager::cleanup() {
    isInitialized = false;
    server = nullptr;
    webSocket = nullptr;
}

// ==================== Module Reference Setters ====================

void WebServerManager::setDataManager(DataManager* dm) {
    dataManager = dm;
}

void WebServerManager::setIRController(IRController* ir) {
    irController = ir;
}

void WebServerManager::setTaskManager(TaskManager* tm) {
    taskManager = tm;
}

void WebServerManager::setWebSocketManager(WebSocketManager* wsm) {
    wsManager = wsm;
}

// ==================== Status Query Methods ====================

bool WebServerManager::isRunning() const {
    return isInitialized && server != nullptr;
}

unsigned long WebServerManager::getTotalRequests() const {
    return totalRequests;
}

unsigned long WebServerManager::getTotalErrors() const {
    return totalErrors;
}

unsigned long WebServerManager::getUptime() const {
    return millis() - startTime;
}

DynamicJsonDocument WebServerManager::getServerStatistics() const {
    DynamicJsonDocument stats(512);
    
    stats["total_requests"] = totalRequests;
    stats["total_errors"] = totalErrors;
    stats["uptime"] = getUptime();
    stats["start_time"] = startTime;
    stats["is_running"] = isRunning();
    
    return stats;
}

// ==================== Route Setup ====================

void WebServerManager::setupRoutes() {
    if (!server) return;
    
    Serial.println("Setting up web server routes...");
    
    // Setup static routes
    setupStaticRoutes();
    
    // Setup API routes
    setupAPIRoutes();
    
    // Add WebSocket if available
    if (webSocket) {
        server->addHandler(webSocket);
        Serial.println("✅ WebSocket handler added");
    }
    
    // Enable CORS
    enableCORS();
    
    Serial.println("✅ All routes configured");
}

void WebServerManager::setupStaticRoutes() {
    // Root route
    server->on("/", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleRoot(request);
    });

    // Test route
    server->on("/test", HTTP_GET, [](AsyncWebServerRequest *request) {
        request->send(200, "text/plain", "ESP32-S3 Web Server is working!");
    });

    // Simple API test route
    server->on("/api/test", HTTP_GET, [](AsyncWebServerRequest *request) {
        DynamicJsonDocument response(256);
        response["success"] = true;
        response["message"] = "API is working";
        response["timestamp"] = millis();

        String jsonString;
        serializeJson(response, jsonString);
        request->send(200, "application/json", jsonString);
    });
    
    // Serve static files from SPIFFS
    server->serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // Handle 404
    server->onNotFound([this](AsyncWebServerRequest *request) {
        this->handleNotFound(request);
    });
    
    Serial.println("✅ Static routes configured");
}

void WebServerManager::setupAPIRoutes() {
    // System API
    server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleSystemStatus(request);
    });
    
    server->on("/api/system/info", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleSystemInfo(request);
    });
    
    server->on("/api/system/restart", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleSystemRestart(request);
    });
    
    // IR Signal API
    server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetSignals(request);
    });
    
    server->on("/api/signals/send", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleSendSignal(request);
    });
    
    server->on("/api/signals/learn", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleLearnSignal(request);
    });
    
    server->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleStopLearning(request);
    });
    
    server->on("/api/signals/learn/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetLearningStatus(request);
    });
    
    server->on("/api/signals/batch", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleBatchSendSignals(request);
    });
    
    // Task API
    server->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetTasks(request);
    });
    
    server->on("/api/tasks", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleCreateTask(request);
    });
    
    server->on("/api/tasks/control", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleControlTask(request);
    });
    
    server->on("/api/tasks/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetTaskStatus(request);
    });
    
    // Timer API
    server->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetTimers(request);
    });
    
    server->on("/api/timers", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleCreateTimer(request);
    });
    
    server->on("/api/timers/toggle", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleToggleTimer(request);
    });
    
    // Data API
    server->on("/api/data/export", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleExportData(request);
    });
    
    server->on("/api/data/import", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleImportData(request);
    });
    
    server->on("/api/data/backup", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleBackupData(request);
    });
    
    server->on("/api/data/clear", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleClearData(request);
    });
    
    // WebSocket API
    server->on("/api/websocket/stats", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleWebSocketStats(request);
    });
    
    server->on("/api/websocket/clients", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleWebSocketClients(request);
    });
    
    server->on("/api/websocket/broadcast", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleWebSocketBroadcast(request);
    });
    
    // Configuration API
    server->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest *request) {
        this->handleGetConfig(request);
    });
    
    server->on("/api/config", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleSetConfig(request);
    });
    
    server->on("/api/config/reset", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleResetConfig(request);
    });

    // Batch API (前端需要的批量请求端点)
    server->on("/api/batch", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleBatchRequests(request);
    });

    // 删除信号API (前端需要)
    server->on("/api/signals/delete", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleDeleteSignal(request);
    });

    // 更新定时器API (前端需要)
    server->on("/api/timers/update", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleUpdateTimer(request);
    });

    // 删除定时器API (前端需要)
    server->on("/api/timers/delete", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleDeleteTimer(request);
    });

    // 删除任务API (前端需要)
    server->on("/api/tasks/delete", HTTP_POST, [this](AsyncWebServerRequest *request) {
        this->handleDeleteTask(request);
    });

    Serial.println("✅ API routes configured");
}

// ==================== CORS and Security ====================

void WebServerManager::enableCORS() {
    // Add CORS headers to all responses
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
    
    // Handle OPTIONS requests
    server->on("/*", HTTP_OPTIONS, [](AsyncWebServerRequest *request) {
        request->send(200);
    });
    
    Serial.println("✅ CORS enabled");
}

void WebServerManager::disableCORS() {
    // Note: DefaultHeaders cannot be easily removed, so this is a placeholder
    Serial.println("⚠️  CORS disable not implemented (headers already set)");
}

// ==================== Server Management ====================

void WebServerManager::sendServerStats() {
    if (wsManager) {
        DynamicJsonDocument stats = getServerStatistics();
        wsManager->broadcastMessage("server_stats", stats);
    }
}

void WebServerManager::handleServerError(const String& error) {
    totalErrors++;
    logError(error);
    
    if (wsManager) {
        wsManager->sendNotification("Server error: " + error, "error");
    }
}

// ==================== Request Handlers ====================

void WebServerManager::handleRoot(AsyncWebServerRequest *request) {
    logRequest(request);
    Serial.println("🌐 Root request received!");

    if (SPIFFS.exists("/index.html")) {
        Serial.println("✅ Serving frontend from SPIFFS");
        request->send(SPIFFS, "/index.html", "text/html");
    } else {
        Serial.println("⚠️ Frontend not found, serving default page");
        String html = generateDefaultHTML();
        request->send(200, "text/html", html);
    }
}

void WebServerManager::handleNotFound(AsyncWebServerRequest *request) {
    logRequest(request);

    String message = "File Not Found\n\n";
    message += "URI: " + request->url() + "\n";
    message += "Method: " + String((request->method() == HTTP_GET) ? "GET" : "POST") + "\n";
    message += "Arguments: " + String(request->args()) + "\n";

    for (uint8_t i = 0; i < request->args(); i++) {
        message += " " + request->argName(i) + ": " + request->arg(i) + "\n";
    }

    request->send(404, "text/plain", message);
}

// ==================== System API ====================

void WebServerManager::handleSystemStatus(AsyncWebServerRequest *request) {
    logRequest(request);

    DynamicJsonDocument response(1024);

    // 标准响应格式
    response["success"] = true;
    response["message"] = "System status retrieved successfully";
    response["timestamp"] = millis();

    // 系统状态数据
    DynamicJsonDocument data(512);
    data["system"] = "ESP32-S3 IR Control System";
    data["version"] = "1.0.0";
    data["uptime"] = getUptime();
    data["free_heap"] = ESP.getFreeHeap();
    data["total_heap"] = ESP.getHeapSize();
    data["wifi_connected"] = (WiFi.status() == WL_CONNECTED);

    if (WiFi.status() == WL_CONNECTED) {
        data["wifi_ip"] = WiFi.localIP().toString();
        data["wifi_rssi"] = WiFi.RSSI();
    }

    if (dataManager) {
        data["signal_count"] = dataManager->getSignalCount();
        data["timer_count"] = dataManager->getTimerCount();
        data["task_count"] = dataManager->getTaskCount();
    }

    if (wsManager) {
        data["active_clients"] = wsManager->getClientCount();
    }

    if (irController) {
        data["ir_hardware_ready"] = irController->isHardwareReady();
        data["learning_state"] = static_cast<int>(irController->getLearningState());
        data["transmission_state"] = static_cast<int>(irController->getTransmissionState());
    }

    response["data"] = data;
    sendJsonResponse(request, response);
}

void WebServerManager::handleSystemInfo(AsyncWebServerRequest *request) {
    logRequest(request);

    DynamicJsonDocument info(512);

    info["chip_model"] = ESP.getChipModel();
    info["chip_revision"] = ESP.getChipRevision();
    info["chip_cores"] = ESP.getChipCores();
    info["cpu_freq_mhz"] = ESP.getCpuFreqMHz();
    info["flash_size"] = ESP.getFlashChipSize();
    info["flash_speed"] = ESP.getFlashChipSpeed();
    info["sdk_version"] = ESP.getSdkVersion();
    info["arduino_version"] = ARDUINO;

    sendJsonResponse(request, info);
}

void WebServerManager::handleSystemRestart(AsyncWebServerRequest *request) {
    logRequest(request);

    DynamicJsonDocument response(128);
    response["message"] = "System restart initiated";
    response["timestamp"] = millis();

    sendJsonResponse(request, response);

    // Restart after a short delay
    delay(1000);
    ESP.restart();
}

// ==================== IR Signal API ====================

void WebServerManager::handleGetSignals(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    DynamicJsonDocument signals = dataManager->getSignalsJSON();
    sendJsonResponse(request, signals);
}

void WebServerManager::handleSendSignal(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!irController) {
        sendErrorResponse(request, "IR controller not available", 503);
        return;
    }

    // 前端通过POST body发送JSON数据，需要从body中解析
    // 由于ESPAsyncWebServer的限制，这里先尝试从URL参数获取
    bool success = false;
    String signalId = getRequiredParam(request, "signalId", success);

    if (!success) {
        // 尝试其他可能的参数名
        signalId = getRequiredParam(request, "id", success);
        if (!success) {
            signalId = getRequiredParam(request, "signal_id", success);
            if (!success) {
                // 如果URL参数都没有，返回错误（实际项目中需要实现body解析）
                sendErrorResponse(request, "Missing signalId parameter. Please use URL parameter: ?signalId=xxx", 400);
                return;
            }
        }
    }

    bool result = irController->sendSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["signal_id"] = signalId;
    response["timestamp"] = millis();

    if (result) {
        response["message"] = "Signal sent successfully";
        sendJsonResponse(request, response);
    } else {
        response["message"] = "Failed to send signal";
        sendJsonResponse(request, response, 500);
    }
}

void WebServerManager::handleLearnSignal(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!irController) {
        sendErrorResponse(request, "IR controller not available", 503);
        return;
    }

    bool success = false;
    // 前端发送的是 "name" 参数，不是 "signal_name"
    String signalName = getRequiredParam(request, "name", success);

    if (!success) {
        // 尝试旧的参数名作为备选
        signalName = getRequiredParam(request, "signal_name", success);
        if (!success) {
            sendErrorResponse(request, "Missing name or signal_name parameter", 400);
            return;
        }
    }

    bool result = irController->startLearning(signalName);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["signal_name"] = signalName;
    response["timestamp"] = millis();

    if (result) {
        response["message"] = "Learning started";
        response["timeout_ms"] = 30000; // Default timeout
        sendJsonResponse(request, response);
    } else {
        response["message"] = "Failed to start learning";
        sendJsonResponse(request, response, 500);
    }
}

void WebServerManager::handleStopLearning(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!irController) {
        sendErrorResponse(request, "IR controller not available", 503);
        return;
    }

    bool result = irController->stopLearning();

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["message"] = result ? "Learning stopped" : "Failed to stop learning";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

void WebServerManager::handleGetLearningStatus(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!irController) {
        sendErrorResponse(request, "IR controller not available", 503);
        return;
    }

    DynamicJsonDocument status(256);
    status["learning_state"] = static_cast<int>(irController->getLearningState());
    status["current_signal"] = irController->getCurrentLearningSignal();
    status["elapsed_ms"] = irController->getLearningElapsed();
    status["remaining_ms"] = irController->getLearningRemaining();
    status["timestamp"] = millis();

    sendJsonResponse(request, status);
}

void WebServerManager::handleBatchSendSignals(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!irController) {
        sendErrorResponse(request, "IR controller not available", 503);
        return;
    }

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    // 从URL参数获取信号ID列表 (简化实现)
    String signalIds = getOptionalParam(request, "signal_ids", "");
    int delayMs = getOptionalParam(request, "delay", "500").toInt();

    if (signalIds.isEmpty()) {
        sendErrorResponse(request, "Missing signal_ids parameter", 400);
        return;
    }

    // 解析信号ID列表 (逗号分隔)
    std::vector<String> signalIdList;
    int startIndex = 0;
    while (startIndex < signalIds.length()) {
        int commaIndex = signalIds.indexOf(',', startIndex);
        String signalId;
        if (commaIndex == -1) {
            signalId = signalIds.substring(startIndex);
        } else {
            signalId = signalIds.substring(startIndex, commaIndex);
        }
        signalId.trim();
        if (signalId.length() > 0) {
            signalIdList.push_back(signalId);
        }
        if (commaIndex == -1) break;
        startIndex = commaIndex + 1;
    }

    if (signalIdList.empty()) {
        sendErrorResponse(request, "No valid signal IDs provided", 400);
        return;
    }

    // 创建批量发送任务
    String taskId = taskManager->createBatchSendTask(signalIdList, delayMs, TaskPriority::NORMAL);

    if (taskId.isEmpty()) {
        sendErrorResponse(request, "Failed to create batch send task", 500);
        return;
    }

    // 启动任务
    bool started = taskManager->startTask(taskId);

    DynamicJsonDocument response(256);
    response["success"] = started;
    response["task_id"] = taskId;
    response["signal_count"] = signalIdList.size();
    response["delay_ms"] = delayMs;
    response["message"] = started ? "Batch send task created and started" : "Task created but failed to start";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, started ? 200 : 500);
}

void WebServerManager::handleDeleteSignal(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    bool success = false;
    String signalId = getRequiredParam(request, "signal_id", success);

    if (!success) {
        sendErrorResponse(request, "Missing signal_id parameter", 400);
        return;
    }

    bool result = dataManager->deleteSignal(signalId);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["signal_id"] = signalId;
    response["message"] = result ? "Signal deleted" : "Failed to delete signal";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

// ==================== Task API ====================

void WebServerManager::handleGetTasks(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    DynamicJsonDocument tasks = taskManager->getTasksJSON();
    sendJsonResponse(request, tasks);
}

void WebServerManager::handleCreateTask(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    // For now, return not implemented (would need request body parsing)
    sendErrorResponse(request, "Task creation not implemented yet", 501);
}

void WebServerManager::handleControlTask(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    bool success = false;
    String taskId = getRequiredParam(request, "task_id", success);
    String action = getRequiredParam(request, "action", success);

    if (!success) {
        sendErrorResponse(request, "Missing task_id or action parameter", 400);
        return;
    }

    bool result = false;
    String message = "";

    if (action == "start") {
        result = taskManager->startTask(taskId);
        message = result ? "Task started" : "Failed to start task";
    } else if (action == "pause") {
        result = taskManager->pauseTask(taskId);
        message = result ? "Task paused" : "Failed to pause task";
    } else if (action == "resume") {
        result = taskManager->resumeTask(taskId);
        message = result ? "Task resumed" : "Failed to resume task";
    } else if (action == "cancel") {
        result = taskManager->cancelTask(taskId);
        message = result ? "Task cancelled" : "Failed to cancel task";
    } else {
        sendErrorResponse(request, "Invalid action: " + action, 400);
        return;
    }

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["task_id"] = taskId;
    response["action"] = action;
    response["message"] = message;
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

void WebServerManager::handleGetTaskStatus(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    DynamicJsonDocument status = taskManager->getStatistics();
    sendJsonResponse(request, status);
}

void WebServerManager::handleDeleteTask(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!taskManager) {
        sendErrorResponse(request, "Task manager not available", 503);
        return;
    }

    bool success = false;
    String taskId = getRequiredParam(request, "task_id", success);

    if (!success) {
        sendErrorResponse(request, "Missing task_id parameter", 400);
        return;
    }

    bool result = taskManager->deleteTask(taskId);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["task_id"] = taskId;
    response["message"] = result ? "Task deleted" : "Failed to delete task";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

// ==================== Timer API ====================

void WebServerManager::handleGetTimers(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    DynamicJsonDocument timers = dataManager->getTimersJSON();
    sendJsonResponse(request, timers);
}

void WebServerManager::handleCreateTimer(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    // For now, return not implemented (would need request body parsing)
    sendErrorResponse(request, "Timer creation not implemented yet", 501);
}

void WebServerManager::handleUpdateTimer(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    // For now, return not implemented
    sendErrorResponse(request, "Timer update not implemented yet", 501);
}

void WebServerManager::handleDeleteTimer(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    bool success = false;
    String timerId = getRequiredParam(request, "timer_id", success);

    if (!success) {
        sendErrorResponse(request, "Missing timer_id parameter", 400);
        return;
    }

    bool result = dataManager->deleteTimer(timerId);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["timer_id"] = timerId;
    response["message"] = result ? "Timer deleted" : "Failed to delete timer";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

void WebServerManager::handleToggleTimer(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    bool success = false;
    String timerId = getRequiredParam(request, "timer_id", success);

    if (!success) {
        sendErrorResponse(request, "Missing timer_id parameter", 400);
        return;
    }

    TimerData* timer = dataManager->getTimer(timerId);
    if (!timer) {
        sendErrorResponse(request, "Timer not found", 404);
        return;
    }

    // Toggle timer active state
    DynamicJsonDocument updateData(128);
    updateData["is_active"] = !timer->isActive;

    bool result = dataManager->updateTimer(timerId, updateData);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["timer_id"] = timerId;
    response["is_active"] = !timer->isActive;
    response["message"] = result ? "Timer toggled" : "Failed to toggle timer";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

// ==================== Data API ====================

void WebServerManager::handleExportData(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    DynamicJsonDocument exportData = dataManager->exportAllData();
    sendJsonResponse(request, exportData);
}

void WebServerManager::handleImportData(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    // For now, return not implemented (would need request body parsing)
    sendErrorResponse(request, "Data import not implemented yet", 501);
}

void WebServerManager::handleBackupData(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    bool result = dataManager->backupData();

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["message"] = result ? "Data backup completed" : "Failed to backup data";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

void WebServerManager::handleRestoreData(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    bool result = dataManager->restoreData();

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["message"] = result ? "Data restore completed" : "Failed to restore data";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

void WebServerManager::handleClearData(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    String dataType = getOptionalParam(request, "type", "all");

    if (dataType == "signals") {
        dataManager->clearAllSignals();
    } else if (dataType == "timers") {
        dataManager->clearAllTimers();
    } else if (dataType == "tasks") {
        dataManager->clearAllTasks();
    } else if (dataType == "all") {
        dataManager->clearAllData();
    } else {
        sendErrorResponse(request, "Invalid data type: " + dataType, 400);
        return;
    }

    DynamicJsonDocument response(256);
    response["success"] = true;
    response["type"] = dataType;
    response["message"] = "Data cleared successfully";
    response["timestamp"] = millis();

    sendJsonResponse(request, response);
}

// ==================== WebSocket API ====================

void WebServerManager::handleWebSocketStats(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!wsManager) {
        sendErrorResponse(request, "WebSocket manager not available", 503);
        return;
    }

    DynamicJsonDocument stats = wsManager->getStatistics();
    sendJsonResponse(request, stats);
}

void WebServerManager::handleWebSocketClients(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!wsManager) {
        sendErrorResponse(request, "WebSocket manager not available", 503);
        return;
    }

    DynamicJsonDocument clients(1024);
    std::vector<WSClient> activeClients = wsManager->getActiveClients();

    JsonArray clientsArray = clients.createNestedArray("clients");
    for (const auto& client : activeClients) {
        DynamicJsonDocument clientInfo(128);
        clientInfo["id"] = client.id;
        clientInfo["ip"] = client.ip;
        clientInfo["connected_at"] = client.connectedAt;
        clientInfo["last_ping"] = client.lastPing;
        clientInfo["is_active"] = client.isActive;

        clientsArray.add(clientInfo);
    }

    clients["count"] = activeClients.size();
    clients["timestamp"] = millis();

    sendJsonResponse(request, clients);
}

void WebServerManager::handleWebSocketBroadcast(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!wsManager) {
        sendErrorResponse(request, "WebSocket manager not available", 503);
        return;
    }

    bool success = false;
    String message = getRequiredParam(request, "message", success);

    if (!success) {
        sendErrorResponse(request, "Missing message parameter", 400);
        return;
    }

    String level = getOptionalParam(request, "level", "info");

    wsManager->sendNotification(message, level);

    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Broadcast sent";
    response["timestamp"] = millis();

    sendJsonResponse(request, response);
}

// ==================== Configuration API ====================

void WebServerManager::handleGetConfig(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    DynamicJsonDocument config = dataManager->getSystemConfig();
    sendJsonResponse(request, config);
}

void WebServerManager::handleSetConfig(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    // For now, return not implemented (would need request body parsing)
    sendErrorResponse(request, "Config update not implemented yet", 501);
}

void WebServerManager::handleResetConfig(AsyncWebServerRequest *request) {
    logRequest(request);

    if (!dataManager) {
        sendErrorResponse(request, "Data manager not available", 503);
        return;
    }

    // Reset to default configuration
    DynamicJsonDocument defaultConfig(256);
    defaultConfig["system_name"] = "ESP32-S3 IR Control System";
    defaultConfig["version"] = "1.0.0";
    defaultConfig["ir_frequency"] = 38000;
    defaultConfig["learning_timeout"] = 30000;

    bool result = dataManager->setSystemConfig(defaultConfig);

    DynamicJsonDocument response(256);
    response["success"] = result;
    response["message"] = result ? "Configuration reset" : "Failed to reset configuration";
    response["timestamp"] = millis();

    sendJsonResponse(request, response, result ? 200 : 500);
}

// ==================== Additional Signal API ====================

// ==================== Additional Timer API ====================



// ==================== Additional Task API ====================

// ==================== Batch API ====================

void WebServerManager::handleBatchRequests(AsyncWebServerRequest *request) {
    logRequest(request);

    // For now, return not implemented (would need request body parsing)
    // This is a complex feature that requires parsing multiple requests
    sendErrorResponse(request, "Batch requests not implemented yet", 501);
}

// ==================== Helper Methods ====================

void WebServerManager::sendErrorResponse(AsyncWebServerRequest *request, const String& message, int code) {
    totalErrors++;

    DynamicJsonDocument error(256);
    error["error"] = true;
    error["message"] = message;
    error["code"] = code;
    error["timestamp"] = millis();

    String response;
    serializeJson(error, response);

    request->send(code, "application/json", response);

    logError("HTTP " + String(code) + ": " + message);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest *request, const String& message,
                                          const DynamicJsonDocument& data) {
    DynamicJsonDocument response(data.memoryUsage() + 128);

    response["success"] = true;
    response["message"] = message;
    response["timestamp"] = millis();

    if (!data.isNull()) {
        response["data"] = data;
    }

    sendJsonResponse(request, response);
}

void WebServerManager::sendJsonResponse(AsyncWebServerRequest *request, const DynamicJsonDocument& data, int code) {
    String response;
    serializeJson(data, response);

    request->send(code, "application/json", response);
    totalRequests++;
}

bool WebServerManager::validateRequest(AsyncWebServerRequest *request) {
    // Basic request validation
    if (!request) {
        return false;
    }

    // Check rate limiting (simplified)
    if (!checkRateLimit(request)) {
        return false;
    }

    return true;
}

bool WebServerManager::isAuthorized(AsyncWebServerRequest *request) {
    // For now, all requests are authorized
    // In a production system, you would check authentication tokens here
    return true;
}

void WebServerManager::handleUnauthorized(AsyncWebServerRequest *request) {
    sendErrorResponse(request, "Unauthorized access", 401);
}

void WebServerManager::logRequest(AsyncWebServerRequest *request) {
    String method = (request->method() == HTTP_GET) ? "GET" :
                   (request->method() == HTTP_POST) ? "POST" :
                   (request->method() == HTTP_PUT) ? "PUT" :
                   (request->method() == HTTP_DELETE) ? "DELETE" : "UNKNOWN";

    Serial.printf("🌐 %s %s from %s\n",
                 method.c_str(),
                 request->url().c_str(),
                 request->client()->remoteIP().toString().c_str());
}

void WebServerManager::logError(const String& error) {
    Serial.printf("❌ Web Server Error: %s\n", error.c_str());
}

String WebServerManager::getRequiredParam(AsyncWebServerRequest *request, const String& name, bool& success) {
    if (request->hasParam(name)) {
        success = true;
        return request->getParam(name)->value();
    }

    success = false;
    return "";
}

String WebServerManager::getOptionalParam(AsyncWebServerRequest *request, const String& name, const String& defaultValue) {
    if (request->hasParam(name)) {
        return request->getParam(name)->value();
    }

    return defaultValue;
}

DynamicJsonDocument WebServerManager::parseRequestBody(AsyncWebServerRequest *request, bool& success) {
    // This would need to be implemented with request body handling
    // For now, return empty document
    success = false;
    return DynamicJsonDocument(0);
}

String WebServerManager::generateDefaultHTML() {
    String html = "<!DOCTYPE html><html><head>";
    html += "<title>ESP32-S3 IR Control System</title>";
    html += "<meta charset='utf-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
    html += "<style>body{font-family:Arial,sans-serif;margin:40px;background:#f0f0f0;}";
    html += ".container{background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
    html += "h1{color:#333;text-align:center;}";
    html += ".status{background:#e8f5e8;padding:10px;border-radius:4px;margin:10px 0;}";
    html += ".api-list{background:#f8f8f8;padding:15px;border-radius:4px;margin:20px 0;}";
    html += ".endpoint{font-family:monospace;background:#fff;padding:5px;margin:5px 0;border-left:3px solid #007acc;}";
    html += "</style></head><body>";
    html += "<div class='container'>";
    html += "<h1>🚀 ESP32-S3 IR Control System</h1>";
    html += "<div class='status'>✅ System is running and ready!</div>";
    html += "<p><strong>Hardware:</strong> ESP32-S3 with IR Transmitter (GPIO21) and Receiver (GPIO14)</p>";
    html += "<p><strong>Features:</strong> IR Signal Learning, Transmission, Task Management, Real-time WebSocket</p>";
    html += "<div class='api-list'>";
    html += "<h3>📡 Available API Endpoints:</h3>";
    html += "<div class='endpoint'>GET /api/system/status - System status</div>";
    html += "<div class='endpoint'>GET /api/signals - Get all signals</div>";
    html += "<div class='endpoint'>POST /api/signals/send - Send IR signal</div>";
    html += "<div class='endpoint'>POST /api/signals/learn - Learn IR signal</div>";
    html += "<div class='endpoint'>GET /api/tasks - Get all tasks</div>";
    html += "<div class='endpoint'>GET /api/timers - Get all timers</div>";
    html += "<div class='endpoint'>WebSocket: ws://[IP]/ws - Real-time communication</div>";
    html += "</div>";
    html += "<p><strong>WebSocket:</strong> Connect to <code>ws://" + WiFi.localIP().toString() + "/ws</code> for real-time updates</p>";
    html += "<p><strong>Time:</strong> " + String(millis()) + " ms since boot</p>";
    html += "</div></body></html>";

    return html;
}

String WebServerManager::generateErrorHTML(const String& error) {
    String html = "<!DOCTYPE html><html><head><title>Error</title></head><body>";
    html += "<h1>Error</h1><p>" + error + "</p>";
    html += "<a href='/'>Back to Home</a></body></html>";
    return html;
}

String WebServerManager::generateStatusHTML() {
    String html = generateDefaultHTML();
    // Add more detailed status information
    return html;
}

bool WebServerManager::serveStaticFile(AsyncWebServerRequest *request, const String& path) {
    if (SPIFFS.exists(path)) {
        String contentType = getContentType(path);
        request->send(SPIFFS, path, contentType);
        return true;
    }
    return false;
}

String WebServerManager::getContentType(const String& filename) {
    if (filename.endsWith(".html")) return "text/html";
    if (filename.endsWith(".css")) return "text/css";
    if (filename.endsWith(".js")) return "application/javascript";
    if (filename.endsWith(".json")) return "application/json";
    if (filename.endsWith(".png")) return "image/png";
    if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) return "image/jpeg";
    if (filename.endsWith(".gif")) return "image/gif";
    if (filename.endsWith(".ico")) return "image/x-icon";
    return "text/plain";
}

bool WebServerManager::fileExists(const String& path) {
    return SPIFFS.exists(path);
}

bool WebServerManager::checkRateLimit(AsyncWebServerRequest *request) {
    // Simple rate limiting - allow all for now
    // In production, implement proper rate limiting per IP
    return true;
}

bool WebServerManager::validateCSRFToken(AsyncWebServerRequest *request) {
    // CSRF protection not implemented yet
    return true;
}

String WebServerManager::generateCSRFToken() {
    // Generate a simple token based on time and random
    return String(millis()) + "_" + String(random(10000, 99999));
}

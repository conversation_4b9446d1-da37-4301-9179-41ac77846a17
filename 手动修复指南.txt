ESP_Async_WebServer MD5.H 兼容性问题 - 手动修复指南

=== 问题确认 ===
根据编译错误日志，问题出现在：
文件: c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebAuthentication.cpp
错误: 第9行 #include "md5.h" 找不到文件

=== 解决方案 ===

步骤1: 定位文件
打开文件管理器，导航到：
c:\Users\<USER>\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebAuthentication.cpp

步骤2: 备份文件
复制 WebAuthentication.cpp 为 WebAuthentication.cpp.backup

步骤3: 编辑文件
用记事本或其他文本编辑器打开 WebAuthentication.cpp
找到第9行：
#include "md5.h"

将其替换为：
#include "mbedtls/md5.h"
#include "mbedtls/compat-2.x.h"

步骤4: 保存文件
保存修改后的文件

步骤5: 重新编译
返回Arduino IDE，重新编译项目

=== 验证修复 ===
如果修复成功，编译时应该不再出现 md5.h 相关错误。

=== 技术说明 ===
在ESP32 Arduino Core 2.0.17中，MD5功能从独立的md5.h迁移到了mbedtls库中。
这是一个已知的库兼容性问题，需要手动修复ESP_Async_WebServer库。

=== 如果修复失败 ===
1. 确认文件路径是否正确
2. 检查是否有权限修改文件
3. 尝试重新安装ESP_Async_WebServer库的兼容版本
4. 考虑使用其他Web服务器库作为替代方案

=== 相关链接 ===
GitHub Issue: https://github.com/me-no-dev/ESPAsyncWebServer/issues/1410
ESP32 Arduino Core: https://github.com/espressif/arduino-esp32

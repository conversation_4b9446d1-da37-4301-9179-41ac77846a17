/*
 * Web Server Manager Header - ESP32-S3 IR Control System
 * Fully compatible with ESPAsyncWebServer 3.7.8 API
 * Handles HTTP routes, API endpoints, and static file serving
 */

#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <functional>

// Forward declarations
class DataManager;
class IRController;
class TaskManager;
class WebSocketManager;

class WebServerManager {
public:
    // Constructor and Destructor
    WebServerManager();
    ~WebServerManager();
    
    // Core Methods
    bool initialize(AsyncWebServer* serverPtr, AsyncWebSocket* wsPtr = nullptr);
    void cleanup();
    
    // Module Reference Setters
    void setDataManager(DataManager* dm);
    void setIRController(IRController* ir);
    void setTaskManager(TaskManager* tm);
    void setWebSocketManager(WebSocketManager* wsm);
    
    // Status Query Methods
    bool isRunning() const;
    unsigned long getTotalRequests() const;
    unsigned long getTotalErrors() const;
    unsigned long getUptime() const;
    DynamicJsonDocument getServerStatistics() const;
    
    // Server Management
    void sendServerStats();
    void handleServerError(const String& error);
    
    // CORS and Security
    void enableCORS();
    void disableCORS();

private:
    // Server Components
    AsyncWebServer* server;
    AsyncWebSocket* webSocket;
    bool isInitialized;
    
    // Module References
    DataManager* dataManager;
    IRController* irController;
    TaskManager* taskManager;
    WebSocketManager* wsManager;
    
    // Statistics
    unsigned long totalRequests;
    unsigned long totalErrors;
    unsigned long startTime;
    
    // Route Setup
    void setupRoutes();
    void setupAPIRoutes();
    void setupStaticRoutes();
    
    // ==================== Request Handlers ====================
    
    // Root and Static
    void handleRoot(AsyncWebServerRequest *request);
    void handleNotFound(AsyncWebServerRequest *request);
    
    // System API (Frontend Matching)
    void handleSystemStatus(AsyncWebServerRequest *request);
    void handleSystemInfo(AsyncWebServerRequest *request);
    void handleSystemRestart(AsyncWebServerRequest *request);
    
    // IR API (Frontend Matching)
    void handleGetSignals(AsyncWebServerRequest *request);
    void handleSendSignal(AsyncWebServerRequest *request);
    void handleLearnSignal(AsyncWebServerRequest *request);
    void handleDeleteSignal(AsyncWebServerRequest *request);
    void handleBatchSendSignals(AsyncWebServerRequest *request);
    void handleStopLearning(AsyncWebServerRequest *request);
    void handleGetLearningStatus(AsyncWebServerRequest *request);
    
    // Task API (Frontend Matching)
    void handleGetTasks(AsyncWebServerRequest *request);
    void handleCreateTask(AsyncWebServerRequest *request);
    void handleControlTask(AsyncWebServerRequest *request);
    void handleDeleteTask(AsyncWebServerRequest *request);
    void handleGetTaskStatus(AsyncWebServerRequest *request);
    
    // Timer API (Frontend Matching)
    void handleGetTimers(AsyncWebServerRequest *request);
    void handleCreateTimer(AsyncWebServerRequest *request);
    void handleUpdateTimer(AsyncWebServerRequest *request);
    void handleDeleteTimer(AsyncWebServerRequest *request);
    void handleToggleTimer(AsyncWebServerRequest *request);
    
    // Data API (Frontend Matching)
    void handleExportData(AsyncWebServerRequest *request);
    void handleImportData(AsyncWebServerRequest *request);
    void handleBackupData(AsyncWebServerRequest *request);
    void handleRestoreData(AsyncWebServerRequest *request);
    void handleClearData(AsyncWebServerRequest *request);
    
    // WebSocket API (Frontend Matching)
    void handleWebSocketStats(AsyncWebServerRequest *request);
    void handleWebSocketClients(AsyncWebServerRequest *request);
    void handleWebSocketBroadcast(AsyncWebServerRequest *request);
    
    // Configuration API (Frontend Matching)
    void handleGetConfig(AsyncWebServerRequest *request);
    void handleSetConfig(AsyncWebServerRequest *request);
    void handleResetConfig(AsyncWebServerRequest *request);

    // Batch API (Frontend Required)
    void handleBatchRequests(AsyncWebServerRequest *request);

    // ==================== Helper Methods ====================
    
    // Response Helpers
    void sendErrorResponse(AsyncWebServerRequest *request, const String& message, int code = 400);
    void sendSuccessResponse(AsyncWebServerRequest *request, const String& message, const DynamicJsonDocument& data = DynamicJsonDocument(0));
    void sendJsonResponse(AsyncWebServerRequest *request, const DynamicJsonDocument& data, int code = 200);
    
    // Request Processing
    bool validateRequest(AsyncWebServerRequest *request);
    bool isAuthorized(AsyncWebServerRequest *request);
    void handleUnauthorized(AsyncWebServerRequest *request);
    void logRequest(AsyncWebServerRequest *request);
    void logError(const String& error);
    
    // Parameter Extraction
    String getRequiredParam(AsyncWebServerRequest *request, const String& name, bool& success);
    String getOptionalParam(AsyncWebServerRequest *request, const String& name, const String& defaultValue = "");
    DynamicJsonDocument parseRequestBody(AsyncWebServerRequest *request, bool& success);
    
    // Content Generation
    String generateDefaultHTML();
    String generateErrorHTML(const String& error);
    String generateStatusHTML();
    
    // File Handling
    bool serveStaticFile(AsyncWebServerRequest *request, const String& path);
    String getContentType(const String& filename);
    bool fileExists(const String& path);
    
    // Security
    bool checkRateLimit(AsyncWebServerRequest *request);
    bool validateCSRFToken(AsyncWebServerRequest *request);
    String generateCSRFToken();
    
    // Constants
    static const size_t MAX_REQUEST_SIZE = 8192;
    static const unsigned long RATE_LIMIT_WINDOW = 60000; // 1 minute
    static const int MAX_REQUESTS_PER_WINDOW = 100;
};

#endif // WEB_SERVER_MANAGER_H

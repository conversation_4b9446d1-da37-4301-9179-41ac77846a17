/**
 * DOM更新管理器 - 批处理DOM操作以提升性能
 */
class DOMUpdateManager {
  constructor() {
    this.pendingUpdates = new Map();
    this.updateFrame = null;
    this.highPriorityUpdates = new Set();
    this.domDepthCache = new Map();
    this.performance = {
      totalUpdates: 0,
      batchedUpdates: 0,
      savedOperations: 0
    };
  }

  /**
   * 调度DOM更新
   * @param {string} elementId - 元素ID
   * @param {Function} updateFn - 更新函数
   * @param {string} priority - 优先级 ('high' | 'normal')
   */
  scheduleUpdate(elementId, updateFn, priority = 'normal') {
    this.performance.totalUpdates++;

    // 高优先级立即执行
    if (priority === 'high') {
      try {
        updateFn();
      } catch (error) {
        console.error(`DOM更新错误 [${elementId}]:`, error);
      }
      return;
    }

    // 普通优先级批处理
    if (this.pendingUpdates.has(elementId)) {
      this.performance.savedOperations++;
    }
    
    this.pendingUpdates.set(elementId, updateFn);

    if (!this.updateFrame) {
      this.updateFrame = requestAnimationFrame(() => {
        this.flushUpdates();
        this.updateFrame = null;
      });
    }
  }

  /**
   * 刷新所有待处理的更新
   */
  flushUpdates() {
    if (this.pendingUpdates.size === 0) return;

    const startTime = performance.now();
    
    // 按DOM层级排序，减少重排
    const sortedUpdates = Array.from(this.pendingUpdates.entries())
      .sort(([a], [b]) => this.getDOMDepth(a) - this.getDOMDepth(b));

    // 批量执行更新
    for (const [elementId, updateFn] of sortedUpdates) {
      try {
        updateFn();
      } catch (error) {
        console.error(`批量DOM更新错误 [${elementId}]:`, error);
      }
    }

    this.pendingUpdates.clear();
    this.performance.batchedUpdates++;

    const duration = performance.now() - startTime;
    if (duration > 16) {
      console.warn(`DOM批量更新耗时过长: ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * 获取DOM元素深度（用于排序）
   */
  getDOMDepth(elementId) {
    if (this.domDepthCache.has(elementId)) {
      return this.domDepthCache.get(elementId);
    }

    const element = document.getElementById(elementId);
    if (!element) {
      this.domDepthCache.set(elementId, 0);
      return 0;
    }

    let depth = 0;
    let current = element;
    while (current.parentElement) {
      depth++;
      current = current.parentElement;
    }

    this.domDepthCache.set(elementId, depth);
    return depth;
  }

  /**
   * 批量更新文本内容
   */
  batchTextUpdate(updates) {
    const fragment = document.createDocumentFragment();
    const elementsToUpdate = [];

    for (const { elementId, text } of updates) {
      const element = document.getElementById(elementId);
      if (element) {
        elementsToUpdate.push({ element, text });
      }
    }

    // 批量更新
    for (const { element, text } of elementsToUpdate) {
      element.textContent = text;
    }
  }

  /**
   * 批量更新样式
   */
  batchStyleUpdate(updates) {
    for (const { elementId, styles } of updates) {
      const element = document.getElementById(elementId);
      if (element) {
        Object.assign(element.style, styles);
      }
    }
  }

  /**
   * 批量更新类名
   */
  batchClassUpdate(updates) {
    for (const { elementId, add = [], remove = [] } of updates) {
      const element = document.getElementById(elementId);
      if (element) {
        element.classList.remove(...remove);
        element.classList.add(...add);
      }
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.domDepthCache.clear();
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performance,
      pendingUpdates: this.pendingUpdates.size,
      cacheSize: this.domDepthCache.size,
      efficiency: this.performance.totalUpdates > 0 
        ? (this.performance.savedOperations / this.performance.totalUpdates * 100).toFixed(1) + '%'
        : '0%'
    };
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.updateFrame) {
      cancelAnimationFrame(this.updateFrame);
      this.updateFrame = null;
    }
    this.pendingUpdates.clear();
    this.clearCache();
  }
}

// 创建全局实例
window.DOMUpdateManager = new DOMUpdateManager();

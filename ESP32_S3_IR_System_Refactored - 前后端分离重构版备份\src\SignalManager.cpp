/**
 * 信号管理器 - 实现文件
 * 负责信号的CRUD操作、数据验证、存储管理
 */

#include "SignalManager.h"
#include "FileSystemManager.h"
#include <SPIFFS.h>

// Signal类方法实现

Signal Signal::fromJson(const JsonObject& json) {
    Signal signal;
    
    signal.id = json["id"] | "";
    signal.name = json["name"] | "";
    signal.type = json["type"] | "other";
    signal.description = json["description"] | "";
    signal.signalCode = json["signalCode"] | "";
    signal.protocol = json["protocol"] | "NEC";
    signal.frequency = json["frequency"] | "38000";
    signal.rawData = json["rawData"] | "";
    signal.isLearned = json["isLearned"] | false;
    signal.created = json["created"] | millis();
    signal.lastSent = json["lastSent"] | 0;
    signal.sentCount = json["sentCount"] | 0;
    signal.parseSuccess = json["parseSuccess"] | false;
    
    return signal;
}

JsonObject Signal::toJson(JsonDocument& doc) const {
    JsonObject obj = doc.createNestedObject();
    
    obj["id"] = id;
    obj["name"] = name;
    obj["type"] = type;
    obj["description"] = description;
    obj["signalCode"] = signalCode;
    obj["protocol"] = protocol;
    obj["frequency"] = frequency;
    obj["rawData"] = rawData;
    obj["isLearned"] = isLearned;
    obj["created"] = created;
    obj["lastSent"] = lastSent;
    obj["sentCount"] = sentCount;
    obj["parseSuccess"] = parseSuccess;
    
    return obj;
}

bool Signal::isValid() const {
    // 基本字段验证
    if (id.isEmpty() || name.isEmpty()) {
        return false;
    }
    
    // ID格式验证
    if (!id.startsWith("signal_") || id.length() != 15) {
        return false;
    }
    
    // 名称长度验证
    if (name.length() > 50) {
        return false;
    }
    
    // 类型验证
    if (type != "tv" && type != "ac" && type != "fan" && 
        type != "light" && type != "other") {
        return false;
    }
    
    // 协议验证
    if (protocol != "NEC" && protocol != "RC5" && protocol != "SONY" && 
        protocol != "RAW" && protocol != "UNKNOWN") {
        return false;
    }
    
    return true;
}

String Signal::generateId() {
    return "signal_" + String(random(10000000, 99999999));
}

// APIResponse类方法实现

APIResponse APIResponse::createSuccess(const JsonDocument& data) {
    APIResponse response;
    response.success = true;
    response.data = data;
    response.timestamp = millis();
    return response;
}

APIResponse APIResponse::createError(const String& error, int code) {
    APIResponse response;
    response.success = false;
    response.error = error;
    response.timestamp = millis();
    
    // 在data中添加错误代码
    JsonObject errorObj = response.data.createNestedObject();
    errorObj["code"] = code;
    errorObj["message"] = error;
    
    return response;
}

String APIResponse::toJsonString() const {
    JsonDocument doc;
    JsonObject root = doc.to<JsonObject>();
    
    root["success"] = success;
    root["timestamp"] = timestamp;
    
    if (success) {
        root["data"] = data;
    } else {
        root["error"] = error;
        if (!data.isNull()) {
            root["details"] = data;
        }
    }
    
    String result;
    serializeJson(doc, result);
    return result;
}

// SignalManager类方法实现

SignalManager::SignalManager() {
    signals.reserve(MAX_SIGNALS);
}

SignalManager::~SignalManager() {
    saveToFile();
}

bool SignalManager::init() {
    Serial.println("📡 初始化信号管理器...");
    
    // 清空现有数据
    signals.clear();
    signalIndex.clear();
    
    // 从文件加载信号数据
    if (!loadFromFile()) {
        Serial.println("⚠️ 信号数据加载失败，使用空列表");
    }
    
    Serial.printf("✅ 信号管理器初始化完成，共 %d 个信号\n", signals.size());
    return true;
}

APIResponse SignalManager::addSignal(const Signal& signal) {
    // 验证信号数据
    if (!validateSignal(signal)) {
        return APIResponse::createError("信号数据验证失败", INVALID_PARAMETER);
    }
    
    // 检查信号数量限制
    if (signals.size() >= MAX_SIGNALS) {
        return APIResponse::createError("信号数量已达上限", MAX_SIGNALS_REACHED);
    }
    
    // 检查ID是否已存在
    if (findSignalIndex(signal.id) >= 0) {
        return APIResponse::createError("信号ID已存在", DUPLICATE_SIGNAL);
    }
    
    // 添加信号
    signals.push_back(signal);
    updateSignalIndex();
    
    // 保存到文件
    saveToFile();
    
    // 创建响应数据
    JsonDocument responseData;
    JsonObject signalObj = signal.toJson(responseData);
    
    Serial.printf("✅ 添加信号成功: %s (%s)\n", signal.name.c_str(), signal.id.c_str());
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::getSignal(const String& id) {
    int index = findSignalIndex(id);
    if (index < 0) {
        return APIResponse::createError("信号不存在", SIGNAL_NOT_FOUND);
    }
    
    JsonDocument responseData;
    JsonObject signalObj = signals[index].toJson(responseData);
    
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::getAllSignals() {
    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");
    
    for (const auto& signal : signals) {
        JsonObject signalObj = signalsArray.createNestedObject();
        signalObj["id"] = signal.id;
        signalObj["name"] = signal.name;
        signalObj["type"] = signal.type;
        signalObj["description"] = signal.description;
        signalObj["signalCode"] = signal.signalCode;
        signalObj["protocol"] = signal.protocol;
        signalObj["frequency"] = signal.frequency;
        signalObj["rawData"] = signal.rawData;
        signalObj["isLearned"] = signal.isLearned;
        signalObj["created"] = signal.created;
        signalObj["lastSent"] = signal.lastSent;
        signalObj["sentCount"] = signal.sentCount;
        signalObj["parseSuccess"] = signal.parseSuccess;
    }
    
    // 添加统计信息
    JsonObject stats = responseData.createNestedObject("stats");
    stats["totalSignals"] = signals.size();
    stats["totalSent"] = getTotalSentCount();
    
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::deleteSignal(const String& id) {
    int index = findSignalIndex(id);
    if (index < 0) {
        return APIResponse::createError("信号不存在", SIGNAL_NOT_FOUND);
    }
    
    String signalName = signals[index].name;
    
    // 删除信号
    signals.erase(signals.begin() + index);
    updateSignalIndex();
    
    // 保存到文件
    saveToFile();
    
    // 创建响应数据
    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["deletedId"] = id;
    result["message"] = "信号删除成功";
    
    Serial.printf("✅ 删除信号成功: %s (%s)\n", signalName.c_str(), id.c_str());
    return APIResponse::createSuccess(responseData);
}

bool SignalManager::validateSignal(const Signal& signal) const {
    // 使用Signal类的验证方法
    if (!signal.isValid()) {
        return false;
    }
    
    // 额外的业务逻辑验证
    if (!validateSignalCode(signal.signalCode)) {
        return false;
    }
    
    if (!validateProtocol(signal.protocol)) {
        return false;
    }
    
    if (!validateFrequency(signal.frequency)) {
        return false;
    }
    
    return true;
}

bool SignalManager::validateSignalCode(const String& code) const {
    if (code.isEmpty()) {
        return false;
    }
    
    // 十六进制格式验证
    if (code.startsWith("0x") || code.startsWith("0X")) {
        String hexPart = code.substring(2);
        for (char c : hexPart) {
            if (!isHexadecimalDigit(c)) {
                return false;
            }
        }
        return hexPart.length() > 0 && hexPart.length() <= 8;
    }
    
    // RAW数据格式验证
    if (code == "RAW_DATA") {
        return true;
    }
    
    return false;
}

bool SignalManager::validateProtocol(const String& protocol) const {
    return (protocol == "NEC" || protocol == "RC5" || protocol == "SONY" || 
            protocol == "RAW" || protocol == "UNKNOWN");
}

bool SignalManager::validateFrequency(const String& frequency) const {
    int freq = frequency.toInt();
    return freq >= 30000 && freq <= 60000; // 常见红外频率范围
}

void SignalManager::updateSignalIndex() {
    signalIndex.clear();
    for (size_t i = 0; i < signals.size(); i++) {
        signalIndex[signals[i].id] = i;
    }
}

int SignalManager::findSignalIndex(const String& id) const {
    auto it = signalIndex.find(id);
    return (it != signalIndex.end()) ? it->second : -1;
}

unsigned long SignalManager::getTotalSentCount() const {
    unsigned long total = 0;
    for (const auto& signal : signals) {
        total += signal.sentCount;
    }
    return total;
}

APIResponse SignalManager::updateSignal(const String& id, const Signal& updatedSignal) {
    int index = findSignalIndex(id);
    if (index < 0) {
        return APIResponse::createError("信号不存在", SIGNAL_NOT_FOUND);
    }

    // 验证更新的信号数据
    Signal signalToUpdate = updatedSignal;
    signalToUpdate.id = id; // 保持原ID

    if (!validateSignal(signalToUpdate)) {
        return APIResponse::createError("信号数据验证失败", INVALID_PARAMETER);
    }

    // 保留原有的统计数据
    signalToUpdate.created = signals[index].created;
    signalToUpdate.lastSent = signals[index].lastSent;
    signalToUpdate.sentCount = signals[index].sentCount;

    // 更新信号
    signals[index] = signalToUpdate;

    // 保存到文件
    saveToFile();

    // 创建响应数据
    JsonDocument responseData;
    JsonObject signalObj = signalToUpdate.toJson(responseData);

    Serial.printf("✅ 更新信号成功: %s (%s)\n", signalToUpdate.name.c_str(), id.c_str());
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::batchDeleteSignals(const std::vector<String>& ids) {
    int successCount = 0;
    int errorCount = 0;
    std::vector<String> deletedIds;
    std::vector<String> errorIds;

    for (const String& id : ids) {
        int index = findSignalIndex(id);
        if (index >= 0) {
            signals.erase(signals.begin() + index);
            updateSignalIndex(); // 重新构建索引
            deletedIds.push_back(id);
            successCount++;
        } else {
            errorIds.push_back(id);
            errorCount++;
        }
    }

    // 保存到文件
    if (successCount > 0) {
        saveToFile();
    }

    // 创建响应数据
    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["successCount"] = successCount;
    result["errorCount"] = errorCount;

    JsonArray deletedArray = result.createNestedArray("deletedIds");
    for (const String& id : deletedIds) {
        deletedArray.add(id);
    }

    JsonArray errorArray = result.createNestedArray("errorIds");
    for (const String& id : errorIds) {
        errorArray.add(id);
    }

    Serial.printf("✅ 批量删除完成: 成功 %d 个，失败 %d 个\n", successCount, errorCount);
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::saveLearnedSignal(const String& name, const String& type,
                                           const String& description, const JsonObject& signalData) {
    // 创建新信号
    Signal newSignal;
    newSignal.id = Signal::generateId();
    newSignal.name = name;
    newSignal.type = type;
    newSignal.description = description;
    newSignal.isLearned = true;
    newSignal.created = millis();
    newSignal.lastSent = 0;
    newSignal.sentCount = 0;

    // 从学习数据中提取信号信息
    newSignal.signalCode = signalData["signalCode"] | "RAW_DATA";
    newSignal.protocol = signalData["protocol"] | "NEC";
    newSignal.frequency = signalData["frequency"] | "38000";
    newSignal.rawData = signalData["rawData"] | "";
    newSignal.parseSuccess = signalData["parseSuccess"] | false;

    // 添加信号
    return addSignal(newSignal);
}

void SignalManager::updateSentStatistics(const String& id) {
    int index = findSignalIndex(id);
    if (index >= 0) {
        signals[index].sentCount++;
        signals[index].lastSent = millis();

        // 异步保存到文件（避免阻塞）
        saveToFile();

        Serial.printf("📊 更新发射统计: %s (总计: %d 次)\n",
                     signals[index].name.c_str(), signals[index].sentCount);
    }
}

bool SignalManager::loadFromFile() {
    const char* filename = "/signals.json";

    if (!SPIFFS.exists(filename)) {
        Serial.println("📁 信号文件不存在，创建新文件");
        return saveToFile(); // 创建空文件
    }

    File file = SPIFFS.open(filename, "r");
    if (!file) {
        Serial.println("❌ 无法打开信号文件");
        return false;
    }

    // 读取文件内容
    String content = file.readString();
    file.close();

    if (content.isEmpty()) {
        Serial.println("📁 信号文件为空");
        return true;
    }

    // 解析JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, content);

    if (error) {
        Serial.printf("❌ JSON解析失败: %s\n", error.c_str());
        return false;
    }

    // 清空现有信号
    signals.clear();
    signalIndex.clear();

    // 加载信号数据
    JsonArray signalsArray = doc["signals"];
    for (JsonObject signalObj : signalsArray) {
        Signal signal = Signal::fromJson(signalObj);
        if (signal.isValid()) {
            signals.push_back(signal);
        } else {
            Serial.printf("⚠️ 跳过无效信号: %s\n", signal.name.c_str());
        }
    }

    // 重建索引
    updateSignalIndex();

    Serial.printf("✅ 从文件加载 %d 个信号\n", signals.size());
    return true;
}

bool SignalManager::saveToFile() const {
    const char* filename = "/signals.json";

    // 创建JSON文档
    JsonDocument doc;
    JsonObject root = doc.to<JsonObject>();

    // 添加元数据
    root["version"] = "1.0";
    root["timestamp"] = millis();
    root["count"] = signals.size();

    // 添加信号数组
    JsonArray signalsArray = root.createNestedArray("signals");
    for (const auto& signal : signals) {
        JsonObject signalObj = signalsArray.createNestedObject();
        signalObj["id"] = signal.id;
        signalObj["name"] = signal.name;
        signalObj["type"] = signal.type;
        signalObj["description"] = signal.description;
        signalObj["signalCode"] = signal.signalCode;
        signalObj["protocol"] = signal.protocol;
        signalObj["frequency"] = signal.frequency;
        signalObj["rawData"] = signal.rawData;
        signalObj["isLearned"] = signal.isLearned;
        signalObj["created"] = signal.created;
        signalObj["lastSent"] = signal.lastSent;
        signalObj["sentCount"] = signal.sentCount;
        signalObj["parseSuccess"] = signal.parseSuccess;
    }

    // 写入文件
    File file = SPIFFS.open(filename, "w");
    if (!file) {
        Serial.println("❌ 无法创建信号文件");
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    if (bytesWritten == 0) {
        Serial.println("❌ 信号文件写入失败");
        return false;
    }

    Serial.printf("✅ 保存 %d 个信号到文件 (%d bytes)\n", signals.size(), bytesWritten);
    return true;
}

APIResponse SignalManager::importSignals(const std::vector<Signal>& importSignals, bool overwrite) {
    int successCount = 0;
    int errorCount = 0;
    int duplicateCount = 0;
    std::vector<String> addedIds;
    std::vector<String> errorMessages;

    for (const Signal& signal : importSignals) {
        // 验证信号数据
        if (!validateSignal(signal)) {
            errorCount++;
            errorMessages.push_back("信号 " + signal.name + " 验证失败");
            continue;
        }

        // 检查是否已存在
        int existingIndex = findSignalIndex(signal.id);
        if (existingIndex >= 0) {
            if (overwrite) {
                // 覆盖现有信号
                signals[existingIndex] = signal;
                successCount++;
                addedIds.push_back(signal.id);
            } else {
                duplicateCount++;
                errorMessages.push_back("信号 " + signal.name + " 已存在");
            }
            continue;
        }

        // 检查信号数量限制
        if (signals.size() >= MAX_SIGNALS) {
            errorCount++;
            errorMessages.push_back("信号数量已达上限");
            break;
        }

        // 添加新信号
        signals.push_back(signal);
        addedIds.push_back(signal.id);
        successCount++;
    }

    // 重建索引
    updateSignalIndex();

    // 保存到文件
    if (successCount > 0) {
        saveToFile();
    }

    // 创建响应数据
    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["successCount"] = successCount;
    result["errorCount"] = errorCount;
    result["duplicateCount"] = duplicateCount;
    result["totalProcessed"] = importSignals.size();

    JsonArray addedArray = result.createNestedArray("addedIds");
    for (const String& id : addedIds) {
        addedArray.add(id);
    }

    JsonArray errorsArray = result.createNestedArray("errors");
    for (const String& error : errorMessages) {
        errorsArray.add(error);
    }

    Serial.printf("✅ 导入完成: 成功 %d 个，失败 %d 个，重复 %d 个\n",
                 successCount, errorCount, duplicateCount);

    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::parseImportFile(const String& content, const String& fileType) const {
    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");

    if (fileType == "json") {
        return parseJSONImport(content);
    } else if (fileType == "csv") {
        return parseCSVImport(content);
    } else if (fileType == "txt") {
        return parseTextImport(content, "other");
    } else {
        return APIResponse::createError("不支持的文件类型: " + fileType, INVALID_PARAMETER);
    }
}

APIResponse SignalManager::parseJSONImport(const String& content) const {
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, content);

    if (error) {
        return APIResponse::createError("JSON解析失败: " + String(error.c_str()), INVALID_PARAMETER);
    }

    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");
    int validCount = 0;
    int invalidCount = 0;

    // 处理不同的JSON格式
    if (doc.is<JsonArray>()) {
        // 直接是信号数组
        JsonArray inputArray = doc.as<JsonArray>();
        for (JsonObject signalObj : inputArray) {
            Signal signal = Signal::fromJson(signalObj);
            if (signal.isValid()) {
                JsonObject outputSignal = signalsArray.createNestedObject();
                signal.toJson(responseData); // 这里需要修正
                validCount++;
            } else {
                invalidCount++;
            }
        }
    } else if (doc.is<JsonObject>()) {
        JsonObject rootObj = doc.as<JsonObject>();

        // 检查是否有signals字段
        if (rootObj.containsKey("signals")) {
            JsonArray inputArray = rootObj["signals"];
            for (JsonObject signalObj : inputArray) {
                Signal signal = Signal::fromJson(signalObj);
                if (signal.isValid()) {
                    JsonObject outputSignal = signalsArray.createNestedObject();
                    // 复制信号数据到输出
                    outputSignal["id"] = signal.id.isEmpty() ? Signal::generateId() : signal.id;
                    outputSignal["name"] = signal.name;
                    outputSignal["type"] = signal.type;
                    outputSignal["description"] = signal.description;
                    outputSignal["signalCode"] = signal.signalCode;
                    outputSignal["protocol"] = signal.protocol;
                    outputSignal["frequency"] = signal.frequency;
                    outputSignal["rawData"] = signal.rawData;
                    outputSignal["isLearned"] = signal.isLearned;
                    outputSignal["created"] = signal.created;
                    outputSignal["parseSuccess"] = signal.parseSuccess;
                    validCount++;
                } else {
                    invalidCount++;
                }
            }
        } else {
            // 单个信号对象
            Signal signal = Signal::fromJson(rootObj);
            if (signal.isValid()) {
                JsonObject outputSignal = signalsArray.createNestedObject();
                outputSignal["id"] = signal.id.isEmpty() ? Signal::generateId() : signal.id;
                outputSignal["name"] = signal.name;
                outputSignal["type"] = signal.type;
                outputSignal["description"] = signal.description;
                outputSignal["signalCode"] = signal.signalCode;
                outputSignal["protocol"] = signal.protocol;
                outputSignal["frequency"] = signal.frequency;
                outputSignal["rawData"] = signal.rawData;
                outputSignal["isLearned"] = signal.isLearned;
                outputSignal["created"] = signal.created;
                outputSignal["parseSuccess"] = signal.parseSuccess;
                validCount++;
            } else {
                invalidCount++;
            }
        }
    }

    // 添加解析统计
    JsonObject stats = responseData.createNestedObject("stats");
    stats["validCount"] = validCount;
    stats["invalidCount"] = invalidCount;
    stats["totalCount"] = validCount + invalidCount;

    Serial.printf("✅ JSON解析完成: 有效 %d 个，无效 %d 个\n", validCount, invalidCount);
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::parseCSVImport(const String& content) const {
    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");

    // 简单的CSV解析
    int validCount = 0;
    int invalidCount = 0;

    // 按行分割
    int lineStart = 0;
    int lineEnd = content.indexOf('\n');
    bool isFirstLine = true;

    while (lineEnd != -1 || lineStart < content.length()) {
        if (lineEnd == -1) lineEnd = content.length();

        String line = content.substring(lineStart, lineEnd);
        line.trim();

        if (!line.isEmpty() && !isFirstLine) { // 跳过标题行
            // 解析CSV行: name,type,signalCode,protocol,frequency,description
            std::vector<String> fields;
            int fieldStart = 0;
            int fieldEnd = line.indexOf(',');

            while (fieldEnd != -1 || fieldStart < line.length()) {
                if (fieldEnd == -1) fieldEnd = line.length();
                String field = line.substring(fieldStart, fieldEnd);
                field.trim();
                fields.push_back(field);
                fieldStart = fieldEnd + 1;
                fieldEnd = line.indexOf(',', fieldStart);
            }

            if (fields.size() >= 4) { // 至少需要name,type,signalCode,protocol
                JsonObject signalObj = signalsArray.createNestedObject();
                signalObj["id"] = Signal::generateId();
                signalObj["name"] = fields[0];
                signalObj["type"] = fields[1];
                signalObj["signalCode"] = fields[2];
                signalObj["protocol"] = fields[3];
                signalObj["frequency"] = fields.size() > 4 ? fields[4] : "38000";
                signalObj["description"] = fields.size() > 5 ? fields[5] : "";
                signalObj["rawData"] = "";
                signalObj["isLearned"] = false;
                signalObj["created"] = millis();
                signalObj["parseSuccess"] = true;
                validCount++;
            } else {
                invalidCount++;
            }
        }

        isFirstLine = false;
        lineStart = lineEnd + 1;
        lineEnd = content.indexOf('\n', lineStart);
    }

    // 添加解析统计
    JsonObject stats = responseData.createNestedObject("stats");
    stats["validCount"] = validCount;
    stats["invalidCount"] = invalidCount;
    stats["totalCount"] = validCount + invalidCount;

    Serial.printf("✅ CSV解析完成: 有效 %d 个，无效 %d 个\n", validCount, invalidCount);
    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::parseTextImport(const String& content, const String& defaultType) const {
    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");

    int validCount = 0;
    int invalidCount = 0;

    // 按行分割文本
    int lineStart = 0;
    int lineEnd = content.indexOf('\n');
    int signalIndex = 1;

    while (lineEnd != -1 || lineStart < content.length()) {
        if (lineEnd == -1) lineEnd = content.length();

        String line = content.substring(lineStart, lineEnd);
        line.trim();

        if (!line.isEmpty()) {
            // 尝试解析为十六进制信号码
            if (line.startsWith("0x") || line.startsWith("0X")) {
                JsonObject signalObj = signalsArray.createNestedObject();
                signalObj["id"] = Signal::generateId();
                signalObj["name"] = "导入信号_" + String(signalIndex++);
                signalObj["type"] = defaultType;
                signalObj["signalCode"] = line;
                signalObj["protocol"] = "NEC"; // 默认协议
                signalObj["frequency"] = "38000";
                signalObj["description"] = "从文本导入";
                signalObj["rawData"] = "";
                signalObj["isLearned"] = false;
                signalObj["created"] = millis();
                signalObj["parseSuccess"] = true;
                validCount++;
            } else {
                // 尝试解析为原始数据
                if (line.indexOf(',') != -1) { // 包含逗号，可能是原始数据
                    JsonObject signalObj = signalsArray.createNestedObject();
                    signalObj["id"] = Signal::generateId();
                    signalObj["name"] = "原始信号_" + String(signalIndex++);
                    signalObj["type"] = defaultType;
                    signalObj["signalCode"] = "RAW_DATA";
                    signalObj["protocol"] = "RAW";
                    signalObj["frequency"] = "38000";
                    signalObj["description"] = "从文本导入的原始数据";
                    signalObj["rawData"] = line;
                    signalObj["isLearned"] = false;
                    signalObj["created"] = millis();
                    signalObj["parseSuccess"] = false;
                    validCount++;
                } else {
                    invalidCount++;
                }
            }
        }

        lineStart = lineEnd + 1;
        lineEnd = content.indexOf('\n', lineStart);
    }

    // 添加解析统计
    JsonObject stats = responseData.createNestedObject("stats");
    stats["validCount"] = validCount;
    stats["invalidCount"] = invalidCount;
    stats["totalCount"] = validCount + invalidCount;

    Serial.printf("✅ 文本解析完成: 有效 %d 个，无效 %d 个\n", validCount, invalidCount);
    return APIResponse::createSuccess(responseData);
}

int SignalManager::getSignalCountByType(const String& type) const {
    int count = 0;
    for (const auto& signal : signals) {
        if (signal.type == type) {
            count++;
        }
    }
    return count;
}

std::vector<Signal> SignalManager::searchSignals(const String& keyword) const {
    std::vector<Signal> results;
    String lowerKeyword = keyword;
    lowerKeyword.toLowerCase();

    for (const auto& signal : signals) {
        String lowerName = signal.name;
        lowerName.toLowerCase();
        String lowerDesc = signal.description;
        lowerDesc.toLowerCase();

        if (lowerName.indexOf(lowerKeyword) != -1 ||
            lowerDesc.indexOf(lowerKeyword) != -1 ||
            signal.type.indexOf(lowerKeyword) != -1) {
            results.push_back(signal);
        }
    }

    return results;
}

std::vector<Signal> SignalManager::filterSignalsByType(const String& type) const {
    std::vector<Signal> results;
    for (const auto& signal : signals) {
        if (signal.type == type) {
            results.push_back(signal);
        }
    }
    return results;
}

std::vector<Signal> SignalManager::sortSignals(const String& sortBy, bool ascending) const {
    std::vector<Signal> sorted = signals;

    if (sortBy == "name") {
        std::sort(sorted.begin(), sorted.end(), [ascending](const Signal& a, const Signal& b) {
            return ascending ? (a.name < b.name) : (a.name > b.name);
        });
    } else if (sortBy == "type") {
        std::sort(sorted.begin(), sorted.end(), [ascending](const Signal& a, const Signal& b) {
            return ascending ? (a.type < b.type) : (a.type > b.type);
        });
    } else if (sortBy == "created") {
        std::sort(sorted.begin(), sorted.end(), [ascending](const Signal& a, const Signal& b) {
            return ascending ? (a.created < b.created) : (a.created > b.created);
        });
    } else if (sortBy == "sentCount") {
        std::sort(sorted.begin(), sorted.end(), [ascending](const Signal& a, const Signal& b) {
            return ascending ? (a.sentCount < b.sentCount) : (a.sentCount > b.sentCount);
        });
    }

    return sorted;
}

APIResponse SignalManager::exportSignals(const std::vector<String>& ids) const {
    JsonDocument responseData;
    JsonArray signalsArray = responseData.createNestedArray("signals");

    if (ids.empty()) {
        // 导出所有信号
        for (const auto& signal : signals) {
            JsonObject signalObj = signalsArray.createNestedObject();
            signal.toJson(responseData); // 这里需要修正
        }
    } else {
        // 导出指定信号
        for (const String& id : ids) {
            int index = findSignalIndex(id);
            if (index >= 0) {
                JsonObject signalObj = signalsArray.createNestedObject();
                signals[index].toJson(responseData); // 这里需要修正
            }
        }
    }

    // 添加导出统计
    JsonObject stats = responseData.createNestedObject("stats");
    stats["exportedCount"] = signalsArray.size();
    stats["totalCount"] = signals.size();
    stats["exportTime"] = millis();

    return APIResponse::createSuccess(responseData);
}

APIResponse SignalManager::validateImportData(const JsonArray& data) const {
    int validCount = 0;
    int invalidCount = 0;
    std::vector<String> errors;

    for (JsonObject signalObj : data) {
        Signal signal = Signal::fromJson(signalObj);
        if (signal.isValid()) {
            validCount++;
        } else {
            invalidCount++;
            String name = signalObj["name"] | "未知信号";
            errors.push_back("信号 '" + name + "' 验证失败");
        }
    }

    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["validCount"] = validCount;
    result["invalidCount"] = invalidCount;
    result["totalCount"] = validCount + invalidCount;

    JsonArray errorsArray = result.createNestedArray("errors");
    for (const String& error : errors) {
        errorsArray.add(error);
    }

    return APIResponse::createSuccess(responseData);
}

void SignalManager::optimizeMemory() {
    // 压缩信号向量
    signals.shrink_to_fit();

    // 重建索引
    updateSignalIndex();

    // 清理无效信号
    auto it = std::remove_if(signals.begin(), signals.end(),
                            [](const Signal& signal) { return !signal.isValid(); });
    if (it != signals.end()) {
        signals.erase(it, signals.end());
        updateSignalIndex();
    }

    Serial.printf("🔧 内存优化完成，当前信号数: %d\n", signals.size());
}

size_t SignalManager::getMemoryUsage() const {
    size_t usage = sizeof(SignalManager);
    usage += signals.capacity() * sizeof(Signal);
    usage += signalIndex.size() * (sizeof(String) + sizeof(int));

    // 估算字符串内存使用
    for (const auto& signal : signals) {
        usage += signal.name.length();
        usage += signal.description.length();
        usage += signal.signalCode.length();
        usage += signal.protocol.length();
        usage += signal.rawData.length();
    }

    return usage;
}

void SignalManager::printSignalList() const {
    Serial.println("=== 信号列表 ===");
    Serial.printf("总计: %d 个信号\n", signals.size());

    for (size_t i = 0; i < signals.size(); i++) {
        const Signal& signal = signals[i];
        Serial.printf("%d. %s (%s) - %s [%s]\n",
                     i + 1,
                     signal.name.c_str(),
                     signal.id.c_str(),
                     signal.type.c_str(),
                     signal.protocol.c_str());
    }
    Serial.println("===============");
}

void SignalManager::printMemoryInfo() const {
    Serial.println("=== 内存信息 ===");
    Serial.printf("信号数量: %d\n", signals.size());
    Serial.printf("向量容量: %d\n", signals.capacity());
    Serial.printf("索引大小: %d\n", signalIndex.size());
    Serial.printf("内存使用: %d bytes\n", getMemoryUsage());
    Serial.printf("平均每信号: %d bytes\n", signals.empty() ? 0 : getMemoryUsage() / signals.size());
    Serial.println("===============");
}

JsonObject SignalManager::getSystemStats() const {
    JsonDocument doc;
    JsonObject stats = doc.to<JsonObject>();

    stats["signalCount"] = signals.size();
    stats["signalCapacity"] = signals.capacity();
    stats["indexSize"] = signalIndex.size();
    stats["memoryUsage"] = getMemoryUsage();
    stats["totalSentCount"] = getTotalSentCount();

    // 按类型统计
    JsonObject typeStats = stats.createNestedObject("typeStats");
    std::map<String, int> typeCounts;
    for (const auto& signal : signals) {
        typeCounts[signal.type]++;
    }
    for (const auto& pair : typeCounts) {
        typeStats[pair.first] = pair.second;
    }

    // 按协议统计
    JsonObject protocolStats = stats.createNestedObject("protocolStats");
    std::map<String, int> protocolCounts;
    for (const auto& signal : signals) {
        protocolCounts[signal.protocol]++;
    }
    for (const auto& pair : protocolCounts) {
        protocolStats[pair.first] = pair.second;
    }

    return stats;
}

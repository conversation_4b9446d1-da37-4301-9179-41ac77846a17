/**
 * R2智能红外控制系统 - 前端配置管理
 * 纯前端版本：只管理ESP32连接配置
 */

class HardwareConfigManager {
  constructor() {
    this.config = {
      // ESP32设备配置
      esp32: {
        host: "*************",  // 需要根据实际IP调整
        port: 80,
        wsPort: 81,
        timeout: 30000
      },

      // 前端调试配置
      debug: {
        logAPIRequests: true,
        showConnectionStatus: true
      }
    };

    // 从localStorage加载配置
    this.loadConfig();

    // 初始化事件监听
    this.initEventListeners();
  }
  
  /**
   * 加载配置 - 纯前端版本只加载连接配置
   */
  loadConfig() {
    try {
      const savedConfig = localStorage.getItem('r2_esp32_config');
      if (savedConfig) {
        const parsed = JSON.parse(savedConfig);
        this.config = { ...this.config, ...parsed };
        console.log('HardwareConfig: ESP32配置已加载', this.config);
      }
    } catch (error) {
      console.warn('HardwareConfig: 配置加载失败，使用默认配置', error);
    }
  }

  /**
   * 保存配置 - 纯前端版本只保存连接配置
   */
  saveConfig() {
    try {
      localStorage.setItem('r2_esp32_config', JSON.stringify(this.config));
      console.log('HardwareConfig: ESP32配置已保存');
    } catch (error) {
      console.error('HardwareConfig: 配置保存失败', error);
    }
  }

  /**
   * 初始化事件监听 - 纯前端版本简化事件
   */
  initEventListeners() {
    // 监听配置更新请求
    if (window.R1Core && window.R1Core.eventBus) {
      window.R1Core.eventBus.on('esp32.config.update', (data) => {
        this.updateConfig(data);
      });

      // 监听配置查询请求
      window.R1Core.eventBus.on('esp32.config.get', (callback) => {
        if (typeof callback === 'function') {
          callback(this.config);
        }
      });
    }
  }
  
  /**
   * 更新配置 - 纯前端版本只更新连接配置
   */
  updateConfig(updates) {
    try {
      this.config = { ...this.config, ...updates };
      this.saveConfig();

      // 发布配置更新事件
      if (window.R1Core && window.R1Core.eventBus) {
        window.R1Core.eventBus.emit('esp32.config.updated', this.config);
      }

      console.log('HardwareConfig: ESP32配置已更新', updates);
    } catch (error) {
      console.error('HardwareConfig: 配置更新失败', error);
    }
  }

  /**
   * 获取ESP32配置
   */
  getESP32Config() {
    return this.config.esp32;
  }

  /**
   * 更新ESP32 IP地址
   */
  updateESP32Host(host) {
    this.updateConfig({
      esp32: { ...this.config.esp32, host: host }
    });
  }

  /**
   * 更新ESP32端口
   */
  updateESP32Port(port, wsPort) {
    this.updateConfig({
      esp32: {
        ...this.config.esp32,
        port: port,
        wsPort: wsPort || port + 1
      }
    });
  }
  
  /**
   * 获取配置状态 - 纯前端版本只返回连接配置
   */
  getConfigStatus() {
    return {
      esp32: this.config.esp32,
      debug: this.config.debug
    };
  }

  /**
   * 重置为默认配置 - 纯前端版本只重置连接配置
   */
  resetToDefaults() {
    this.config = {
      esp32: {
        host: "*************",
        port: 80,
        wsPort: 81,
        timeout: 30000
      },
      debug: {
        logAPIRequests: true,
        showConnectionStatus: true
      }
    };

    this.saveConfig();

    console.log('HardwareConfig: 已重置为默认ESP32配置');

    // 发布重置事件
    if (window.R1Core && window.R1Core.eventBus) {
      window.R1Core.eventBus.emit('esp32.config.reset', this.config);
    }
  }

  /**
   * 导出配置 - 纯前端版本只导出连接配置
   */
  exportConfig() {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * 导入配置 - 纯前端版本只导入连接配置
   */
  importConfig(configJson) {
    try {
      const imported = JSON.parse(configJson);
      this.config = { ...this.config, ...imported };
      this.saveConfig();

      console.log('HardwareConfig: ESP32配置导入成功');

      // 发布导入事件
      if (window.R1Core && window.R1Core.eventBus) {
        window.R1Core.eventBus.emit('esp32.config.imported', this.config);
      }

      return true;
    } catch (error) {
      console.error('HardwareConfig: 配置导入失败', error);
      return false;
    }
  }
}

// 创建全局实例
window.HardwareConfigManager = new HardwareConfigManager();

// 导出配置管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HardwareConfigManager;
}

console.log('🔧 HardwareConfigManager 已初始化 - 纯前端版本');
console.log('📡 ESP32配置:', window.HardwareConfigManager.getESP32Config());

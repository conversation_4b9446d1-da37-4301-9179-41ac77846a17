# ESP32后端功能需求完整分析文档

## 📋 基于前后端分离的精确需求分析

### 🎯 分析方法
1. **对比分析**：对比分离前后的前端功能差异
2. **交互分析**：分析纯净前端的所有用户交互元素
3. **API分析**：统计前端所有ESP32 API调用
4. **功能映射**：将删除的后端功能映射到ESP32实现

---

## 🔍 第一部分：前端交互元素统计

### 1.1 按钮交互统计（43个交互元素）

#### **信号管理模块（13个按钮）**
```html
<!-- 主要操作按钮 -->
<button data-action="toggle-view">列表视图</button>
<button data-action="toggle-multiselect">多选模式</button>
<button data-action="import-signals">导入信号</button>
<button data-action="export-all-signals">导出信号</button>
<button data-action="toggle-search">搜索</button>
<button data-action="toggle-learning">学习信号</button>

<!-- 批量操作按钮 -->
<button data-action="select-all">全选</button>
<button data-action="select-none">全不选</button>
<button data-action="batch-send">批量发射</button>
<button data-action="export-selected">导出选中</button>
<button data-action="delete-selected">删除选中</button>

<!-- 学习控制按钮 -->
<button data-action="cancel-learning">取消学习</button>

<!-- 动态生成的信号操作按钮（每个信号4个按钮）-->
<button data-action="send-signal">发射</button>
<button data-action="edit-signal">编辑</button>
<button data-action="delete-signal">删除</button>
<button data-action="show-details">详情</button>
```

#### **控制面板模块（2个按钮）**
```html
<button data-action="show-task-history">任务历史</button>
<button data-action="create-task">创建任务</button>
```

#### **定时设置模块（2个按钮）**
```html
<button data-action="show-timer-templates">模板</button>
<button data-action="create-timer">创建定时</button>
```

### 1.2 输入控件统计（5个输入元素）

```html
<!-- 搜索和过滤 -->
<input type="text" id="signalSearchInput" placeholder="搜索信号名称...">
<select id="signalTypeFilter"><!-- 信号类型过滤 --></select>
<select id="signalSortBy"><!-- 排序方式 --></select>

<!-- 动态生成的编辑表单 -->
<input type="text" id="editSignalName"><!-- 信号名称编辑 -->
<textarea id="editSignalDescription"><!-- 信号描述编辑 --></textarea>
```

---

## 🌐 第二部分：ESP32 API调用统计

### 2.1 信号管理API（12个端点）

```cpp
// 基础CRUD操作
GET  /api/signals                    // 获取所有信号 ✅
POST /api/signals/delete             // 删除单个信号 ✅
POST /api/signals/batch-delete       // 批量删除信号 ✅
POST /api/signals/update             // 更新信号信息 ✅
POST /api/signals/send               // 发射信号 ✅

// 学习功能
POST /api/signals/learn/start        // 开始学习 ✅
POST /api/signals/learn/stop         // 停止学习 ✅
POST /api/signals/learn/save         // 保存学习结果 ✅

// 导入功能
POST /api/signals/import             // 文件导入解析 ✅
POST /api/signals/import/text        // 文本导入解析 ✅
POST /api/signals/import/execute     // 执行文件导入 ✅
POST /api/signals/import/text/execute // 执行文本导入 ✅
```

### 2.2 系统管理API（3个端点）

```cpp
GET  /api/system/logs                // 获取系统日志 ✅
POST /api/system/logs                // 保存系统日志 ✅
POST /api/system/error-log           // 记录错误日志 ✅
```

### 2.3 控制管理API（2个端点）

```cpp
GET  /api/control/history            // 获取任务历史 ✅
POST /api/control/history            // 保存任务历史 ✅
```

### 2.4 WebSocket事件（6个事件）

```cpp
// 学习相关事件
signal.learned      // 学习完成事件 ✅
learning.error      // 学习错误事件 ✅

// 信号发射事件
signal.sent         // 信号发射完成事件 ✅
signal.failed       // 信号发射失败事件 ✅

// 系统状态事件
system.status       // 系统状态更新事件 ✅
esp32.connected     // ESP32连接事件 ✅
```

---

## 🗂️ 第三部分：数据结构需求

### 3.1 信号数据结构

```cpp
struct Signal {
    String id;              // 信号ID (signal_xxxxxxxx)
    String name;            // 信号名称
    String type;            // 信号类型 (tv/ac/fan/light/other)
    String description;     // 信号描述
    String signalCode;      // 信号码 (如: 0x20DF10EF)
    String protocol;        // 协议 (NEC/RC5/SONY/RAW)
    String frequency;       // 载波频率 (如: 38000)
    String rawData;         // 原始数据
    bool isLearned;         // 是否为学习信号
    unsigned long created;  // 创建时间戳
    unsigned long lastSent; // 最后发射时间戳
    int sentCount;          // 发射次数
    bool parseSuccess;      // 解析是否成功
};
```

### 3.2 API响应格式

```cpp
struct APIResponse {
    bool success;           // 操作是否成功
    String error;           // 错误信息（失败时）
    JsonObject data;        // 响应数据（成功时）
    unsigned long timestamp; // 响应时间戳
};
```

---

## ⚙️ 第四部分：核心功能需求

### 4.1 红外硬件控制

```cpp
// 红外学习功能
bool startIRLearning(int timeout = 10000);
bool stopIRLearning();
bool isLearning();
IRSignal getLearnedSignal();

// 红外发射功能
bool sendIRSignal(String signalId);
bool sendRawIRSignal(String rawData, int frequency);

// 硬件状态检测
bool isIRTransmitterReady();
bool isIRReceiverReady();
```

### 4.2 数据存储管理

```cpp
// 信号存储
bool saveSignal(Signal signal);
bool deleteSignal(String signalId);
bool updateSignal(String signalId, Signal signal);
Signal getSignal(String signalId);
std::vector<Signal> getAllSignals();

// 批量操作
bool batchDeleteSignals(std::vector<String> signalIds);
bool importSignals(std::vector<Signal> signals);
```

### 4.3 文件解析系统

```cpp
// 文件格式解析
std::vector<Signal> parseJSONFile(String content);
std::vector<Signal> parseCSVFile(String content);
std::vector<Signal> parseTextFile(String content);

// 数据验证
bool validateSignal(Signal signal);
bool validateSignalCode(String code);
bool validateProtocol(String protocol);
```

---

## 🚀 第五部分：实施优先级

### 高优先级（核心功能）
1. ✅ HTTP服务器和基础路由
2. ✅ 信号CRUD API实现
3. ✅ 红外学习和发射硬件控制
4. ✅ WebSocket事件系统
5. ✅ 数据存储管理

### 中优先级（重要功能）
6. 文件导入解析系统
7. 批量操作处理
8. 系统日志管理
9. 任务历史管理
10. 错误处理和恢复

### 低优先级（增强功能）
11. 性能监控和统计
12. 配置管理界面
13. 调试和诊断工具
14. 高级错误恢复
15. 系统优化功能

---

## 📊 第六部分：功能对比表

| 功能类别 | 分离前（前端实现） | 分离后（ESP32实现） | 状态 |
|---------|------------------|-------------------|------|
| 信号验证 | validateSignalFormat() | validateSignal() | ❌ 待实现 |
| 信号存储 | localStorage | SPIFFS/LittleFS | ❌ 待实现 |
| 学习控制 | JavaScript定时器 | 硬件中断+定时器 | ❌ 待实现 |
| 文件解析 | JavaScript解析 | C++解析库 | ❌ 待实现 |
| 批量操作 | 前端循环处理 | 后端批量处理 | ❌ 待实现 |
| 错误日志 | localStorage | 文件系统 | ❌ 待实现 |
| 任务历史 | localStorage | 文件系统 | ❌ 待实现 |

---

## ✅ 确认清单

- [x] 统计了所有前端交互元素（43个）
- [x] 分析了所有ESP32 API调用（17个端点）
- [x] 定义了完整的数据结构
- [x] 对比了分离前后的功能差异
- [x] 制定了实施优先级
- [x] 创建了功能对比表

**总结：需要实现17个API端点、6个WebSocket事件、5个核心数据结构，支持43个前端交互元素的完整后端功能。**

---

## 🔧 第七部分：技术实现规范

### 7.1 HTTP服务器配置

```cpp
// 服务器配置
const int HTTP_PORT = 80;
const int WS_PORT = 81;
const int MAX_CLIENTS = 4;
const int REQUEST_TIMEOUT = 30000;

// CORS配置
const char* CORS_HEADERS = "Content-Type, Authorization, X-Requested-With";
const char* CORS_METHODS = "GET, POST, PUT, DELETE, OPTIONS";
const char* CORS_ORIGIN = "*";
```

### 7.2 文件系统配置

```cpp
// 存储配置
const char* SIGNALS_FILE = "/signals.json";
const char* CONFIG_FILE = "/config.json";
const char* LOGS_FILE = "/logs.json";
const char* HISTORY_FILE = "/history.json";

// 存储限制
const int MAX_SIGNALS = 200;
const int MAX_LOG_ENTRIES = 100;
const int MAX_HISTORY_ENTRIES = 100;
const size_t MAX_FILE_SIZE = 64 * 1024; // 64KB
```

### 7.3 红外硬件配置

```cpp
// 硬件引脚配置
const int IR_SEND_PIN = 4;      // 红外发射引脚
const int IR_RECV_PIN = 15;     // 红外接收引脚
const int STATUS_LED_PIN = 2;   // 状态指示LED

// 红外参数配置
const int IR_FREQUENCY = 38000; // 默认载波频率
const int LEARNING_TIMEOUT = 10000; // 学习超时时间(ms)
const int SEND_REPEAT = 1;      // 发射重复次数
```

### 7.4 内存管理配置

```cpp
// 内存配置
const size_t JSON_BUFFER_SIZE = 8192;   // JSON缓冲区大小
const size_t SIGNAL_CACHE_SIZE = 50;    // 信号缓存数量
const size_t WS_BUFFER_SIZE = 1024;     // WebSocket缓冲区

// PSRAM配置（如果可用）
#ifdef BOARD_HAS_PSRAM
const bool USE_PSRAM = true;
const size_t PSRAM_BUFFER_SIZE = 32768;
#endif
```

---

## 📝 第八部分：API详细规范

### 8.1 信号管理API详细规范

#### GET /api/signals
```json
// 响应格式
{
  "success": true,
  "data": [
    {
      "id": "signal_12345678",
      "name": "客厅电视开关",
      "type": "tv",
      "description": "客厅电视开关按钮",
      "signalCode": "0x20DF10EF",
      "protocol": "NEC",
      "frequency": "38000",
      "rawData": "9000,4500,560,560,560,1690...",
      "isLearned": true,
      "created": 1640995200000,
      "lastSent": 1640995800000,
      "sentCount": 5,
      "parseSuccess": true
    }
  ],
  "timestamp": 1640995200000
}
```

#### POST /api/signals/delete
```json
// 请求格式
{
  "signalId": "signal_12345678"
}

// 响应格式
{
  "success": true,
  "data": {
    "deletedId": "signal_12345678",
    "message": "信号删除成功"
  },
  "timestamp": 1640995200000
}
```

#### POST /api/signals/learn/start
```json
// 请求格式
{
  "timeout": 10000,
  "timestamp": 1640995200000
}

// 响应格式
{
  "success": true,
  "data": {
    "learningId": "learning_12345678",
    "timeout": 10000,
    "message": "学习模式已启动"
  },
  "timestamp": 1640995200000
}
```

### 8.2 WebSocket事件详细规范

#### signal.learned 事件
```json
{
  "event": "signal.learned",
  "data": {
    "success": true,
    "signal": {
      "signalCode": "0x20DF10EF",
      "protocol": "NEC",
      "frequency": "38000",
      "rawData": "9000,4500,560,560...",
      "parseSuccess": true,
      "detectedAt": 1640995200000
    }
  },
  "timestamp": 1640995200000
}
```

#### learning.error 事件
```json
{
  "event": "learning.error",
  "data": {
    "error": "学习超时",
    "code": "LEARNING_TIMEOUT",
    "details": "10秒内未检测到红外信号"
  },
  "timestamp": 1640995200000
}
```

---

## 🛠️ 第九部分：实现架构设计

### 9.1 模块化架构

```cpp
// 核心模块类
class SignalManager;     // 信号管理核心
class IRController;      // 红外硬件控制
class WebServerManager;  // Web服务管理
class FileSystemManager; // 文件系统管理
class WebSocketManager;  // WebSocket管理

// 功能模块类
class ImportProcessor;   // 导入处理器
class DataValidator;     // 数据验证器
class LogManager;        // 日志管理器
class TaskManager;       // 任务管理器
```

### 9.2 数据流设计

```
前端请求 → 路由分发 → 参数验证 → 业务逻辑 → 硬件操作 → 数据存储 → 响应返回
                                      ↓
                              WebSocket事件推送
```

### 9.3 错误处理策略

```cpp
// 错误代码定义
enum ErrorCode {
    SUCCESS = 0,
    INVALID_PARAMETER = 1001,
    SIGNAL_NOT_FOUND = 1002,
    LEARNING_TIMEOUT = 1003,
    HARDWARE_ERROR = 1004,
    STORAGE_ERROR = 1005,
    MEMORY_ERROR = 1006
};

// 错误响应格式
struct ErrorResponse {
    bool success = false;
    int code;
    String error;
    String details;
    unsigned long timestamp;
};
```

---

## 📋 第十部分：开发检查清单

### 10.1 核心功能检查清单

- [ ] HTTP服务器初始化和路由配置
- [ ] WebSocket服务器初始化和事件处理
- [ ] 文件系统初始化和数据加载
- [ ] 红外硬件初始化和功能测试
- [ ] 信号CRUD操作完整实现
- [ ] 学习功能完整实现
- [ ] 导入功能完整实现
- [ ] 批量操作完整实现

### 10.2 数据管理检查清单

- [ ] 信号数据结构定义和验证
- [ ] JSON序列化和反序列化
- [ ] 文件读写和错误处理
- [ ] 内存管理和优化
- [ ] 数据备份和恢复机制

### 10.3 网络通信检查清单

- [ ] HTTP请求处理和响应格式
- [ ] WebSocket连接管理和消息推送
- [ ] CORS配置和跨域处理
- [ ] 错误处理和状态码管理
- [ ] 超时处理和连接恢复

### 10.4 硬件控制检查清单

- [ ] 红外发射功能和参数配置
- [ ] 红外接收功能和信号解析
- [ ] 学习超时控制和状态管理
- [ ] 硬件状态检测和错误恢复
- [ ] 多协议支持和格式转换

---

## 🎯 最终确认

**本文档基于精确的前后端分离分析，确保了：**

1. ✅ **完整性**：覆盖了前端所有43个交互元素的后端支持
2. ✅ **准确性**：基于实际的17个API调用和6个WebSocket事件
3. ✅ **可实施性**：提供了详细的技术规范和实现架构
4. ✅ **可验证性**：包含了完整的开发检查清单

**准备开始ESP32后端实现！**

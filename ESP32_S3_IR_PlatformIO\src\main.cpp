/**
 * ESP32-S3 红外控制系统 - PlatformIO版本 (无PSRAM)
 * 支持纯净前端的完整后端实现
 *
 * 功能特性：
 * - 18个API端点支持
 * - 8个WebSocket事件
 * - 红外学习和发射
 * - 文件系统数据管理
 * - 纯RAM模式运行 (禁用PSRAM)
 *
 * 硬件兼容性：
 * - ESP32-S3 v0.2 及以上版本
 * - 16MB Flash (GD25Q128)
 * - 不依赖PSRAM (解决v0.2版本PSRAM兼容性问题)
 */

// ==================== 核心系统库 ====================
#include <Arduino.h>
#include <WiFi.h>
#include <esp_wifi.h>
#include <esp_system.h>
#include <esp_heap_caps.h>

// ==================== 文件系统 ====================
#include <FS.h>
#include <SPIFFS.h>

// ==================== 网络通信 ====================
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <AsyncWebSocket.h>

// ==================== JSON处理 ====================
#include <ArduinoJson.h>

// ==================== 红外通信 ====================
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>

// ==================== 时间处理 ====================
#include <TimeLib.h>

// ==================== 系统工具 ====================
#include <Preferences.h>
#include <Update.h>

// 系统配置
#include "system-config.h"
#include "hardware-config.h"

// 核心模块
#include "MemoryAllocator.h"
#include "DataManager.h"
#include "IRController.h"
#include "TaskManager.h"
#include "WebServerManager.h"
#include "WSManager.h"
#include "NetworkSecurity.h"
#include "SystemManager.h"

// 全局对象
SystemManager* g_systemManager = nullptr;

// 系统状态
bool g_systemInitialized = false;
unsigned long g_lastHeartbeat = 0;
const unsigned long HEARTBEAT_INTERVAL = 30000; // 30秒心跳

// 内存监控
const size_t MIN_FREE_HEAP = 50 * 1024;  // 50KB最小堆内存

void setup() {
    Serial.begin(115200);
    Serial.println("\n" + String("=").repeat(50));
    Serial.println("🚀 ESP32-S3 红外控制系统启动 (PlatformIO版本)");
    Serial.println("📋 模式: 纯RAM模式 (PSRAM已禁用)");
    Serial.println(String("=").repeat(50));
    
    // 显示硬件信息
    Serial.printf("💻 芯片型号: %s\n", ESP.getChipModel());
    Serial.printf("🔧 芯片版本: %d\n", ESP.getChipRevision());
    Serial.printf("⚡ CPU频率: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("💾 Flash大小: %d MB\n", ESP.getFlashChipSize() / (1024 * 1024));
    Serial.printf("🧠 总RAM: %d KB\n", ESP.getHeapSize() / 1024);
    Serial.printf("🧠 可用RAM: %d KB\n", ESP.getFreeHeap() / 1024);
    
    // 确认PSRAM已禁用
    Serial.println("📋 PSRAM状态: 已禁用 (纯RAM模式)");
    
    // 1. 初始化内存分配器
    Serial.println("🧠 初始化内存分配器...");
    if (!MemoryAllocator::initialize()) {
        Serial.println("❌ 内存分配器初始化失败");
        return;
    }
    
    // 2. 创建系统管理器
    Serial.println("🏗️ 创建系统管理器...");
    SystemCapacity capacity = getSystemCapacity(STANDARD_MODE);  // 使用标准模式容量
    
    try {
        g_systemManager = new SystemManager(capacity);
        if (!g_systemManager) {
            Serial.println("❌ 系统管理器创建失败");
            return;
        }
        Serial.println("✅ 系统管理器创建成功");
    } catch (const std::exception& e) {
        Serial.printf("❌ 系统管理器创建异常: %s\n", e.what());
        return;
    } catch (...) {
        Serial.println("❌ 系统管理器创建未知异常");
        return;
    }
    
    // 3. 初始化系统
    Serial.println("🚀 初始化系统...");
    if (!g_systemManager->initialize()) {
        Serial.println("❌ 系统初始化失败");
        delete g_systemManager;
        g_systemManager = nullptr;
        return;
    }
    
    // 4. 启动系统服务
    Serial.println("🚀 启动系统服务...");
    if (!g_systemManager->start()) {
        Serial.println("❌ 系统服务启动失败");
        delete g_systemManager;
        g_systemManager = nullptr;
        return;
    }
    
    g_systemInitialized = true;
    
    Serial.println(String("=").repeat(50));
    Serial.println("✅ 系统启动完成！");
    Serial.printf("🌐 访问地址: http://%s\n", WiFi.localIP().toString().c_str());
    Serial.printf("🧠 启动后可用内存: %d KB\n", ESP.getFreeHeap() / 1024);
    Serial.println(String("=").repeat(50));
}

void loop() {
    if (!g_systemInitialized || !g_systemManager) {
        delay(1000);
        return;
    }
    
    // 系统主循环
    g_systemManager->loop();
    
    // 系统心跳
    unsigned long currentTime = millis();
    if (currentTime - g_lastHeartbeat >= HEARTBEAT_INTERVAL) {
        systemHeartbeat();
        g_lastHeartbeat = currentTime;
    }
    
    // 内存健康检查
    checkMemoryHealth();
    
    // 短暂延迟
    delay(10);
}

/**
 * 系统心跳
 */
void systemHeartbeat() {
    if (!g_systemManager) return;
    
    Serial.printf("💓 系统心跳 - 运行时间: %lu 秒, 可用内存: %d KB\n", 
                 millis() / 1000, ESP.getFreeHeap() / 1024);
    
    // 检查系统健康状态
    if (!g_systemManager->isHealthy()) {
        Serial.println("⚠️ 系统健康检查失败");
    }
}

/**
 * 检查内存健康状态
 */
void checkMemoryHealth() {
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < MIN_FREE_HEAP) {
        Serial.printf("⚠️ 堆内存不足: %d KB\n", freeHeap / 1024);
        MemoryAllocator::performCleanup();
    }
    
    // 检查内存碎片化
    float fragmentation = MemoryAllocator::getFragmentationLevel();
    if (fragmentation > 0.8f) {
        Serial.printf("⚠️ 内存碎片化严重: %.1f%%\n", fragmentation * 100);
        MemoryAllocator::defragmentMemory();
    }
}

/**
 * 系统重启
 */
void systemRestart() {
    Serial.println("🔄 系统重启中...");

    if (g_systemManager) {
        g_systemManager->shutdown();
        delete g_systemManager;
        g_systemManager = nullptr;
    }

    delay(1000);
    ESP.restart();
}

/**
 * 紧急停止
 */
void emergencyStop() {
    Serial.println("🚨 紧急停止激活");
    
    if (g_systemManager) {
        g_systemManager->emergencyStop();
    }
    
    g_systemInitialized = false;
}

/**
 * 获取系统容量配置 (纯RAM模式)
 */
SystemCapacity getSystemCapacity(SystemMode mode) {
    SystemCapacity capacity;
    
    // 纯RAM模式 - 使用较小的容量配置
    capacity.maxSignals = 50;   // 减少到50个信号
    capacity.maxTasks = 20;     // 减少到20个任务
    capacity.maxTimers = 10;    // 减少到10个定时器
    capacity.maxClients = 5;    // 减少到5个客户端
    
    return capacity;
}

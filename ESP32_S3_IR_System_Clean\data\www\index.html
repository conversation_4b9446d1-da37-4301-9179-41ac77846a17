<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32-S3 IR Controller</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .status-item h3 {
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .status-item p {
            font-size: 1.1em;
            font-weight: bold;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .control-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .control-card h2 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-timestamp {
            color: #a0aec0;
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ESP32-S3 IR Controller</h1>
            <p>Clean Architecture - Zero Global Memory Allocation</p>
        </div>
        
        <div class="status-card">
            <div class="connection-status">
                <div class="status-indicator" id="wsStatus"></div>
                <span id="wsStatusText">Connecting...</span>
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <h3>Uptime</h3>
                    <p id="uptime">--</p>
                </div>
                <div class="status-item">
                    <h3>Free Heap</h3>
                    <p id="freeHeap">--</p>
                </div>
                <div class="status-item">
                    <h3>Free PSRAM</h3>
                    <p id="freePsram">--</p>
                </div>
                <div class="status-item">
                    <h3>WebSocket Clients</h3>
                    <p id="wsClients">--</p>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-card">
                <h2>🎮 Quick Controls</h2>
                <button class="btn btn-primary" onclick="sendCommand('get_status')">
                    📊 Get Status
                </button>
                <button class="btn btn-success" onclick="sendCommand('test_signal')">
                    📡 Test IR Signal
                </button>
                <button class="btn btn-warning" onclick="sendCommand('start_learning')">
                    🎓 Start Learning
                </button>
            </div>
            
            <div class="control-card">
                <h2>📋 System Log</h2>
                <div class="log-container" id="logContainer">
                    <div class="log-entry">
                        <span class="log-timestamp">[00:00:00]</span>
                        <span>System starting...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let reconnectInterval = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                updateConnectionStatus(true);
                addLogEntry('WebSocket connected');
                
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
                
                // Request initial status
                sendCommand('get_status');
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (e) {
                    addLogEntry('Error parsing message: ' + e.message);
                }
            };
            
            ws.onclose = function() {
                updateConnectionStatus(false);
                addLogEntry('WebSocket disconnected');
                
                // Auto-reconnect
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(connectWebSocket, 3000);
                }
            };
            
            ws.onerror = function(error) {
                addLogEntry('WebSocket error: ' + error);
            };
        }
        
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('wsStatus');
            const text = document.getElementById('wsStatusText');
            
            if (connected) {
                indicator.classList.add('connected');
                text.textContent = 'Connected';
            } else {
                indicator.classList.remove('connected');
                text.textContent = 'Disconnected';
            }
        }
        
        function handleWebSocketMessage(data) {
            addLogEntry(`Received: ${data.type}`);
            
            if (data.type === 'status' && data.data) {
                updateStatus(data.data);
            } else if (data.type === 'welcome') {
                addLogEntry(data.message);
            }
        }
        
        function updateStatus(status) {
            document.getElementById('uptime').textContent = formatUptime(status.uptime || 0);
            document.getElementById('freeHeap').textContent = formatBytes(status.free_heap || 0);
            document.getElementById('freePsram').textContent = formatBytes(status.free_psram || 0);
            document.getElementById('wsClients').textContent = status.websocket_clients || 0;
        }
        
        function sendCommand(command) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: command,
                    timestamp: Date.now()
                };
                
                ws.send(JSON.stringify(message));
                addLogEntry(`Sent: ${command}`);
            } else {
                addLogEntry('WebSocket not connected');
            }
        }
        
        function addLogEntry(message) {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleTimeString();
            entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span><span>${message}</span>`;
            
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            
            // Keep only last 50 entries
            while (container.children.length > 50) {
                container.removeChild(container.firstChild);
            }
        }
        
        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes % 60}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            
            // Update status every 5 seconds
            setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    sendCommand('get_status');
                }
            }, 5000);
        });
    </script>
</body>
</html>

/*
 * WebServerManager.h - Web Server Management with Zero Global Memory Allocation
 */

#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

class WebServerManager {
public:
    WebServerManager();
    ~WebServerManager();
    
    bool initialize(AsyncWebServer* server, AsyncWebSocket* webSocket);
    void handleLoop();
    
    // WebSocket management
    void broadcastMessage(const String& type, const DynamicJsonDocument& data);
    void sendMessage(uint32_t clientId, const String& type, const DynamicJsonDocument& data);
    
    // Status
    int getConnectedClients() const;
    DynamicJsonDocument getStatus() const;

private:
    bool m_initialized;
    AsyncWebServer* m_server;
    AsyncWebSocket* m_webSocket;
    
    // Event handlers
    void setupRoutes();
    void setupWebSocketHandlers();
    
    // API handlers
    void handleGetSignals(AsyncWebServerRequest* request);
    void handleAddSignal(AsyncWebServerRequest* request);
    void handleSendSignal(AsyncWebServerRequest* request);
    void handleGetStatus(AsyncWebServerRequest* request);
    
    // WebSocket handlers
    void onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                         AwsEventType type, void* arg, uint8_t* data, size_t len);
    void handleWebSocketMessage(AsyncWebSocketClient* client, const String& message);
    
    // Utility
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
    void sendJsonResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& doc);
};

#endif // WEB_SERVER_MANAGER_H

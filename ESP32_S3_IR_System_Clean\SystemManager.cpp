#include "SystemManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "WebServerManager.h"
#include "WiFiManager.h"

SystemManager::SystemManager() 
    : m_initialized(false)
    , m_startTime(millis())
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_webServerManager(nullptr)
    , m_wifiManager(nullptr)
    , m_server(nullptr)
    , m_webSocket(nullptr)
{
    // Constructor does NO memory allocation
    Serial.println("SystemManager created (no memory allocated)");
}

SystemManager::~SystemManager() {
    cleanup();
}

bool SystemManager::initialize() {
    Serial.println("🔧 Initializing SystemManager...");
    
    if (m_initialized) {
        Serial.println("SystemManager already initialized");
        return true;
    }
    
    // Step 1: Create core server components
    Serial.println("📡 Creating server components...");
    m_server = new AsyncWebServer(80);
    if (!m_server) {
        Serial.println("❌ Failed to create AsyncWebServer");
        return false;
    }
    
    m_webSocket = new AsyncWebSocket("/ws");
    if (!m_webSocket) {
        Serial.println("❌ Failed to create AsyncWebSocket");
        cleanup();
        return false;
    }
    
    // Step 2: Create component managers
    if (!initializeComponents()) {
        Serial.println("❌ Component initialization failed");
        cleanup();
        return false;
    }
    
    // Step 3: Initialize network
    if (!initializeNetwork()) {
        Serial.println("❌ Network initialization failed");
        cleanup();
        return false;
    }
    
    // Step 4: Initialize web server
    if (!initializeWebServer()) {
        Serial.println("❌ Web server initialization failed");
        cleanup();
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ SystemManager initialized successfully");
    
    printMemoryStatus();
    return true;
}

bool SystemManager::initializeComponents() {
    Serial.println("📋 Creating component managers...");
    
    // Create DataManager
    m_dataManager = new DataManager();
    if (!m_dataManager || !m_dataManager->initialize()) {
        Serial.println("❌ DataManager initialization failed");
        return false;
    }
    Serial.println("✅ DataManager created and initialized");
    
    // Create IRController
    m_irController = new IRController();
    if (!m_irController || !m_irController->initialize()) {
        Serial.println("❌ IRController initialization failed");
        return false;
    }
    Serial.println("✅ IRController created and initialized");
    
    // Create WiFiManager
    m_wifiManager = new WiFiManager();
    if (!m_wifiManager || !m_wifiManager->initialize()) {
        Serial.println("❌ WiFiManager initialization failed");
        return false;
    }
    Serial.println("✅ WiFiManager created and initialized");
    
    // Create WebServerManager
    m_webServerManager = new WebServerManager();
    if (!m_webServerManager || !m_webServerManager->initialize(m_server, m_webSocket)) {
        Serial.println("❌ WebServerManager initialization failed");
        return false;
    }
    Serial.println("✅ WebServerManager created and initialized");
    
    return true;
}

bool SystemManager::initializeNetwork() {
    Serial.println("📶 Initializing network...");
    
    if (!m_wifiManager) {
        Serial.println("❌ WiFiManager not available");
        return false;
    }
    
    // Start WiFi connection
    m_wifiManager->startConnection();
    
    Serial.println("✅ Network initialization started");
    return true;
}

bool SystemManager::initializeWebServer() {
    Serial.println("🌐 Initializing web server...");
    
    if (!m_server || !m_webServerManager) {
        Serial.println("❌ Server components not available");
        return false;
    }
    
    // Start web server
    m_server->begin();
    Serial.println("✅ Web server started on port 80");
    
    return true;
}

void SystemManager::handleLoop() {
    if (!m_initialized) {
        return;
    }
    
    // Handle all component loops
    if (m_wifiManager) {
        m_wifiManager->handleLoop();
    }
    
    if (m_irController) {
        m_irController->handleLoop();
    }
    
    if (m_webServerManager) {
        m_webServerManager->handleLoop();
    }
}

DataManager* SystemManager::getDataManager() {
    return m_dataManager;
}

IRController* SystemManager::getIRController() {
    return m_irController;
}

WebServerManager* SystemManager::getWebServerManager() {
    return m_webServerManager;
}

WiFiManager* SystemManager::getWiFiManager() {
    return m_wifiManager;
}

unsigned long SystemManager::getUptime() const {
    return millis() - m_startTime;
}

void SystemManager::restart() {
    Serial.println("🔄 System restart requested");
    delay(1000);
    ESP.restart();
}

void SystemManager::factoryReset() {
    Serial.println("🏭 Factory reset requested");
    
    if (m_dataManager) {
        m_dataManager->clearAllData();
    }
    
    // Clear WiFi credentials
    WiFi.disconnect(true);
    
    Serial.println("Factory reset completed - restarting...");
    delay(2000);
    ESP.restart();
}

void SystemManager::printMemoryStatus() {
    Serial.println("📊 Memory Status:");
    Serial.printf("   Free Heap: %d bytes (%.2f KB)\n", ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    if (psramFound()) {
        Serial.printf("   Free PSRAM: %d bytes (%.2f KB)\n", ESP.getFreePsram(), ESP.getFreePsram() / 1024.0);
    }
}

size_t SystemManager::getFreePSRAM() {
    return psramFound() ? ESP.getFreePsram() : 0;
}

size_t SystemManager::getFreeHeap() {
    return ESP.getFreeHeap();
}

void SystemManager::cleanup() {
    Serial.println("🧹 Cleaning up SystemManager...");
    
    if (m_webServerManager) {
        delete m_webServerManager;
        m_webServerManager = nullptr;
    }
    
    if (m_wifiManager) {
        delete m_wifiManager;
        m_wifiManager = nullptr;
    }
    
    if (m_irController) {
        delete m_irController;
        m_irController = nullptr;
    }
    
    if (m_dataManager) {
        delete m_dataManager;
        m_dataManager = nullptr;
    }
    
    if (m_webSocket) {
        delete m_webSocket;
        m_webSocket = nullptr;
    }
    
    if (m_server) {
        delete m_server;
        m_server = nullptr;
    }
    
    m_initialized = false;
}

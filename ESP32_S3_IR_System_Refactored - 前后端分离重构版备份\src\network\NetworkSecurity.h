#ifndef NETWORK_SECURITY_H
#define NETWORK_SECURITY_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <map>
#include "../../config/network-config.h"

/**
 * 网络安全管理器类
 * 
 * 负责网络安全相关功能
 * 包括认证、授权、速率限制、CORS等
 */
class NetworkSecurity {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     */
    NetworkSecurity();
    
    /**
     * 析构函数
     */
    ~NetworkSecurity();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化网络安全
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭网络安全
     */
    void shutdown();
    
    // ==================== 认证功能 ====================
    
    /**
     * 验证API密钥
     * @param apiKey API密钥
     * @return bool 验证是否通过
     */
    bool validateAPIKey(const String& apiKey);
    
    /**
     * 生成会话令牌
     * @param clientId 客户端ID
     * @return String 会话令牌
     */
    String generateSessionToken(const String& clientId);
    
    /**
     * 验证会话令牌
     * @param token 会话令牌
     * @return bool 验证是否通过
     */
    bool validateSessionToken(const String& token);
    
    /**
     * 撤销会话令牌
     * @param token 会话令牌
     * @return bool 撤销是否成功
     */
    bool revokeSessionToken(const String& token);
    
    /**
     * 清理过期令牌
     * @return int 清理的令牌数量
     */
    int cleanupExpiredTokens();
    
    // ==================== 速率限制 ====================
    
    /**
     * 检查速率限制
     * @param clientIP 客户端IP
     * @param endpoint 端点
     * @return bool 是否允许请求
     */
    bool checkRateLimit(const String& clientIP, const String& endpoint);
    
    /**
     * 记录请求
     * @param clientIP 客户端IP
     * @param endpoint 端点
     */
    void recordRequest(const String& clientIP, const String& endpoint);
    
    /**
     * 重置速率限制
     * @param clientIP 客户端IP
     */
    void resetRateLimit(const String& clientIP);
    
    /**
     * 获取速率限制状态
     * @param clientIP 客户端IP
     * @return DynamicJsonDocument 速率限制状态
     */
    DynamicJsonDocument getRateLimitStatus(const String& clientIP);
    
    // ==================== CORS处理 ====================
    
    /**
     * 验证CORS请求
     * @param origin 请求来源
     * @param method 请求方法
     * @return bool 是否允许
     */
    bool validateCORSRequest(const String& origin, const String& method);
    
    /**
     * 获取CORS头部
     * @param origin 请求来源
     * @return std::map<String, String> CORS头部映射
     */
    std::map<String, String> getCORSHeaders(const String& origin);
    
    // ==================== IP过滤 ====================
    
    /**
     * 检查IP是否被阻止
     * @param clientIP 客户端IP
     * @return bool 是否被阻止
     */
    bool isIPBlocked(const String& clientIP);
    
    /**
     * 阻止IP地址
     * @param clientIP 客户端IP
     * @param reason 阻止原因
     * @param duration 阻止时长（毫秒，0表示永久）
     */
    void blockIP(const String& clientIP, const String& reason, unsigned long duration = 0);
    
    /**
     * 解除IP阻止
     * @param clientIP 客户端IP
     * @return bool 解除是否成功
     */
    bool unblockIP(const String& clientIP);
    
    /**
     * 获取阻止的IP列表
     * @return DynamicJsonDocument 阻止的IP列表
     */
    DynamicJsonDocument getBlockedIPs();
    
    // ==================== 安全统计 ====================
    
    /**
     * 获取安全统计信息
     * @return DynamicJsonDocument 安全统计
     */
    DynamicJsonDocument getSecurityStats();
    
    /**
     * 重置安全统计
     */
    void resetSecurityStats();
    
    /**
     * 记录安全事件
     * @param eventType 事件类型
     * @param clientIP 客户端IP
     * @param details 事件详情
     */
    void logSecurityEvent(const String& eventType, const String& clientIP, const String& details);

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                          // 是否已初始化
    
    // 会话管理
    struct SessionInfo {
        String clientId;
        unsigned long createdTime;
        unsigned long lastAccessTime;
        bool isValid;
    };
    std::map<String, SessionInfo> m_sessions;    // 会话映射表
    
    // 速率限制
    struct RateLimitInfo {
        unsigned long windowStart;
        int requestCount;
        bool isBlocked;
        unsigned long blockUntil;
    };
    std::map<String, RateLimitInfo> m_rateLimits; // 速率限制映射表
    
    // IP阻止
    struct BlockedIPInfo {
        String reason;
        unsigned long blockedTime;
        unsigned long blockDuration;
        bool isPermanent;
    };
    std::map<String, BlockedIPInfo> m_blockedIPs; // 阻止IP映射表
    
    // 安全统计
    unsigned long m_totalRequests;               // 总请求数
    unsigned long m_blockedRequests;             // 被阻止的请求数
    unsigned long m_rateLimitedRequests;         // 被速率限制的请求数
    unsigned long m_invalidTokenRequests;        // 无效令牌请求数
    unsigned long m_corsViolations;              // CORS违规数
    
    // ==================== 私有方法 ====================
    
    /**
     * 生成随机令牌
     * @param length 令牌长度
     * @return String 随机令牌
     */
    String generateRandomToken(int length = 32);
    
    /**
     * 计算令牌哈希
     * @param token 令牌
     * @return String 令牌哈希
     */
    String calculateTokenHash(const String& token);
    
    /**
     * 检查会话是否过期
     * @param session 会话信息
     * @return bool 是否过期
     */
    bool isSessionExpired(const SessionInfo& session);
    
    /**
     * 检查速率限制窗口
     * @param rateLimitInfo 速率限制信息
     * @return bool 是否需要重置窗口
     */
    bool shouldResetRateLimitWindow(const RateLimitInfo& rateLimitInfo);
    
    /**
     * 检查IP阻止是否过期
     * @param blockedInfo 阻止信息
     * @return bool 是否过期
     */
    bool isIPBlockExpired(const BlockedIPInfo& blockedInfo);
    
    /**
     * 清理过期数据
     */
    void performCleanup();
    
    /**
     * 更新安全统计
     * @param eventType 事件类型
     */
    void updateSecurityStats(const String& eventType);
};

#endif // NETWORK_SECURITY_H

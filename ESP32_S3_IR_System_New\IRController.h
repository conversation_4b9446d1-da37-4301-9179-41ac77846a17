/*
 * IRController.h - IR控制器（100% API兼容）
 * 
 * 零全局内存分配的IR控制系统
 * 完全兼容原前端的所有IR相关API调用
 */

#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <ArduinoJson.h>
#include <functional>

// Forward declaration
class DataManager;

class IRController {
public:
    IRController();
    ~IRController();
    
    bool initialize();
    void handleLoop();
    
    // Signal transmission (API Compatible)
    bool sendSignal(const String& signalId);
    bool sendSignalByCode(const String& protocol, const String& code, const String& frequency = "38000");
    bool sendBatchSignals(const String& signalIds, int delayMs = 1000);
    
    // Signal learning (API Compatible)
    bool startLearning(const String& signalName = "", int timeoutMs = 30000);
    bool stopLearning();
    bool isLearning() const { return m_isLearning; }
    DynamicJsonDocument getLearningStatus() const;
    
    // Status and info (API Compatible)
    DynamicJsonDocument getStatus() const;
    DynamicJsonDocument getInfo() const;
    
    // Set data manager reference
    void setDataManager(DataManager* dataManager) { m_dataManager = dataManager; }

private:
    bool m_initialized;
    bool m_isLearning;
    unsigned long m_learningStartTime;
    int m_learningTimeout;
    String m_learningSignalName;
    String m_lastLearnedSignal;
    
    // IR components - created after initialization
    IRsend* m_irSend;
    IRrecv* m_irRecv;
    
    // Data manager reference
    DataManager* m_dataManager;
    
    // Pin definitions
    static const int IR_SEND_PIN = 4;
    static const int IR_RECV_PIN = 5;
    
    // Learning callback
    std::function<void(const String&, const String&, const String&)> m_learningCallback;
    
    // Internal methods
    void processReceivedSignal();
    bool sendNECSignal(uint32_t code, int bits = 32);
    bool sendSonySignal(uint32_t code, int bits = 12);
    bool sendRC5Signal(uint32_t code, int bits = 13);
    bool sendRawSignal(const String& rawData);
    
    // Protocol detection
    String detectProtocol(decode_type_t type);
    String formatSignalCode(decode_results* results);
    
    // Validation
    bool validateSignalCode(const String& protocol, const String& code);
};

#endif // IR_CONTROLLER_H

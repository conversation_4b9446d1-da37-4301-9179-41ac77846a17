/*
 * IRremoteESP8266 Arduino Core 3.x 兼容性补丁
 * 
 * 此文件修复IRremoteESP8266库与ESP32 Arduino Core 3.x的兼容性问题
 * 主要解决Timer API变化导致的编译错误
 * 
 * 使用方法：
 * 1. 将此文件放在项目根目录
 * 2. 在主文件中包含此头文件（在IRremoteESP8266.h之前）
 * 3. 重新编译项目
 */

#ifndef IRREMOTEESP8266_CORE3_FIX_H
#define IRREMOTEESP8266_CORE3_FIX_H

#include <Arduino.h>

// 检查Arduino Core版本
#if ESP_ARDUINO_VERSION >= ESP_ARDUINO_VERSION_VAL(3, 0, 0)

// 为了兼容性，定义旧的Timer API宏
// 这些宏将旧的API调用转换为新的API调用

// 全局变量来存储timer配置
static hw_timer_t* _compat_timer = nullptr;
static bool _timer_alarm_enabled = false;
static uint64_t _timer_alarm_value = 0;

// timerAlarmEnable 兼容性宏
#define timerAlarmEnable(timer) \
  do { \
    _timer_alarm_enabled = true; \
    if (_timer_alarm_value > 0) { \
      timerAlarm(timer, _timer_alarm_value, false, 0); \
    } \
  } while(0)

// timerAlarmDisable 兼容性宏  
#define timerAlarmDisable(timer) \
  do { \
    _timer_alarm_enabled = false; \
    timerStop(timer); \
  } while(0)

// timerAlarmWrite 兼容性宏
#define timerAlarmWrite(timer, value, autoreload) \
  do { \
    _timer_alarm_value = value; \
    if (_timer_alarm_enabled) { \
      timerAlarm(timer, value, autoreload, 0); \
    } \
  } while(0)

// timerBegin 兼容性包装函数
inline hw_timer_t* timerBegin_compat(uint8_t timer_num, uint16_t divider, bool countUp) {
  // Arduino Core 3.x中，timerBegin只需要频率参数
  // 计算频率：80MHz / divider
  uint32_t frequency = 80000000 / divider;
  _compat_timer = timerBegin(frequency);
  return _compat_timer;
}

// 重定义timerBegin为兼容版本
#define timerBegin(timer_num, divider, countUp) timerBegin_compat(timer_num, divider, countUp)

// timerAttachInterrupt 兼容性包装函数
inline void timerAttachInterrupt_compat(hw_timer_t* timer, void (*fn)(void), bool edge) {
  // Arduino Core 3.x中，timerAttachInterrupt不需要edge参数
  timerAttachInterrupt(timer, fn);
}

// 重定义timerAttachInterrupt为兼容版本
#define timerAttachInterrupt(timer, fn, edge) timerAttachInterrupt_compat(timer, fn, edge)

// 添加一些可能需要的其他兼容性定义
#ifndef ONCE
#define ONCE false
#endif

#ifndef REPEAT  
#define REPEAT true
#endif

// 调试信息
#ifdef DEBUG_IRREMOTE_CORE3_FIX
#define CORE3_FIX_DEBUG(x) Serial.println("[IRremote Core3 Fix] " x)
#else
#define CORE3_FIX_DEBUG(x)
#endif

#endif // ESP_ARDUINO_VERSION >= 3.0.0

#endif // IRREMOTEESP8266_CORE3_FIX_H

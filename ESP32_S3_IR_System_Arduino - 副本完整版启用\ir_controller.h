/*
 * IR Controller Header - ESP32-S3 IR Control System
 * Fully compatible with IRremoteESP8266 2.8.6 API
 * Handles IR signal learning, transmission, and hardware management
 */

#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <ArduinoJson.h>
#include <vector>
#include <functional>

// Forward declaration
class DataManager;

// GPIO pin definitions for ESP32-S3
const uint16_t IR_SEND_PIN = 21;    // GPIO21 for IR transmitter
const uint16_t IR_RECV_PIN = 14;    // GPIO14 for IR receiver

// Learning States
enum class LearningState {
    IDLE,
    LEARNING,
    COMPLETED,
    TIMEOUT,
    ERROR
};

// Transmission States
enum class TransmissionState {
    IDLE,
    TRANSMITTING,
    COMPLETED,
    ERROR
};

class IRController {
public:
    // Constructor and Destructor
    IRController();
    ~IRController();
    
    // Core Methods
    bool initialize();
    void handleLoop();
    bool isHardwareReady() const;
    bool testHardware();
    
    // Learning Functions (Frontend API Matching)
    bool startLearning(const String& signalName);
    bool stopLearning();
    LearningState getLearningState() const { return m_learningState; }
    String getCurrentLearningSignal() const { return m_currentLearningSignalName; }
    unsigned long getLearningElapsed() const;
    unsigned long getLearningRemaining() const;
    
    // Signal Transmission (Frontend API Matching)
    bool sendSignal(const String& signalId);
    bool sendSignalByData(const String& protocol, const String& data, 
                         const String& rawData = "", int frequency = 38000);
    bool batchSendSignals(const std::vector<String>& signalIds, int delayMs = 500);
    
    // Status and Information
    TransmissionState getTransmissionState() const { return m_transmissionState; }
    bool isTransmitting() const { return m_isTransmitting; }
    DynamicJsonDocument getStatistics() const;
    DynamicJsonDocument getAllSignalsJSON();
    
    // Configuration
    void setLearningTimeout(unsigned long timeoutMs);
    void setTransmissionFrequency(int frequency);
    void setDataManager(DataManager* dm) { dataManager = dm; }
    
    // Event Callbacks (Frontend Integration)
    void setOnSignalLearned(std::function<void(const DynamicJsonDocument&)> callback);
    void setOnLearningTimeout(std::function<void(const String&)> callback);
    void setOnLearningError(std::function<void(const String&)> callback);
    void setOnSignalSent(std::function<void(const String&, bool)> callback);
    void setOnTransmissionError(std::function<void(const String&)> callback);

private:
    // Hardware Components
    IRsend* irSend;
    IRrecv* irRecv;
    decode_results results;
    
    // Data Manager Reference
    DataManager* dataManager;
    
    // Learning State
    LearningState m_learningState;
    String m_currentLearningSignalName;
    unsigned long m_learningStartTime;
    unsigned long m_learningTimeoutMs;

    // Transmission State
    TransmissionState m_transmissionState;
    bool m_isTransmitting;
    
    // Configuration
    int defaultFrequency;
    
    // Statistics
    unsigned long totalSignalsSent;
    unsigned long totalSignalsLearned;
    unsigned long totalErrors;
    
    // Constants
    static const unsigned long LEARNING_TIMEOUT = 30000;  // 30 seconds
    static const int MAX_RAW_LENGTH = 1024;
    
    // Event Callbacks
    std::function<void(const DynamicJsonDocument&)> onSignalLearned;
    std::function<void(const String&)> onLearningTimeout;
    std::function<void(const String&)> onLearningError;
    std::function<void(const String&, bool)> onSignalSent;
    std::function<void(const String&)> onTransmissionError;
    
    // Hardware Management
    bool initializeHardware();
    void cleanupHardware();
    
    // Learning Process
    bool processReceivedSignal();
    DynamicJsonDocument parseIRSignal(const decode_results& results);
    
    // Signal Transmission
    bool sendProtocolSignal(decode_type_t type, uint64_t value, uint16_t bits);
    bool sendRawSignal(const String& rawData, int frequency);
    bool parseSignalData(const String& protocol, const String& data, 
                        decode_type_t& type, uint64_t& value, uint16_t& bits);
    bool parseRawData(const String& rawData, uint16_t* rawArray, uint16_t& rawLen);
    
    // Helper Methods
    String uint64ToString(uint64_t value, uint8_t base = 10);
    bool isValidProtocol(const String& protocol);
    int getDefaultBitsForProtocol(decode_type_t type);
};

#endif // IR_CONTROLLER_H

/**
 * 红外控制器 - 头文件
 * 负责红外信号的学习、发射、硬件控制
 */

#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <ArduinoJson.h>

// 学习状态枚举
enum LearningState {
    LEARNING_IDLE,      // 空闲状态
    LEARNING_ACTIVE,    // 学习中
    LEARNING_SUCCESS,   // 学习成功
    LEARNING_TIMEOUT,   // 学习超时
    LEARNING_ERROR      // 学习错误
};

// 红外信号数据结构
struct IRSignalData {
    String protocol;        // 协议名称
    String signalCode;      // 信号码
    String frequency;       // 载波频率
    String rawData;         // 原始数据
    bool parseSuccess;      // 解析是否成功
    unsigned long timestamp; // 检测时间戳
    
    IRSignalData() : parseSuccess(false), timestamp(0) {}
    
    // 转换为JSON
    JsonObject toJson(JsonDocument& doc) const;
    
    // 从decode_results创建
    static IRSignalData fromDecodeResults(const decode_results& results);
    
    // 验证数据有效性
    bool isValid() const;
};

class IRController {
private:
    // 硬件对象
    IRsend* irSend;
    IRrecv* irRecv;
    
    // 硬件配置
    int sendPin;
    int recvPin;
    int statusLedPin;
    
    // 学习状态管理
    LearningState currentState;
    unsigned long learningStartTime;
    unsigned long learningTimeout;
    IRSignalData learnedSignal;
    String learningId;
    
    // 配置参数
    static const int DEFAULT_FREQUENCY = 38000;
    static const int DEFAULT_LEARNING_TIMEOUT = 10000; // 10秒
    static const int RECV_BUFFER_SIZE = 1024;
    static const int SEND_REPEAT_COUNT = 1;
    
    // 内部方法
    bool initSender();
    bool initReceiver();
    void resetLearningState();
    bool processReceivedSignal(const decode_results& results);
    String protocolToString(decode_type_t protocol);
    decode_type_t stringToProtocol(const String& protocolStr);
    bool sendNECSignal(uint32_t data, int frequency = DEFAULT_FREQUENCY);
    bool sendRC5Signal(uint32_t data, int frequency = DEFAULT_FREQUENCY);
    bool sendSONYSignal(uint32_t data, int frequency = DEFAULT_FREQUENCY);
    bool sendRawSignal(const String& rawData, int frequency = DEFAULT_FREQUENCY);
    
public:
    IRController(int sendPin, int recvPin, int statusLedPin = -1);
    ~IRController();
    
    // 初始化和配置
    bool init();
    void setLearningTimeout(unsigned long timeout) { learningTimeout = timeout; }
    
    // 学习功能
    bool startLearning(const String& learningId = "");
    bool stopLearning();
    bool isLearning() const { return currentState == LEARNING_ACTIVE; }
    LearningState getLearningState() const { return currentState; }
    IRSignalData getLearnedSignal() const { return learnedSignal; }
    String getLearningId() const { return learningId; }
    unsigned long getLearningElapsedTime() const;
    
    // 学习处理（在主循环中调用）
    void handleLearning();
    
    // 信号发射
    bool sendSignal(const String& protocol, const String& signalCode, 
                   const String& frequency = "38000");
    bool sendSignal(const IRSignalData& signalData);
    bool sendRawData(const String& rawData, int frequency = DEFAULT_FREQUENCY);
    
    // 硬件状态检测
    bool isTransmitterReady() const;
    bool isReceiverReady() const;
    bool isSending() const;
    
    // 硬件信息
    String getHardwareInfo() const;
    JsonObject getHardwareStatus() const;
    
    // 信号解析和验证
    static bool validateSignalCode(const String& code, const String& protocol);
    static bool validateFrequency(int frequency);
    static bool validateRawData(const String& rawData);
    
    // 调试和诊断
    void enableDebug(bool enable = true);
    void printLearningStatus() const;
    void printHardwareStatus() const;
    
    // 回调函数类型定义
    typedef std::function<void(const IRSignalData&)> LearningCallback;
    typedef std::function<void(const String&)> ErrorCallback;
    
    // 设置回调函数
    void setLearningCallback(LearningCallback callback) { onLearningSuccess = callback; }
    void setErrorCallback(ErrorCallback callback) { onLearningError = callback; }
    
private:
    // 回调函数
    LearningCallback onLearningSuccess;
    ErrorCallback onLearningError;
    
    // 调试标志
    bool debugEnabled;
    
    // 状态LED控制
    void setStatusLED(bool state);
    void blinkStatusLED(int times = 1, int delayMs = 100);
};

#endif // IR_CONTROLLER_H

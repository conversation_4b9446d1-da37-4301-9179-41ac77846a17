#include "NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
NetworkSecurity::NetworkSecurity()
    : m_initialized(false)
    , m_totalRequests(0)
    , m_blockedRequests(0)
    , m_rateLimitedRequests(0)
    , m_invalidTokenRequests(0)
    , m_corsViolations(0)
{
    Serial.println("🔒 NetworkSecurity created");
}

NetworkSecurity::~NetworkSecurity() {
    shutdown();
    Serial.println("🔒 NetworkSecurity destroyed");
}

// ==================== 系统生命周期 ====================
bool NetworkSecurity::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  NetworkSecurity already initialized");
        return true;
    }
    
    Serial.println("🔒 Initializing NetworkSecurity...");
    
    // 清理所有映射表
    m_sessions.clear();
    m_rateLimits.clear();
    m_blockedIPs.clear();
    
    m_initialized = true;
    Serial.println("✅ NetworkSecurity initialization completed");
    return true;
}

void NetworkSecurity::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔒 Shutting down NetworkSecurity...");
    
    // 清理所有数据
    m_sessions.clear();
    m_rateLimits.clear();
    m_blockedIPs.clear();
    
    m_initialized = false;
    Serial.println("✅ NetworkSecurity shutdown completed");
}

// ==================== 认证功能 ====================
bool NetworkSecurity::validateAPIKey(const String& apiKey) {
    if (!ENABLE_AUTHENTICATION) {
        return true;  // 如果未启用认证，总是返回true
    }
    
    if (apiKey.isEmpty()) {
        updateSecurityStats("invalid_api_key");
        return false;
    }
    
    // 验证API密钥
    bool isValid = (apiKey == API_KEY);
    
    if (!isValid) {
        updateSecurityStats("invalid_api_key");
        Serial.printf("🔒 Invalid API key: %s\n", apiKey.c_str());
    }
    
    return isValid;
}

String NetworkSecurity::generateSessionToken(const String& clientId) {
    String token = generateRandomToken(32);
    
    SessionInfo session;
    session.clientId = clientId;
    session.createdTime = millis();
    session.lastAccessTime = millis();
    session.isValid = true;
    
    m_sessions[token] = session;
    
    Serial.printf("🔒 Session token generated for client: %s\n", clientId.c_str());
    return token;
}

bool NetworkSecurity::validateSessionToken(const String& token) {
    if (!ENABLE_AUTHENTICATION) {
        return true;  // 如果未启用认证，总是返回true
    }
    
    auto it = m_sessions.find(token);
    if (it == m_sessions.end()) {
        updateSecurityStats("invalid_token");
        return false;
    }
    
    SessionInfo& session = it->second;
    
    // 检查会话是否过期
    if (isSessionExpired(session)) {
        m_sessions.erase(it);
        updateSecurityStats("expired_token");
        return false;
    }
    
    // 更新最后访问时间
    session.lastAccessTime = millis();
    return session.isValid;
}

bool NetworkSecurity::revokeSessionToken(const String& token) {
    auto it = m_sessions.find(token);
    if (it != m_sessions.end()) {
        m_sessions.erase(it);
        Serial.printf("🔒 Session token revoked: %s\n", token.c_str());
        return true;
    }
    return false;
}

int NetworkSecurity::cleanupExpiredTokens() {
    int cleanedCount = 0;
    
    auto it = m_sessions.begin();
    while (it != m_sessions.end()) {
        if (isSessionExpired(it->second)) {
            it = m_sessions.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
    
    if (cleanedCount > 0) {
        Serial.printf("🔒 Cleaned up %d expired tokens\n", cleanedCount);
    }
    
    return cleanedCount;
}

// ==================== 速率限制 ====================
bool NetworkSecurity::checkRateLimit(const String& clientIP, const String& endpoint) {
    if (!ENABLE_RATE_LIMITING) {
        return true;  // 如果未启用速率限制，总是返回true
    }
    
    String key = clientIP + ":" + endpoint;
    auto it = m_rateLimits.find(key);
    
    unsigned long currentTime = millis();
    
    if (it == m_rateLimits.end()) {
        // 首次请求，创建新的速率限制记录
        RateLimitInfo info;
        info.windowStart = currentTime;
        info.requestCount = 1;
        info.isBlocked = false;
        info.blockUntil = 0;
        
        m_rateLimits[key] = info;
        return true;
    }
    
    RateLimitInfo& info = it->second;
    
    // 检查是否仍在阻止期间
    if (info.isBlocked && currentTime < info.blockUntil) {
        updateSecurityStats("rate_limited");
        return false;
    }
    
    // 检查是否需要重置窗口
    if (shouldResetRateLimitWindow(info)) {
        info.windowStart = currentTime;
        info.requestCount = 1;
        info.isBlocked = false;
        return true;
    }
    
    // 增加请求计数
    info.requestCount++;
    
    // 检查是否超过限制
    if (info.requestCount > MAX_REQUESTS_PER_MINUTE) {
        info.isBlocked = true;
        info.blockUntil = currentTime + RATE_LIMIT_BLOCK_TIME;
        updateSecurityStats("rate_limited");
        
        Serial.printf("🔒 Rate limit exceeded for %s:%s\n", clientIP.c_str(), endpoint.c_str());
        return false;
    }
    
    return true;
}

void NetworkSecurity::recordRequest(const String& clientIP, const String& endpoint) {
    updateSecurityStats("request");
    
    // 定期清理过期数据
    static unsigned long lastCleanup = 0;
    if (millis() - lastCleanup > 300000) {  // 每5分钟清理一次
        performCleanup();
        lastCleanup = millis();
    }
}

// ==================== CORS处理 ====================
bool NetworkSecurity::validateCORSRequest(const String& origin, const String& method) {
    if (!ENABLE_CORS) {
        return true;
    }
    
    // 检查允许的来源
    if (String(CORS_ORIGIN) == "*" || origin == CORS_ORIGIN) {
        // 检查允许的方法
        String allowedMethods = CORS_METHODS;
        if (allowedMethods.indexOf(method) >= 0) {
            return true;
        }
    }
    
    updateSecurityStats("cors_violation");
    Serial.printf("🔒 CORS violation: origin=%s, method=%s\n", origin.c_str(), method.c_str());
    return false;
}

std::map<String, String> NetworkSecurity::getCORSHeaders(const String& origin) {
    std::map<String, String> headers;
    
    if (ENABLE_CORS) {
        headers["Access-Control-Allow-Origin"] = CORS_ORIGIN;
        headers["Access-Control-Allow-Methods"] = CORS_METHODS;
        headers["Access-Control-Allow-Headers"] = CORS_HEADERS;
        headers["Access-Control-Max-Age"] = "86400";  // 24小时
    }
    
    return headers;
}

// ==================== IP过滤 ====================
bool NetworkSecurity::isIPBlocked(const String& clientIP) {
    auto it = m_blockedIPs.find(clientIP);
    if (it == m_blockedIPs.end()) {
        return false;
    }
    
    const BlockedIPInfo& info = it->second;
    
    // 检查是否为永久阻止
    if (info.isPermanent) {
        return true;
    }
    
    // 检查临时阻止是否过期
    if (isIPBlockExpired(info)) {
        m_blockedIPs.erase(it);
        Serial.printf("🔒 IP block expired for: %s\n", clientIP.c_str());
        return false;
    }
    
    return true;
}

void NetworkSecurity::blockIP(const String& clientIP, const String& reason, unsigned long duration) {
    BlockedIPInfo info;
    info.reason = reason;
    info.blockedTime = millis();
    info.blockDuration = duration;
    info.isPermanent = (duration == 0);
    
    m_blockedIPs[clientIP] = info;
    
    Serial.printf("🔒 IP blocked: %s (reason: %s, duration: %s)\n", 
                 clientIP.c_str(), reason.c_str(), 
                 info.isPermanent ? "permanent" : String(duration).c_str());
}

bool NetworkSecurity::unblockIP(const String& clientIP) {
    auto it = m_blockedIPs.find(clientIP);
    if (it != m_blockedIPs.end()) {
        m_blockedIPs.erase(it);
        Serial.printf("🔒 IP unblocked: %s\n", clientIP.c_str());
        return true;
    }
    return false;
}

// ==================== 安全统计 ====================
DynamicJsonDocument NetworkSecurity::getSecurityStats() {
    DynamicJsonDocument stats(512);
    
    stats["total_requests"] = m_totalRequests;
    stats["blocked_requests"] = m_blockedRequests;
    stats["rate_limited_requests"] = m_rateLimitedRequests;
    stats["invalid_token_requests"] = m_invalidTokenRequests;
    stats["cors_violations"] = m_corsViolations;
    stats["active_sessions"] = m_sessions.size();
    stats["blocked_ips"] = m_blockedIPs.size();
    stats["rate_limit_entries"] = m_rateLimits.size();
    
    return stats;
}

void NetworkSecurity::resetSecurityStats() {
    m_totalRequests = 0;
    m_blockedRequests = 0;
    m_rateLimitedRequests = 0;
    m_invalidTokenRequests = 0;
    m_corsViolations = 0;
    
    Serial.println("🔒 Security statistics reset");
}

// ==================== 私有方法 ====================
String NetworkSecurity::generateRandomToken(int length) {
    const char charset[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    String token = "";
    
    for (int i = 0; i < length; i++) {
        token += charset[random(0, sizeof(charset) - 1)];
    }
    
    return token;
}

bool NetworkSecurity::isSessionExpired(const SessionInfo& session) {
    unsigned long currentTime = millis();
    return (currentTime - session.lastAccessTime) > SESSION_TIMEOUT;
}

bool NetworkSecurity::shouldResetRateLimitWindow(const RateLimitInfo& rateLimitInfo) {
    unsigned long currentTime = millis();
    return (currentTime - rateLimitInfo.windowStart) > RATE_LIMIT_WINDOW;
}

bool NetworkSecurity::isIPBlockExpired(const BlockedIPInfo& blockedInfo) {
    if (blockedInfo.isPermanent) {
        return false;
    }
    
    unsigned long currentTime = millis();
    return (currentTime - blockedInfo.blockedTime) > blockedInfo.blockDuration;
}

void NetworkSecurity::performCleanup() {
    // 清理过期会话
    cleanupExpiredTokens();
    
    // 清理过期的速率限制记录
    auto rateLimitIt = m_rateLimits.begin();
    while (rateLimitIt != m_rateLimits.end()) {
        if (shouldResetRateLimitWindow(rateLimitIt->second)) {
            rateLimitIt = m_rateLimits.erase(rateLimitIt);
        } else {
            ++rateLimitIt;
        }
    }
    
    // 清理过期的IP阻止
    auto blockedIPIt = m_blockedIPs.begin();
    while (blockedIPIt != m_blockedIPs.end()) {
        if (isIPBlockExpired(blockedIPIt->second)) {
            blockedIPIt = m_blockedIPs.erase(blockedIPIt);
        } else {
            ++blockedIPIt;
        }
    }
}

void NetworkSecurity::updateSecurityStats(const String& eventType) {
    if (eventType == "request") {
        m_totalRequests++;
    } else if (eventType == "blocked") {
        m_blockedRequests++;
    } else if (eventType == "rate_limited") {
        m_rateLimitedRequests++;
    } else if (eventType == "invalid_token" || eventType == "invalid_api_key" || eventType == "expired_token") {
        m_invalidTokenRequests++;
    } else if (eventType == "cors_violation") {
        m_corsViolations++;
    }
}

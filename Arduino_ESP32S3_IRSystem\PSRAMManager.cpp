#include "PSRAMManager.h"

// ==================== 静态成员初始化 ====================
bool PSRAMManager::s_initialized = false;
bool PSRAMManager::s_psramAvailable = false;
SystemMode PSRAMManager::s_systemMode = SystemMode::STANDARD;
String PSRAMManager::s_lastError = "";
unsigned long PSRAMManager::s_lastCheck = 0;

// ==================== 核心初始化方法 ====================
SystemMode PSRAMManager::initialize() {
    Serial.println("🔧 Initializing PSRAM Manager...");
    
    if (s_initialized) {
        Serial.println("⚠️  PSRAM Manager already initialized");
        return s_systemMode;
    }
    
    // 清除之前的错误状态
    clearError();
    
    // 第一步：检查硬件配置
    if (!checkHardwareConfiguration()) {
        setError("Hardware configuration check failed");
        logPSRAMStatus("Hardware configuration incompatible with PSRAM", true);
        s_systemMode = SystemMode::STANDARD;
        s_psramAvailable = false;
        s_initialized = true;
        return s_systemMode;
    }
    
    // 第二步：检测PSRAM硬件
    if (!psramFound()) {
        logPSRAMStatus("PSRAM hardware not detected - using Standard Mode");
        s_systemMode = SystemMode::STANDARD;
        s_psramAvailable = false;
        s_initialized = true;
        return s_systemMode;
    }
    
    Serial.printf("✅ PSRAM hardware detected: %d MB\n", ESP.getPsramSize() / (1024 * 1024));
    
    // 第三步：执行PSRAM功能测试
    if (!testPSRAMFunctionality()) {
        setError("PSRAM functionality test failed");
        logPSRAMStatus("PSRAM functionality test failed - falling back to Standard Mode", true);
        s_systemMode = SystemMode::STANDARD;
        s_psramAvailable = false;
        s_initialized = true;
        return s_systemMode;
    }
    
    // 第四步：PSRAM可用，设置高性能模式
    s_systemMode = SystemMode::HIGH_PERFORMANCE;
    s_psramAvailable = true;
    s_initialized = true;
    s_lastCheck = millis();
    
    logPSRAMStatus("PSRAM initialized successfully - High Performance Mode activated");
    printPSRAMStatus();
    
    return s_systemMode;
}

SystemMode PSRAMManager::detectSystemMode() {
    if (!psramFound()) {
        return SystemMode::STANDARD;
    }
    
    // 执行快速PSRAM测试
    void* testPtr = ps_malloc(1024);
    if (!testPtr) {
        return SystemMode::STANDARD;
    }
    
    // 简单的读写测试
    memset(testPtr, 0xAA, 1024);
    uint8_t* bytePtr = (uint8_t*)testPtr;
    bool testPassed = true;
    
    for (int i = 0; i < 1024; i++) {
        if (bytePtr[i] != 0xAA) {
            testPassed = false;
            break;
        }
    }
    
    free(testPtr);
    
    return testPassed ? SystemMode::HIGH_PERFORMANCE : SystemMode::STANDARD;
}

// ==================== PSRAM功能测试 ====================
bool PSRAMManager::testPSRAMFunctionality() {
    Serial.println("🧪 Testing PSRAM functionality...");
    
    // 基础测试
    if (!performBasicPSRAMTest()) {
        setError("Basic PSRAM test failed");
        return false;
    }
    
    // 读写测试
    if (!performPSRAMReadWriteTest(PSRAM_TEST_SIZE)) {
        setError("PSRAM read/write test failed");
        return false;
    }
    
    // 内存模式测试
    if (!testMemoryPatterns()) {
        setError("PSRAM memory pattern test failed");
        return false;
    }
    
    // 压力测试（可选）
    if (!performPSRAMStressTest()) {
        Serial.println("⚠️  PSRAM stress test failed, but basic functionality works");
        // 压力测试失败不影响基本功能
    }
    
    Serial.println("✅ PSRAM functionality test passed");
    return true;
}

// ==================== 基础PSRAM测试 ====================
bool PSRAMManager::performBasicPSRAMTest() {
    // 尝试分配小块内存
    void* testPtr = ps_malloc(1024);
    if (!testPtr) {
        setError("Failed to allocate test memory from PSRAM");
        return false;
    }
    
    // 写入测试数据
    memset(testPtr, 0xAA, 1024);
    
    // 验证数据
    uint8_t* bytePtr = (uint8_t*)testPtr;
    for (int i = 0; i < 1024; i++) {
        if (bytePtr[i] != 0xAA) {
            free(testPtr);
            setError("PSRAM data verification failed");
            return false;
        }
    }
    
    free(testPtr);
    return true;
}

// ==================== PSRAM读写测试 ====================
bool PSRAMManager::performPSRAMReadWriteTest(size_t size) {
    // 分配测试缓冲区
    uint8_t* testBuffer = (uint8_t*)ps_malloc(size);
    if (!testBuffer) {
        setError("Failed to allocate PSRAM test buffer");
        return false;
    }
    
    // 生成测试数据
    generateTestData(testBuffer, size);
    
    // 验证测试数据
    bool result = verifyTestData(testBuffer, size);
    
    free(testBuffer);
    
    if (!result) {
        setError("PSRAM read/write verification failed");
    }
    
    return result;
}

// ==================== 内存模式测试 ====================
bool PSRAMManager::testMemoryPatterns() {
    const size_t testSize = 4096;
    uint8_t* buffer = (uint8_t*)ps_malloc(testSize);
    if (!buffer) {
        return false;
    }
    
    // 测试不同的内存模式
    uint8_t patterns[] = {0x00, 0xFF, 0xAA, 0x55, 0xCC, 0x33};
    
    for (uint8_t pattern : patterns) {
        // 写入模式
        memset(buffer, pattern, testSize);
        
        // 验证模式
        for (size_t i = 0; i < testSize; i++) {
            if (buffer[i] != pattern) {
                free(buffer);
                setError("Memory pattern test failed");
                return false;
            }
        }
    }
    
    free(buffer);
    return true;
}

// ==================== PSRAM压力测试 ====================
bool PSRAMManager::performPSRAMStressTest() {
    const int numBlocks = 10;
    const size_t blockSize = 8192;
    void* blocks[numBlocks];
    
    // 分配多个内存块
    for (int i = 0; i < numBlocks; i++) {
        blocks[i] = ps_malloc(blockSize);
        if (!blocks[i]) {
            // 清理已分配的内存
            for (int j = 0; j < i; j++) {
                free(blocks[j]);
            }
            return false;
        }
        
        // 写入测试数据
        memset(blocks[i], i + 1, blockSize);
    }
    
    // 验证所有内存块
    bool result = true;
    for (int i = 0; i < numBlocks; i++) {
        uint8_t* bytePtr = (uint8_t*)blocks[i];
        for (size_t j = 0; j < blockSize; j++) {
            if (bytePtr[j] != (uint8_t)(i + 1)) {
                result = false;
                break;
            }
        }
        if (!result) break;
    }
    
    // 清理内存
    for (int i = 0; i < numBlocks; i++) {
        free(blocks[i]);
    }
    
    return result;
}

// ==================== 硬件配置检查 ====================
bool PSRAMManager::checkHardwareConfiguration() {
    // 检查ESP32-S3芯片
    if (strcmp(ESP.getChipModel(), "ESP32-S3") != 0) {
        setError("Not running on ESP32-S3 chip");
        return false;
    }
    
    // 检查Flash模式
    if (!checkFlashMode()) {
        return false;
    }
    
    // 检查分区表
    if (!checkPartitionTable()) {
        return false;
    }
    
    return true;
}

// ==================== 内存健康检查 ====================
bool PSRAMManager::checkMemoryHealth() {
    if (!s_psramAvailable) {
        return true;  // 标准模式下总是健康的
    }

    // 检查可用PSRAM
    size_t freePSRAM = getFreePSRAM();
    if (freePSRAM < MIN_FREE_PSRAM) {
        setError("Low PSRAM memory");
        return false;
    }

    // 检查内存碎片化
    float fragmentation = getMemoryFragmentation();
    if (fragmentation > 0.8f) {  // 80%碎片化阈值
        setError("High memory fragmentation");
        return false;
    }

    return true;
}

// ==================== 内存碎片化检查 ====================
float PSRAMManager::getMemoryFragmentation() {
    if (!s_psramAvailable) {
        return 0.0f;
    }

    // 尝试分配大块内存来检测碎片化
    size_t totalFree = getFreePSRAM();
    size_t largestBlock = 0;

    // 二分查找最大可分配块
    size_t low = 1024;
    size_t high = totalFree;

    while (low <= high) {
        size_t mid = (low + high) / 2;
        void* ptr = ps_malloc(mid);
        if (ptr) {
            free(ptr);
            largestBlock = mid;
            low = mid + 1;
        } else {
            high = mid - 1;
        }
    }

    // 计算碎片化程度
    return totalFree > 0 ? 1.0f - (float)largestBlock / totalFree : 0.0f;
}

// ==================== 内存清理 ====================
void PSRAMManager::performMemoryCleanup() {
    if (!s_psramAvailable) {
        return;
    }

    Serial.println("🧹 Performing memory cleanup...");

    // 强制垃圾回收（如果支持）
    // 注意：ESP32没有内置垃圾回收，这里主要是占位符

    Serial.println("✅ Memory cleanup completed");
}

// ==================== 状态打印 ====================
void PSRAMManager::printPSRAMStatus() {
    Serial.println("📊 PSRAM Status Report:");
    Serial.printf("   Mode: %s\n", getSystemModeString(s_systemMode));
    Serial.printf("   Available: %s\n", s_psramAvailable ? "Yes" : "No");

    if (s_psramAvailable) {
        Serial.printf("   Total Size: %d MB\n", ESP.getPsramSize() / (1024 * 1024));
        Serial.printf("   Free Size: %d KB\n", ESP.getFreePsram() / 1024);
        Serial.printf("   Usage: %.1f%%\n", getPSRAMUsage() * 100);
        Serial.printf("   Fragmentation: %.1f%%\n", getMemoryFragmentation() * 100);
    }

    if (!s_lastError.isEmpty()) {
        Serial.printf("   Last Error: %s\n", s_lastError.c_str());
    }
}

// ==================== 工具方法 ====================
const char* PSRAMManager::getSystemModeString(SystemMode mode) {
    switch (mode) {
        case SystemMode::HIGH_PERFORMANCE:
            return "High Performance (PSRAM)";
        case SystemMode::STANDARD:
            return "Standard (RAM Only)";
        default:
            return "Unknown";
    }
}

void PSRAMManager::generateTestData(uint8_t* buffer, size_t size) {
    for (size_t i = 0; i < size; i++) {
        buffer[i] = (uint8_t)(i & 0xFF);
    }
}

bool PSRAMManager::verifyTestData(const uint8_t* buffer, size_t size) {
    for (size_t i = 0; i < size; i++) {
        if (buffer[i] != (uint8_t)(i & 0xFF)) {
            return false;
        }
    }
    return true;
}

void PSRAMManager::setError(const String& error) {
    s_lastError = error;
    Serial.printf("❌ PSRAM Error: %s\n", error.c_str());
}

void PSRAMManager::logPSRAMStatus(const String& message, bool isError) {
    if (isError) {
        Serial.printf("❌ PSRAM: %s\n", message.c_str());
    } else {
        Serial.printf("✅ PSRAM: %s\n", message.c_str());
    }
}

// ==================== 重新初始化 ====================
bool PSRAMManager::reinitializePSRAM() {
    Serial.println("🔄 Reinitializing PSRAM...");

    s_initialized = false;
    s_psramAvailable = false;
    clearError();

    SystemMode newMode = initialize();

    if (newMode == SystemMode::HIGH_PERFORMANCE) {
        Serial.println("✅ PSRAM reinitialization successful");
        return true;
    } else {
        Serial.println("⚠️  PSRAM reinitialization failed, using Standard Mode");
        return false;
    }
}

// ==================== Flash模式检查 ====================
bool PSRAMManager::checkFlashMode() {
    // 获取Flash模式
    uint32_t flashMode = ESP.getFlashChipMode();
    
    // ESP32-S3 with OPI PSRAM requires QIO mode
    if (flashMode != FM_QIO) {
        setError("Flash mode must be QIO for OPI PSRAM");
        Serial.printf("⚠️  Current Flash mode: %d, Required: QIO (%d)\n", flashMode, FM_QIO);
        return false;
    }
    
    return true;
}

// ==================== 分区表检查 ====================
bool PSRAMManager::checkPartitionTable() {
    // 检查可用的Flash空间
    size_t flashSize = ESP.getFlashChipSize();
    if (flashSize < 8 * 1024 * 1024) {  // 至少8MB Flash
        setError("Insufficient Flash size for PSRAM operation");
        return false;
    }
    
    return true;
}

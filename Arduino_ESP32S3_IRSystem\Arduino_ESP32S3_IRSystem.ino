/**
 * ESP32-S3 红外控制系统 - Arduino IDE版本
 * 支持纯净前端的完整后端实现
 *
 * 功能特性：
 * - 18个API端点支持
 * - 8个WebSocket事件
 * - 红外学习和发射
 * - 文件系统数据管理
 * - PSRAM优化支持
 *
 * 库文件版本要求：
 * - ESP32 Arduino Core: 2.0.17 (重要：不要使用3.x版本)
 * - AsyncTCP: 1.1.1
 * - ESPAsyncWebServer: 1.2.3
 * - ArduinoJson: 6.21.3
 * - IRremoteESP8266: 2.8.6 (重要：与ESP32 Core 2.0.17兼容)
 * - TimeLib: 1.6.1 (注意：已替代Time.h，避免混用)
 */

// ==================== 核心系统库 ====================
#include <Arduino.h>
#include <WiFi.h>
#include <esp_wifi.h>
#include <esp_system.h>
#include <esp_heap_caps.h>

// ==================== 文件系统 ====================
#include <FS.h>
#include <SPIFFS.h>

// ==================== 网络通信 ====================
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <AsyncWebSocket.h>

// ==================== JSON处理 ====================
#include <ArduinoJson.h>

// ==================== 红外通信 ====================
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>

// ==================== 时间处理 ====================
#include <TimeLib.h>  // 替代 Time.h，避免混用

// ==================== 系统工具（可选） ====================
#include <Preferences.h>  // 用于存储系统设置
#include <Update.h>       // OTA 更新功能（可选）

// 系统配置
#include "system-config.h"
#include "hardware-config.h"

// 核心模块
#include "PSRAMManager.h"
#include "MemoryAllocator.h"
#include "DataManager.h"
#include "IRController.h"
#include "TaskManager.h"
#include "WebServerManager.h"
#include "WSManager.h"
#include "NetworkSecurity.h"
#include "SystemManager.h"

// 全局对象
SystemManager* g_systemManager = nullptr;

// 系统状态
bool g_systemInitialized = false;
unsigned long g_lastHeartbeat = 0;
const unsigned long HEARTBEAT_INTERVAL = 30000; // 30秒心跳

void setup() {
    Serial.begin(SERIAL_BAUD_RATE);
    Serial.println("\n=== ESP32-S3 红外控制系统启动 ===");
    Serial.printf("版本: %s\n", SYSTEM_VERSION);
    Serial.printf("编译时间: %s %s\n", __DATE__, __TIME__);
    
    // 初始化状态LED
    INIT_LED_PINS();
    SET_LED_STATE(STATUS_LED_PIN, LED_ON);
    
    // 1. 初始化PSRAM
    Serial.println("🔧 初始化PSRAM...");
    SystemMode systemMode = PSRAMManager::initialize();
    Serial.printf("✅ 系统模式: %s\n", PSRAMManager::getSystemModeString(systemMode));
    
    // 2. 初始化内存分配器
    Serial.println("🧠 初始化内存分配器...");
    if (!MemoryAllocator::initialize()) {
        Serial.println("❌ 内存分配器初始化失败");
        return;
    }
    
    // 3. 创建系统管理器
    Serial.println("🏗️ 创建系统管理器...");
    SystemCapacity capacity = getSystemCapacity(systemMode);
    g_systemManager = new SystemManager(capacity);
    
    if (!g_systemManager) {
        Serial.println("❌ 系统管理器创建失败");
        return;
    }
    
    // 4. 初始化系统
    Serial.println("🚀 初始化系统...");
    if (!g_systemManager->initialize()) {
        Serial.println("❌ 系统初始化失败");
        delete g_systemManager;
        g_systemManager = nullptr;
        return;
    }

    // 5. 启动系统服务
    Serial.println("🚀 启动系统服务...");
    if (!g_systemManager->start()) {
        Serial.println("❌ 系统服务启动失败");
        delete g_systemManager;
        g_systemManager = nullptr;
        return;
    }

    g_systemInitialized = true;
    g_lastHeartbeat = millis();
    
    SET_LED_STATE(STATUS_LED_PIN, LED_OFF);
    SET_LED_STATE(WIFI_LED_PIN, LED_BLINK_SLOW);
    
    Serial.println("✅ 系统启动完成");
    Serial.printf("📊 可用内存: %d KB\n", ESP.getFreeHeap() / 1024);
    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("📊 可用PSRAM: %d KB\n", PSRAMManager::getFreePSRAM() / 1024);
    }
    
    // 打印系统信息
    printSystemInfo();
}





/**
 * 执行心跳检测
 */
void performHeartbeat() {
    if (!g_systemManager) return;

    // 检查系统健康状态
    bool isHealthy = g_systemManager->isHealthy();

    // 更新LED状态
    if (isHealthy) {
        SET_LED_STATE(STATUS_LED_PIN, LED_BLINK_SLOW);
    } else {
        SET_LED_STATE(STATUS_LED_PIN, LED_BLINK_FAST);
    }

    // 发送心跳信息
    DynamicJsonDocument heartbeat(256);
    heartbeat["type"] = "heartbeat";
    heartbeat["timestamp"] = millis();
    heartbeat["healthy"] = isHealthy;
    heartbeat["uptime"] = millis() / 1000;
    heartbeat["free_heap"] = ESP.getFreeHeap();

    if (PSRAMManager::isPSRAMAvailable()) {
        heartbeat["free_psram"] = PSRAMManager::getFreePSRAM();
    }

    g_systemManager->broadcastMessage(heartbeat);

    Serial.printf("💓 心跳: %s (运行时间: %lu秒)\n",
                 isHealthy ? "健康" : "异常", millis() / 1000);
}

/**
 * 检查内存健康状态
 */
void checkMemoryHealth() {
    // 检查堆内存
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < MIN_FREE_HEAP) {
        Serial.printf("⚠️ 堆内存不足: %d KB\n", freeHeap / 1024);
        MemoryAllocator::performCleanup();
    }

    // 检查PSRAM
    if (PSRAMManager::isPSRAMAvailable()) {
        if (!PSRAMManager::checkMemoryHealth()) {
            Serial.println("⚠️ PSRAM健康检查失败");
            PSRAMManager::performMemoryCleanup();
        }
    }

    // 检查内存碎片化
    float fragmentation = MemoryAllocator::getFragmentationLevel();
    if (fragmentation > 0.8f) {
        Serial.printf("⚠️ 内存碎片化严重: %.1f%%\n", fragmentation * 100);
        MemoryAllocator::defragmentMemory();
    }
}

/**
 * 打印系统信息
 */
void printSystemInfo() {
    Serial.println("\n📋 系统信息:");
    Serial.printf("   芯片型号: %s\n", ESP.getChipModel());
    Serial.printf("   芯片版本: %d\n", ESP.getChipRevision());
    Serial.printf("   CPU频率: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("   Flash大小: %d MB\n", ESP.getFlashChipSize() / (1024 * 1024));
    Serial.printf("   Flash速度: %d MHz\n", ESP.getFlashChipSpeed() / 1000000);
    Serial.printf("   可用堆内存: %d KB\n", ESP.getFreeHeap() / 1024);
    Serial.printf("   最大分配块: %d KB\n", ESP.getMaxAllocHeap() / 1024);

    if (PSRAMManager::isPSRAMAvailable()) {
        Serial.printf("   PSRAM大小: %d MB\n", PSRAMManager::getPSRAMSize() / (1024 * 1024));
        Serial.printf("   可用PSRAM: %d KB\n", PSRAMManager::getFreePSRAM() / 1024);
    }

    Serial.printf("   MAC地址: %s\n", WiFi.macAddress().c_str());
    Serial.println();
}

/**
 * 系统重启
 */
void systemRestart() {
    Serial.println("🔄 系统重启中...");

    if (g_systemManager) {
        g_systemManager->shutdown();
        delete g_systemManager;
        g_systemManager = nullptr;
    }

    delay(1000);
    ESP.restart();
}

/**
 * 紧急停止
 */
void emergencyStop() {
    Serial.println("🛑 紧急停止");

    // 停止所有LED
    SET_LED_STATE(STATUS_LED_PIN, LED_OFF);
    SET_LED_STATE(WIFI_LED_PIN, LED_OFF);
    SET_LED_STATE(IR_LED_PIN, LED_OFF);

    // 停止系统
    if (g_systemManager) {
        g_systemManager->emergencyStop();
    }
}

/**
 * 看门狗喂狗
 */
void feedWatchdog() {
    // ESP32的看门狗会自动重置，这里是占位符
    yield();
}

/**
 * 错误处理
 */
void handleError(const String& error) {
    Serial.printf("❌ 错误: %s\n", error.c_str());

    // 设置错误LED状态
    SET_LED_STATE(STATUS_LED_PIN, LED_BLINK_FAST);

    // 发送错误消息
    if (g_systemManager) {
        DynamicJsonDocument errorMsg(256);
        errorMsg["type"] = "error";
        errorMsg["message"] = error;
        errorMsg["timestamp"] = millis();

        g_systemManager->broadcastMessage(errorMsg);
    }
}

void loop() {
    if (!g_systemInitialized || !g_systemManager) {
        delay(1000);
        return;
    }
    
    // 主循环处理
    g_systemManager->loop();
    
    // 心跳检测
    unsigned long currentTime = millis();
    if (currentTime - g_lastHeartbeat >= HEARTBEAT_INTERVAL) {
        performHeartbeat();
        g_lastHeartbeat = currentTime;
    }
    
    // 内存监控
    if (currentTime % 60000 == 0) { // 每分钟检查一次
        checkMemoryHealth();
    }
    
    // 让出CPU时间
    yield();
}
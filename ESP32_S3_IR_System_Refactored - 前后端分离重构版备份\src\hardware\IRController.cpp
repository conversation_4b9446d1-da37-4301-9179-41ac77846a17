#include "IRController.h"
#include "../data/DataManager.h"
#include "../core/MemoryAllocator.h"

// ==================== 构造函数和析构函数 ====================
IRController::IRController()
    : m_initialized(false)
    , m_isLearning(false)
    , m_isSending(false)
    , m_dataManager(nullptr)
    , m_transmitPin(IR_SEND_PIN)
    , m_receivePin(IR_RECV_PIN)
    , m_carrierFrequency(38000)
    , m_transmitPower(80)
    , m_receiveSensitivity(50)
    , m_learningStartTime(0)
    , m_learningTimeout(10000)
    , m_learnBuffer(nullptr)
    , m_learnBufferSize(0)
    , m_totalSent(0)
    , m_totalLearned(0)
    , m_sendErrors(0)
    , m_learnErrors(0)
{
    Serial.println("📡 IRController created");
}

IRController::~IRController() {
    shutdown();
    cleanupHardware();
    cleanupLearnBuffer();
    Serial.println("📡 IRController destroyed");
}

// ==================== 系统生命周期 ====================
bool IRController::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  IRController already initialized");
        return true;
    }
    
    Serial.println("📡 Initializing IRController...");
    
    // 1. 初始化硬件
    if (!initializeHardware()) {
        logError("Hardware initialization failed");
        return false;
    }
    
    // 2. 初始化学习缓冲区
    if (!initializeLearnBuffer()) {
        logError("Learn buffer initialization failed");
        return false;
    }
    
    // 3. 测试硬件功能
    if (!testHardware()) {
        Serial.println("⚠️  Hardware test failed, but continuing with limited functionality");
    }
    
    m_initialized = true;
    Serial.println("✅ IRController initialization completed");
    return true;
}

void IRController::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("📡 Shutting down IRController...");
    
    // 停止所有操作
    stopSending();
    stopLearning();
    
    m_initialized = false;
    Serial.println("✅ IRController shutdown completed");
}

bool IRController::isHealthy() const {
    return m_initialized && m_learnBuffer != nullptr;
}

void IRController::setDataManager(DataManager* dataManager) {
    m_dataManager = dataManager;
    Serial.println("📡 IRController: DataManager dependency set");
}

// ==================== 信号发送 ====================
bool IRController::sendSignal(const String& signalCode, const String& protocol) {
    if (!m_initialized) {
        logError("IRController not initialized");
        return false;
    }

    if (m_isLearning) {
        logError("Cannot send signal while learning");
        return false;
    }

    Serial.printf("📡 Sending signal: %s (protocol: %s)\n",
                 signalCode.c_str(), protocol.c_str());

    m_isSending = true;

    // 点亮发送状态LED
    digitalWrite(IR_LED_PIN, LED_ON);

    bool success = false;

    // 根据协议类型发送信号
    if (protocol == "NEC") {
        success = sendNECSignal(signalCode);
    } else if (protocol == "RC5") {
        success = sendRC5Signal(signalCode);
    } else if (protocol == "RC6") {
        success = sendRC6Signal(signalCode);
    } else if (protocol == "SONY") {
        success = sendSonySignal(signalCode);
    } else if (protocol == "RAW") {
        success = sendRawSignalFromCode(signalCode);
    } else {
        logError("Unsupported protocol: " + protocol);
        success = false;
    }

    // 关闭发送状态LED
    digitalWrite(IR_LED_PIN, LED_OFF);

    m_isSending = false;
    updateStatistics("send", success);

    if (success) {
        Serial.printf("📡 Signal sent successfully: %s\n", signalCode.c_str());
    } else {
        Serial.printf("❌ Failed to send signal: %s\n", signalCode.c_str());
    }

    return success;
}

bool IRController::sendRawSignal(const uint16_t* rawData, size_t dataLength, uint16_t frequency) {
    if (!m_initialized || !rawData || dataLength == 0) {
        logError("Invalid parameters for raw signal send");
        return false;
    }
    
    // 第一阶段：模拟发送
    Serial.printf("📡 Sending raw signal: %d samples at %d Hz - SIMULATED\n", 
                 dataLength, frequency);
    
    m_isSending = true;
    delay(100); // 模拟发送时间
    m_isSending = false;
    
    updateStatistics("send_raw", true);
    return true;
}

int IRController::batchSendSignals(const DynamicJsonDocument& signals, int interval) {
    if (!m_initialized) {
        logError("IRController not initialized");
        return 0;
    }
    
    int sentCount = 0;
    
    // 第一阶段：模拟批量发送
    if (signals.is<JsonArray>()) {
        JsonArray signalArray = signals.as<JsonArray>();
        for (JsonVariant signal : signalArray) {
            if (signal.containsKey("signalCode") && signal.containsKey("protocol")) {
                if (sendSignal(signal["signalCode"], signal["protocol"])) {
                    sentCount++;
                }
                if (interval > 0) {
                    delay(interval);
                }
            }
        }
    }
    
    Serial.printf("📡 Batch sent %d signals - SIMULATED\n", sentCount);
    return sentCount;
}

void IRController::stopSending() {
    if (m_isSending) {
        m_isSending = false;
        Serial.println("📡 Signal sending stopped");
    }
}

// ==================== 信号学习 ====================
bool IRController::startLearning(unsigned long timeout) {
    if (!m_initialized) {
        logError("IRController not initialized");
        return false;
    }

    if (m_isSending) {
        logError("Cannot start learning while sending");
        return false;
    }

    Serial.printf("📡 Starting learning mode (timeout: %lu ms)\n", timeout);

    m_isLearning = true;
    m_learningStartTime = millis();
    m_learningTimeout = timeout;

    // 清除学习缓冲区
    clearLearnBuffer();

    // 点亮学习状态LED
    digitalWrite(IR_LED_PIN, LED_ON);

    // 设置接收引脚为中断模式
    attachInterrupt(digitalPinToInterrupt(m_receivePin),
                   []() { /* 中断处理函数 - 第三阶段简化 */ },
                   CHANGE);

    Serial.println("📡 Learning mode active - waiting for IR signal...");
    Serial.println("📡 Point your remote at the receiver and press a button");

    return true;
}

void IRController::stopLearning() {
    if (m_isLearning) {
        m_isLearning = false;

        // 关闭学习状态LED
        digitalWrite(IR_LED_PIN, LED_OFF);

        // 分离中断
        detachInterrupt(digitalPinToInterrupt(m_receivePin));

        Serial.println("📡 Learning mode stopped");

        // 学习结束指示：闪烁LED
        for (int i = 0; i < 2; i++) {
            digitalWrite(STATUS_LED_PIN, LED_ON);
            delay(200);
            digitalWrite(STATUS_LED_PIN, LED_OFF);
            delay(200);
        }
    }
}

bool IRController::isLearning() const {
    if (!m_isLearning) {
        return false;
    }
    
    // 检查超时
    if (millis() - m_learningStartTime > m_learningTimeout) {
        const_cast<IRController*>(this)->stopLearning();
        return false;
    }
    
    return true;
}

DynamicJsonDocument IRController::getLearnedSignal() {
    DynamicJsonDocument signal(512);
    
    if (!m_isLearning && m_learnBuffer) {
        // 第一阶段：返回模拟学习到的信号
        signal["signalCode"] = "0x20DF10EF"; // 模拟信号代码
        signal["protocol"] = "NEC";
        signal["frequency"] = m_carrierFrequency;
        signal["learned_at"] = millis();
        signal["success"] = true;
        
        updateStatistics("learn", true);
        Serial.println("📡 Learned signal retrieved - SIMULATED");
    } else {
        signal["success"] = false;
        signal["error"] = "No signal learned or still learning";
    }
    
    return signal;
}

void IRController::clearLearnBuffer() {
    if (m_learnBuffer) {
        memset(m_learnBuffer, 0, m_learnBufferSize);
    }
}

// ==================== 信号分析 ====================
String IRController::analyzeProtocol(const String& signalCode) {
    // 第一阶段：简单的协议检测
    if (signalCode.startsWith("0x") && signalCode.length() == 10) {
        return "NEC";
    } else if (signalCode.startsWith("RC5:")) {
        return "RC5";
    } else if (signalCode.startsWith("RC6:")) {
        return "RC6";
    } else {
        return "RAW";
    }
}

bool IRController::validateSignalFormat(const String& signalCode, const String& protocol) {
    // 第一阶段：基本格式验证
    if (protocol == "NEC") {
        return signalCode.startsWith("0x") && signalCode.length() == 10;
    } else if (protocol == "RC5" || protocol == "RC6") {
        return signalCode.indexOf(":") > 0;
    } else if (protocol == "RAW") {
        return signalCode.length() > 0;
    }
    
    return false;
}

String IRController::convertSignalFormat(const String& signalCode, const String& fromProtocol, const String& toProtocol) {
    // 第一阶段：基本转换（主要是格式标准化）
    if (fromProtocol == toProtocol) {
        return signalCode;
    }
    
    // 简单的转换逻辑
    if (toProtocol == "RAW") {
        return "RAW:" + signalCode;
    }
    
    return signalCode; // 第一阶段返回原始代码
}

// ==================== 硬件状态 ====================
DynamicJsonDocument IRController::getHardwareStatus() {
    DynamicJsonDocument status(512);
    
    status["initialized"] = m_initialized;
    status["transmit_pin"] = m_transmitPin;
    status["receive_pin"] = m_receivePin;
    status["carrier_frequency"] = m_carrierFrequency;
    status["transmit_power"] = m_transmitPower;
    status["receive_sensitivity"] = m_receiveSensitivity;
    status["is_learning"] = m_isLearning;
    status["is_sending"] = m_isSending;
    status["hardware_healthy"] = isHealthy();
    
    return status;
}

bool IRController::testHardware() {
    Serial.println("📡 Testing hardware...");

    bool allTestsPassed = true;

    // 1. 测试引脚配置
    if (m_transmitPin < 0 || m_receivePin < 0) {
        logError("Invalid pin configuration");
        allTestsPassed = false;
    }

    // 2. 测试PWM输出
    Serial.println("📡 Testing PWM output...");
    startCarrier();
    delay(100);
    stopCarrier();

    // 3. 测试LED指示灯
    Serial.println("📡 Testing LED indicators...");
    digitalWrite(STATUS_LED_PIN, LED_ON);
    delay(200);
    digitalWrite(STATUS_LED_PIN, LED_OFF);

    digitalWrite(IR_LED_PIN, LED_ON);
    delay(200);
    digitalWrite(IR_LED_PIN, LED_OFF);

    // 4. 测试按键输入
    Serial.println("📡 Testing button input...");
    bool buttonState = digitalRead(LEARN_BUTTON_PIN);
    Serial.printf("📡 Learn button state: %s\n", buttonState ? "Released" : "Pressed");

    // 5. 测试载波频率设置
    Serial.println("📡 Testing frequency settings...");
    uint16_t originalFreq = m_carrierFrequency;
    setCarrierFrequency(36000);
    if (m_carrierFrequency != 36000) {
        logError("Frequency setting test failed");
        allTestsPassed = false;
    }
    setCarrierFrequency(originalFreq);  // 恢复原始频率

    // 6. 测试功率设置
    Serial.println("📡 Testing power settings...");
    int originalPower = m_transmitPower;
    setTransmitPower(50);
    if (m_transmitPower != 50) {
        logError("Power setting test failed");
        allTestsPassed = false;
    }
    setTransmitPower(originalPower);  // 恢复原始功率

    if (allTestsPassed) {
        Serial.println("✅ All hardware tests passed");
        // 成功指示：快速闪烁状态LED
        for (int i = 0; i < 3; i++) {
            digitalWrite(STATUS_LED_PIN, LED_ON);
            delay(100);
            digitalWrite(STATUS_LED_PIN, LED_OFF);
            delay(100);
        }
    } else {
        Serial.println("❌ Some hardware tests failed");
        // 失败指示：慢速闪烁状态LED
        for (int i = 0; i < 3; i++) {
            digitalWrite(STATUS_LED_PIN, LED_ON);
            delay(500);
            digitalWrite(STATUS_LED_PIN, LED_OFF);
            delay(500);
        }
    }

    return allTestsPassed;
}

bool IRController::calibrateTransmitter() {
    // 第一阶段：模拟校准
    Serial.println("📡 Calibrating transmitter - SIMULATED");
    delay(200);
    return true;
}

bool IRController::calibrateReceiver() {
    // 第一阶段：模拟校准
    Serial.println("📡 Calibrating receiver - SIMULATED");
    delay(200);
    return true;
}

// ==================== 配置管理 ====================
void IRController::setTransmitPower(int power) {
    m_transmitPower = constrain(power, 0, 100);
    Serial.printf("📡 Transmit power set to: %d%%\n", m_transmitPower);
}

void IRController::setReceiveSensitivity(int sensitivity) {
    m_receiveSensitivity = constrain(sensitivity, 0, 100);
    Serial.printf("📡 Receive sensitivity set to: %d%%\n", m_receiveSensitivity);
}

void IRController::setCarrierFrequency(uint16_t frequency) {
    m_carrierFrequency = frequency;
    Serial.printf("📡 Carrier frequency set to: %d Hz\n", m_carrierFrequency);
}

DynamicJsonDocument IRController::getCurrentConfig() {
    DynamicJsonDocument config(256);
    
    config["transmit_pin"] = m_transmitPin;
    config["receive_pin"] = m_receivePin;
    config["carrier_frequency"] = m_carrierFrequency;
    config["transmit_power"] = m_transmitPower;
    config["receive_sensitivity"] = m_receiveSensitivity;
    
    return config;
}

// ==================== 统计信息 ====================
DynamicJsonDocument IRController::getSendStatistics() {
    DynamicJsonDocument stats(256);
    
    stats["total_sent"] = m_totalSent;
    stats["send_errors"] = m_sendErrors;
    stats["success_rate"] = m_totalSent > 0 ? (float)(m_totalSent - m_sendErrors) / m_totalSent * 100 : 0;
    
    return stats;
}

DynamicJsonDocument IRController::getLearnStatistics() {
    DynamicJsonDocument stats(256);
    
    stats["total_learned"] = m_totalLearned;
    stats["learn_errors"] = m_learnErrors;
    stats["success_rate"] = m_totalLearned > 0 ? (float)(m_totalLearned - m_learnErrors) / m_totalLearned * 100 : 0;
    
    return stats;
}

void IRController::resetStatistics() {
    m_totalSent = 0;
    m_totalLearned = 0;
    m_sendErrors = 0;
    m_learnErrors = 0;
    Serial.println("📡 Statistics reset");
}

// ==================== 私有方法 ====================
bool IRController::initializeHardware() {
    Serial.println("📡 Initializing IR hardware...");

    // 1. 初始化发射引脚
    pinMode(m_transmitPin, OUTPUT);
    digitalWrite(m_transmitPin, LOW);

    // 2. 初始化接收引脚
    pinMode(m_receivePin, INPUT);

    // 3. 初始化状态LED引脚
    pinMode(STATUS_LED_PIN, OUTPUT);
    pinMode(IR_LED_PIN, OUTPUT);
    digitalWrite(STATUS_LED_PIN, LED_OFF);
    digitalWrite(IR_LED_PIN, LED_OFF);

    // 4. 设置PWM参数
    setupPWM(m_carrierFrequency, IR_SEND_DUTY_CYCLE);

    // 5. 初始化按键引脚（如果需要）
    pinMode(LEARN_BUTTON_PIN, INPUT_PULLUP);

    Serial.printf("📡 Hardware initialized: TX=%d, RX=%d, Freq=%d Hz\n",
                 m_transmitPin, m_receivePin, m_carrierFrequency);

    // 点亮状态LED表示初始化完成
    digitalWrite(STATUS_LED_PIN, LED_ON);
    delay(500);
    digitalWrite(STATUS_LED_PIN, LED_OFF);

    return true;
}

void IRController::cleanupHardware() {
    // 第一阶段：基本清理
    if (m_transmitPin >= 0) {
        digitalWrite(m_transmitPin, LOW);
    }
}

bool IRController::initializeLearnBuffer() {
    // 分配学习缓冲区
    m_learnBufferSize = 1024; // 1KB缓冲区
    m_learnBuffer = MemoryAllocator::smartAlloc(m_learnBufferSize);
    
    if (!m_learnBuffer) {
        logError("Failed to allocate learn buffer");
        return false;
    }
    
    memset(m_learnBuffer, 0, m_learnBufferSize);
    Serial.printf("📡 Learn buffer allocated: %d bytes\n", m_learnBufferSize);
    return true;
}

void IRController::cleanupLearnBuffer() {
    if (m_learnBuffer) {
        MemoryAllocator::smartFree(m_learnBuffer);
        m_learnBuffer = nullptr;
        m_learnBufferSize = 0;
    }
}

void IRController::processReceivedSignal(const uint16_t* rawData, size_t dataLength) {
    // 第一阶段：基本信号处理占位符
    Serial.printf("📡 Processing received signal: %d samples\n", dataLength);
}

size_t IRController::encodeSignalData(const String& signalCode, const String& protocol, 
                                     uint16_t* encodedData, size_t maxLength) {
    // 第一阶段：基本编码占位符
    if (!encodedData || maxLength == 0) {
        return 0;
    }
    
    // 简单的模拟编码
    size_t length = min(signalCode.length() / 2, maxLength);
    for (size_t i = 0; i < length; i++) {
        encodedData[i] = 500 + (i * 10); // 模拟时序数据
    }
    
    return length;
}

String IRController::decodeSignalData(const uint16_t* rawData, size_t dataLength, const String& protocol) {
    // 第一阶段：基本解码占位符
    if (!rawData || dataLength == 0) {
        return "";
    }
    
    // 简单的模拟解码
    return "0x" + String(rawData[0], HEX) + String(rawData[dataLength-1], HEX);
}

void IRController::logError(const String& error) {
    Serial.printf("❌ IRController Error: %s\n", error.c_str());
}

void IRController::updateStatistics(const String& type, bool success) {
    if (type == "send" || type == "send_raw") {
        m_totalSent++;
        if (!success) {
            m_sendErrors++;
        }
    } else if (type == "learn") {
        m_totalLearned++;
        if (!success) {
            m_learnErrors++;
        }
    }
}

// ==================== 协议特定发送方法实现 ====================
bool IRController::sendNECSignal(const String& signalCode) {
    // 解析NEC信号代码 (格式: 0x20DF10EF)
    if (!signalCode.startsWith("0x") || signalCode.length() != 10) {
        logError("Invalid NEC signal format: " + signalCode);
        return false;
    }

    // 提取32位数据
    uint32_t data = strtoul(signalCode.c_str() + 2, nullptr, 16);

    Serial.printf("📡 Sending NEC signal: 0x%08X\n", data);

    // NEC协议发送序列
    // 1. 发送头部 (9ms mark + 4.5ms space)
    sendCarrierPulse(9000);
    sendSpace(4500);

    // 2. 发送32位数据 (LSB first)
    for (int i = 0; i < 32; i++) {
        sendCarrierPulse(560);  // 位标记

        if (data & (1UL << i)) {
            sendSpace(1690);    // 逻辑1: 1.69ms space
        } else {
            sendSpace(560);     // 逻辑0: 560μs space
        }
    }

    // 3. 发送结束标记
    sendCarrierPulse(560);

    return true;
}

bool IRController::sendRC5Signal(const String& signalCode) {
    // RC5协议实现 (简化版本)
    Serial.printf("📡 Sending RC5 signal: %s\n", signalCode.c_str());

    // RC5协议使用曼彻斯特编码
    // 第三阶段简化实现：发送基本脉冲序列
    for (int i = 0; i < 14; i++) {
        sendCarrierPulse(889);  // RC5 T1时间单位
        sendSpace(889);
    }

    return true;
}

bool IRController::sendRC6Signal(const String& signalCode) {
    // RC6协议实现 (简化版本)
    Serial.printf("📡 Sending RC6 signal: %s\n", signalCode.c_str());

    // RC6头部
    sendCarrierPulse(2666);
    sendSpace(889);

    // 简化的数据发送
    for (int i = 0; i < 16; i++) {
        sendCarrierPulse(444);
        sendSpace(444);
    }

    return true;
}

bool IRController::sendSonySignal(const String& signalCode) {
    // Sony协议实现 (简化版本)
    Serial.printf("📡 Sending Sony signal: %s\n", signalCode.c_str());

    // Sony头部
    sendCarrierPulse(2400);
    sendSpace(600);

    // 简化的数据发送 (12位)
    for (int i = 0; i < 12; i++) {
        if (i % 2 == 0) {
            sendCarrierPulse(1200);  // 逻辑1
        } else {
            sendCarrierPulse(600);   // 逻辑0
        }
        sendSpace(600);
    }

    return true;
}

bool IRController::sendRawSignalFromCode(const String& signalCode) {
    // 解析原始信号代码 (格式: "RAW:500,1000,500,1500,...")
    if (!signalCode.startsWith("RAW:")) {
        logError("Invalid RAW signal format: " + signalCode);
        return false;
    }

    String data = signalCode.substring(4);  // 移除"RAW:"前缀

    Serial.printf("📡 Sending RAW signal: %s\n", data.c_str());

    // 解析时序数据
    int startIndex = 0;
    bool isCarrier = true;  // 交替发送载波和空白

    while (startIndex < data.length()) {
        int commaIndex = data.indexOf(',', startIndex);
        if (commaIndex == -1) {
            commaIndex = data.length();
        }

        String durationStr = data.substring(startIndex, commaIndex);
        unsigned int duration = durationStr.toInt();

        if (duration > 0) {
            if (isCarrier) {
                sendCarrierPulse(duration);
            } else {
                sendSpace(duration);
            }
        }

        isCarrier = !isCarrier;
        startIndex = commaIndex + 1;
    }

    return true;
}

// ==================== 底层硬件控制实现 ====================
void IRController::sendCarrierPulse(unsigned int duration) {
    if (duration == 0) return;

    startCarrier();
    delayMicroseconds(duration);
    stopCarrier();
}

void IRController::sendSpace(unsigned int duration) {
    if (duration == 0) return;

    stopCarrier();
    delayMicroseconds(duration);
}

void IRController::startCarrier() {
    // 使用ESP32的ledcWrite来生成PWM载波
    ledcWrite(IR_SEND_CHANNEL, POWER_TO_PWM_DUTY(m_transmitPower));
}

void IRController::stopCarrier() {
    // 停止PWM输出
    ledcWrite(IR_SEND_CHANNEL, 0);
}

void IRController::setupPWM(uint32_t frequency, uint8_t dutyCycle) {
    // 配置PWM通道
    ledcSetup(IR_SEND_CHANNEL, frequency, IR_SEND_RESOLUTION);
    ledcAttachPin(m_transmitPin, IR_SEND_CHANNEL);

    m_carrierFrequency = frequency;

    Serial.printf("📡 PWM configured: %d Hz, %d%% duty cycle\n", frequency, dutyCycle);
}

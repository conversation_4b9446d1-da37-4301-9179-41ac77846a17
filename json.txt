{"build": {"core": "esp32", "extra_flags": "-DARDUINO_ESP32S3_DEV -DARDUI<PERSON><PERSON>_ARCH_ESP32 -DBOARD_HAS_PSRAM -DCONFIG_SPIRAM_SUPPORT=1", "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "psram_type": "opi", "flash_size": "16777216", "hwids": [["0x303A", "0x1001"]], "mcu": "esp32s3", "variant": "esp32s3"}, "connectivity": ["wifi", "bluetooth"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "ESP32-S3 WROOM Custom OPI", "url": "https://www.espressif.com/en/products/modules/esp32-s3", "upload": {"maximum_size": 16777216, "maximum_ram_size": 8388608, "protocol": "esptool", "protocols": ["esptool", "esp-prog", "iot-bus-jtag", "jlink", "minimodule", "olimex-arm-usb-tiny-h", "olimex-arm-usb-ocd-h", "olimex-arm-usb-ocd", "olimex-jtag-tiny", "tumpa"]}, "vendor": "E<PERSON>ress<PERSON>"}
/*
 * WiFi Manager Header - ESP32-S3 IR Control System
 * Handles WiFi connection, AP mode, and network configuration
 * Compatible with ESP32-S3 hardware and latest WiFi libraries
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <functional>

class WiFiManager {
public:
    // Constructor and Destructor
    WiFiManager();
    ~WiFiManager();
    
    // Core Methods
    bool initialize();
    void handleLoop();
    bool startConnection();
    void disconnect();
    void reconnect();
    
    // WiFi Configuration
    bool setWiFiCredentials(const String& ssid, const String& password);
    bool setAPCredentials(const String& ssid, const String& password);
    
    // Connection Management
    bool connectToWiFi();
    bool startAPMode();
    void stopAPMode();
    
    // Status Queries
    bool isWiFiConnected() const { return isConnected; }
    bool isInAPMode() const { return isAPMode; }
    String getLocalIP() const;
    String getAPIP() const;
    String getSSID() const;
    int getRSSI() const;
    
    // Network Information
    DynamicJsonDocument getNetworkInfo() const;
    DynamicJsonDocument getWiFiStatus() const;
    DynamicJsonDocument scanNetworks();
    
    // Configuration Management
    void loadWiFiConfig();
    void saveWiFiConfig();
    void resetWiFiSettings();
    
    // Event Callbacks
    void setOnConnected(std::function<void()> callback) { onConnected = callback; }
    void setOnDisconnected(std::function<void()> callback) { onDisconnected = callback; }
    void setOnAPStarted(std::function<void()> callback) { onAPStarted = callback; }

private:
    // Connection State
    bool isConnected;
    bool isAPMode;
    unsigned long lastConnectionAttempt;
    int connectionAttempts;
    
    // Configuration
    String ssid;
    String password;
    String apSSID;
    String apPassword;
    
    // Constants
    static const unsigned long CONNECTION_TIMEOUT = 10000;  // 10 seconds
    static const unsigned long RETRY_INTERVAL = 30000;     // 30 seconds
    static const int MAX_CONNECTION_ATTEMPTS = 3;
    
    // Event Callbacks
    std::function<void()> onConnected;
    std::function<void()> onDisconnected;
    std::function<void()> onAPStarted;
    
    // Helper Methods
    void handleWiFiEvent();
    bool validateCredentials(const String& ssid, const String& password);
};

#endif // WIFI_MANAGER_H

#include "DataManager.h"

// File path constants
const char* DataManager::SIGNALS_FILE = "/data/signals.json";
const char* DataManager::TIMERS_FILE = "/data/timers.json";
const char* DataManager::TASKS_FILE = "/data/tasks.json";
const char* DataManager::CONFIG_FILE = "/data/config.json";

DataManager::DataManager() : m_initialized(false) {
    // Constructor does NO memory allocation
    Serial.println("DataManager created (no memory allocated)");
}

DataManager::~DataManager() {
    // Smart pointers automatically clean up
    Serial.println("DataManager destroyed");
}

bool DataManager::initialize() {
    Serial.println("📋 Initializing DataManager...");
    
    if (m_initialized) {
        Serial.println("DataManager already initialized");
        return true;
    }
    
    // Create storage containers after PSRAM is available
    if (!initializeStorage()) {
        Serial.println("❌ Failed to initialize storage");
        return false;
    }
    
    // Create data directories
    if (!createDataDirectories()) {
        Serial.println("❌ Failed to create data directories");
        return false;
    }
    
    // Load existing data
    if (!loadAllData()) {
        Serial.println("⚠️  Warning: Failed to load some data files");
        // Continue anyway - files might not exist yet
    }
    
    m_initialized = true;
    Serial.printf("✅ DataManager initialized - Signals: %d, Timers: %d, Tasks: %d\n", 
                 getSignalCount(), getTimerCount(), getTaskCount());
    
    return true;
}

bool DataManager::initializeStorage() {
    Serial.println("🗄️  Creating storage containers...");
    
    try {
        m_signals = std::make_unique<std::vector<SignalData>>();
        m_timers = std::make_unique<std::vector<TimerData>>();
        m_tasks = std::make_unique<std::vector<TaskData>>();
        m_config = std::make_unique<std::map<String, String>>();
        m_preferences = std::make_unique<Preferences>();
        
        if (!m_signals || !m_timers || !m_tasks || !m_config || !m_preferences) {
            Serial.println("❌ Failed to create storage containers");
            return false;
        }
        
        Serial.println("✅ Storage containers created successfully");
        return true;
        
    } catch (const std::exception& e) {
        Serial.printf("❌ Exception creating storage: %s\n", e.what());
        return false;
    }
}

bool DataManager::createDataDirectories() {
    if (!SPIFFS.exists("/data")) {
        if (!SPIFFS.mkdir("/data")) {
            Serial.println("❌ Failed to create /data directory");
            return false;
        }
        Serial.println("✅ Created /data directory");
    }
    return true;
}

// ==================== Signal Management ====================

bool DataManager::addSignal(const SignalData& signal) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    if (!validateSignalData(signal)) {
        return false;
    }
    
    // Check for duplicate ID
    if (findSignalIndex(signal.id) >= 0) {
        Serial.println("❌ Signal ID already exists");
        return false;
    }
    
    SignalData newSignal = signal;
    if (newSignal.id.isEmpty()) {
        newSignal.id = generateUniqueId("signal");
    }
    if (newSignal.created == 0) {
        newSignal.created = millis();
    }
    
    m_signals->push_back(newSignal);
    saveSignalsToFile();
    
    Serial.printf("✅ Signal added: %s\n", newSignal.name.c_str());
    return true;
}

bool DataManager::deleteSignal(const String& id) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.println("❌ Signal not found");
        return false;
    }
    
    m_signals->erase(m_signals->begin() + index);
    saveSignalsToFile();
    
    Serial.printf("✅ Signal deleted: %s\n", id.c_str());
    return true;
}

bool DataManager::updateSignal(const String& id, const SignalData& signal) {
    if (!m_initialized || !m_signals) {
        return false;
    }
    
    int index = findSignalIndex(id);
    if (index < 0) {
        Serial.println("❌ Signal not found");
        return false;
    }
    
    if (!validateSignalData(signal)) {
        return false;
    }
    
    (*m_signals)[index] = signal;
    (*m_signals)[index].id = id; // Preserve original ID
    saveSignalsToFile();
    
    Serial.printf("✅ Signal updated: %s\n", id.c_str());
    return true;
}

SignalData* DataManager::getSignal(const String& id) {
    if (!m_initialized || !m_signals) {
        return nullptr;
    }
    
    int index = findSignalIndex(id);
    if (index >= 0) {
        return &(*m_signals)[index];
    }
    
    return nullptr;
}

DynamicJsonDocument DataManager::getSignalsJSON() const {
    DynamicJsonDocument doc(8192);
    doc["success"] = true;
    
    JsonArray signalsArray = doc.createNestedArray("data");
    
    if (m_initialized && m_signals) {
        for (const auto& signal : *m_signals) {
            if (signal.isActive) {
                signalsArray.add(signal.toJson());
            }
        }
    }
    
    doc["count"] = signalsArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

int DataManager::getSignalCount() const {
    return (m_initialized && m_signals) ? m_signals->size() : 0;
}

void DataManager::clearAllSignals() {
    if (m_initialized && m_signals) {
        m_signals->clear();
        saveSignalsToFile();
        Serial.println("✅ All signals cleared");
    }
}

// ==================== Timer Management ====================

bool DataManager::addTimer(const TimerData& timer) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    if (!validateTimerData(timer)) {
        return false;
    }
    
    TimerData newTimer = timer;
    if (newTimer.id.isEmpty()) {
        newTimer.id = generateUniqueId("timer");
    }
    newTimer.timestamp = millis();
    
    m_timers->push_back(newTimer);
    saveTimersToFile();
    
    Serial.printf("✅ Timer added: %s\n", newTimer.name.c_str());
    return true;
}

bool DataManager::deleteTimer(const String& id) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    int index = findTimerIndex(id);
    if (index < 0) {
        return false;
    }
    
    m_timers->erase(m_timers->begin() + index);
    saveTimersToFile();
    
    return true;
}

bool DataManager::updateTimer(const String& id, const TimerData& timer) {
    if (!m_initialized || !m_timers) {
        return false;
    }
    
    int index = findTimerIndex(id);
    if (index < 0) {
        return false;
    }
    
    (*m_timers)[index] = timer;
    (*m_timers)[index].id = id;
    saveTimersToFile();
    
    return true;
}

TimerData* DataManager::getTimer(const String& id) {
    if (!m_initialized || !m_timers) {
        return nullptr;
    }
    
    int index = findTimerIndex(id);
    if (index >= 0) {
        return &(*m_timers)[index];
    }
    
    return nullptr;
}

DynamicJsonDocument DataManager::getTimersJSON() const {
    DynamicJsonDocument doc(4096);
    doc["success"] = true;
    
    JsonArray timersArray = doc.createNestedArray("data");
    
    if (m_initialized && m_timers) {
        for (const auto& timer : *m_timers) {
            if (timer.isActive) {
                timersArray.add(timer.toJson());
            }
        }
    }
    
    doc["count"] = timersArray.size();
    doc["timestamp"] = millis();
    
    return doc;
}

int DataManager::getTimerCount() const {
    return (m_initialized && m_timers) ? m_timers->size() : 0;
}

void DataManager::clearAllTimers() {
    if (m_initialized && m_timers) {
        m_timers->clear();
        saveTimersToFile();
        Serial.println("✅ All timers cleared");
    }
}

// ==================== Task Management ====================

bool DataManager::addTask(const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    if (!validateTaskData(task)) {
        return false;
    }

    TaskData newTask = task;
    if (newTask.id.isEmpty()) {
        newTask.id = generateUniqueId("task");
    }
    newTask.timestamp = millis();

    m_tasks->push_back(newTask);
    saveTasksToFile();

    Serial.printf("✅ Task added: %s\n", newTask.name.c_str());
    return true;
}

bool DataManager::deleteTask(const String& id) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    m_tasks->erase(m_tasks->begin() + index);
    saveTasksToFile();

    return true;
}

bool DataManager::updateTask(const String& id, const TaskData& task) {
    if (!m_initialized || !m_tasks) {
        return false;
    }

    int index = findTaskIndex(id);
    if (index < 0) {
        return false;
    }

    (*m_tasks)[index] = task;
    (*m_tasks)[index].id = id;
    saveTasksToFile();

    return true;
}

TaskData* DataManager::getTask(const String& id) {
    if (!m_initialized || !m_tasks) {
        return nullptr;
    }

    int index = findTaskIndex(id);
    if (index >= 0) {
        return &(*m_tasks)[index];
    }

    return nullptr;
}

DynamicJsonDocument DataManager::getTasksJSON() const {
    DynamicJsonDocument doc(4096);
    doc["success"] = true;

    JsonArray tasksArray = doc.createNestedArray("data");

    if (m_initialized && m_tasks) {
        for (const auto& task : *m_tasks) {
            if (task.isActive) {
                tasksArray.add(task.toJson());
            }
        }
    }

    doc["count"] = tasksArray.size();
    doc["timestamp"] = millis();

    return doc;
}

int DataManager::getTaskCount() const {
    return (m_initialized && m_tasks) ? m_tasks->size() : 0;
}

void DataManager::clearAllTasks() {
    if (m_initialized && m_tasks) {
        m_tasks->clear();
        saveTasksToFile();
        Serial.println("✅ All tasks cleared");
    }
}

// ==================== Configuration Management ====================

bool DataManager::setConfig(const String& key, const String& value) {
    if (!m_initialized || !m_config) {
        return false;
    }

    (*m_config)[key] = value;
    saveConfigToFile();

    return true;
}

String DataManager::getConfig(const String& key, const String& defaultValue) const {
    if (!m_initialized || !m_config) {
        return defaultValue;
    }

    auto it = m_config->find(key);
    if (it != m_config->end()) {
        return it->second;
    }

    return defaultValue;
}

bool DataManager::hasConfig(const String& key) const {
    if (!m_initialized || !m_config) {
        return false;
    }

    return m_config->find(key) != m_config->end();
}

DynamicJsonDocument DataManager::getSystemConfig() const {
    DynamicJsonDocument doc(2048);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");

    if (m_initialized && m_config) {
        for (const auto& pair : *m_config) {
            data[pair.first] = pair.second;
        }
    }

    doc["timestamp"] = millis();

    return doc;
}

bool DataManager::setSystemConfig(const DynamicJsonDocument& config) {
    if (!m_initialized || !m_config) {
        return false;
    }

    if (config.containsKey("data") && config["data"].is<JsonObject>()) {
        JsonObjectConst data = config["data"];

        for (JsonPairConst pair : data) {
            (*m_config)[pair.key().c_str()] = pair.value().as<String>();
        }

        saveConfigToFile();
        return true;
    }

    return false;
}

bool DataManager::resetSystemConfig() {
    if (!m_initialized || !m_config) {
        return false;
    }

    // Clear all configuration
    m_config->clear();

    // Set default configuration values
    setConfig("device_name", "ESP32-S3 IR Controller");
    setConfig("wifi_mode", "ap");
    setConfig("ap_ssid", "ESP32_IR_Controller");
    setConfig("ap_password", "12345678");
    setConfig("ir_frequency", "38000");
    setConfig("ir_protocol", "NEC");
    setConfig("auto_save", "true");
    setConfig("debug_mode", "false");

    // Save to file
    saveConfigToFile();

    Serial.println("✅ System configuration reset to defaults");
    return true;
}

// ==================== Data Persistence ====================

bool DataManager::saveAllData() {
    bool success = true;

    if (!saveSignalsToFile()) {
        Serial.println("❌ Failed to save signals");
        success = false;
    }

    if (!saveTimersToFile()) {
        Serial.println("❌ Failed to save timers");
        success = false;
    }

    if (!saveTasksToFile()) {
        Serial.println("❌ Failed to save tasks");
        success = false;
    }

    if (!saveConfigToFile()) {
        Serial.println("❌ Failed to save config");
        success = false;
    }

    if (success) {
        Serial.println("✅ All data saved successfully");
    }

    return success;
}

bool DataManager::loadAllData() {
    bool success = true;

    if (!loadSignalsFromFile()) {
        Serial.println("⚠️  Failed to load signals");
        success = false;
    }

    if (!loadTimersFromFile()) {
        Serial.println("⚠️  Failed to load timers");
        success = false;
    }

    if (!loadTasksFromFile()) {
        Serial.println("⚠️  Failed to load tasks");
        success = false;
    }

    if (!loadConfigFromFile()) {
        Serial.println("⚠️  Failed to load config");
        success = false;
    }

    return success;
}

void DataManager::clearAllData() {
    clearAllSignals();
    clearAllTimers();
    clearAllTasks();

    if (m_config) {
        m_config->clear();
        saveConfigToFile();
    }

    Serial.println("✅ All data cleared");
}

// ==================== Export/Import ====================

DynamicJsonDocument DataManager::exportData(const String& type) const {
    DynamicJsonDocument doc(16384);
    doc["success"] = true;
    doc["type"] = type;
    doc["timestamp"] = millis();

    JsonObject data = doc.createNestedObject("data");

    if (type == "all" || type == "signals") {
        JsonArray signals = data.createNestedArray("signals");
        if (m_initialized && m_signals) {
            for (const auto& signal : *m_signals) {
                signals.add(signal.toJson());
            }
        }
    }

    if (type == "all" || type == "timers") {
        JsonArray timers = data.createNestedArray("timers");
        if (m_initialized && m_timers) {
            for (const auto& timer : *m_timers) {
                timers.add(timer.toJson());
            }
        }
    }

    if (type == "all" || type == "tasks") {
        JsonArray tasks = data.createNestedArray("tasks");
        if (m_initialized && m_tasks) {
            for (const auto& task : *m_tasks) {
                tasks.add(task.toJson());
            }
        }
    }

    if (type == "all" || type == "config") {
        JsonObject config = data.createNestedObject("config");
        if (m_initialized && m_config) {
            for (const auto& pair : *m_config) {
                config[pair.first] = pair.second;
            }
        }
    }

    return doc;
}

bool DataManager::importData(const DynamicJsonDocument& data) {
    if (!m_initialized) {
        return false;
    }

    bool success = true;

    if (data.containsKey("data")) {
        JsonObjectConst dataObj = data["data"];

        // Import signals
        if (dataObj.containsKey("signals")) {
            JsonArrayConst signals = dataObj["signals"];
            for (JsonVariantConst signalVariant : signals) {
                SignalData signal;
                DynamicJsonDocument signalDoc(1024);
                signalDoc.set(signalVariant);
                if (signal.fromJson(signalDoc)) {
                    addSignal(signal);
                }
            }
        }

        // Import timers
        if (dataObj.containsKey("timers")) {
            JsonArrayConst timers = dataObj["timers"];
            for (JsonVariantConst timerVariant : timers) {
                TimerData timer;
                DynamicJsonDocument timerDoc(512);
                timerDoc.set(timerVariant);
                if (timer.fromJson(timerDoc)) {
                    addTimer(timer);
                }
            }
        }

        // Import tasks
        if (dataObj.containsKey("tasks")) {
            JsonArrayConst tasks = dataObj["tasks"];
            for (JsonVariantConst taskVariant : tasks) {
                TaskData task;
                DynamicJsonDocument taskDoc(512);
                taskDoc.set(taskVariant);
                if (task.fromJson(taskDoc)) {
                    addTask(task);
                }
            }
        }

        // Import config
        if (dataObj.containsKey("config")) {
            JsonObjectConst config = dataObj["config"];
            for (JsonPairConst pair : config) {
                setConfig(pair.key().c_str(), pair.value().as<String>());
            }
        }
    }

    return success;
}

bool DataManager::backupData() {
    DynamicJsonDocument backup = exportData("all");

    String backupFilename = "/data/backup_" + String(millis()) + ".json";

    File file = SPIFFS.open(backupFilename, "w");
    if (!file) {
        Serial.println("❌ Failed to create backup file");
        return false;
    }

    serializeJson(backup, file);
    file.close();

    Serial.printf("✅ Backup created: %s\n", backupFilename.c_str());
    return true;
}

// ==================== System Information ====================

size_t DataManager::getUsedSpace() const {
    return SPIFFS.usedBytes();
}

size_t DataManager::getFreeSpace() const {
    return SPIFFS.totalBytes() - SPIFFS.usedBytes();
}

DynamicJsonDocument DataManager::getSystemStatus() const {
    DynamicJsonDocument doc(1024);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");

    data["initialized"] = m_initialized;
    data["signal_count"] = getSignalCount();
    data["timer_count"] = getTimerCount();
    data["task_count"] = getTaskCount();
    data["config_count"] = m_config ? m_config->size() : 0;

    JsonObject storage = data.createNestedObject("storage");
    storage["total_bytes"] = SPIFFS.totalBytes();
    storage["used_bytes"] = SPIFFS.usedBytes();
    storage["free_bytes"] = getFreeSpace();
    storage["usage_percent"] = (float)SPIFFS.usedBytes() / SPIFFS.totalBytes() * 100;

    JsonObject memory = data.createNestedObject("memory");
    memory["free_heap"] = ESP.getFreeHeap();
    memory["free_psram"] = psramFound() ? ESP.getFreePsram() : 0;

    data["timestamp"] = millis();

    return doc;
}

DynamicJsonDocument DataManager::getStatistics() const {
    DynamicJsonDocument doc(512);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");

    data["total_signals"] = getSignalCount();
    data["total_timers"] = getTimerCount();
    data["total_tasks"] = getTaskCount();
    data["storage_used"] = getUsedSpace();
    data["storage_free"] = getFreeSpace();

    data["timestamp"] = millis();

    return doc;
}

// ==================== Helper Methods ====================

String DataManager::generateUniqueId(const String& prefix) const {
    return prefix + "_" + String(millis()) + "_" + String(random(1000, 9999));
}

int DataManager::findSignalIndex(const String& id) const {
    if (!m_signals) return -1;

    for (size_t i = 0; i < m_signals->size(); i++) {
        if ((*m_signals)[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTimerIndex(const String& id) const {
    if (!m_timers) return -1;

    for (size_t i = 0; i < m_timers->size(); i++) {
        if ((*m_timers)[i].id == id) {
            return i;
        }
    }
    return -1;
}

int DataManager::findTaskIndex(const String& id) const {
    if (!m_tasks) return -1;

    for (size_t i = 0; i < m_tasks->size(); i++) {
        if ((*m_tasks)[i].id == id) {
            return i;
        }
    }
    return -1;
}

// ==================== Validation Methods ====================

bool DataManager::validateSignalData(const SignalData& signal) const {
    if (signal.name.isEmpty()) {
        Serial.println("❌ Signal name cannot be empty");
        return false;
    }

    if (signal.signalCode.isEmpty()) {
        Serial.println("❌ Signal code cannot be empty");
        return false;
    }

    return true;
}

bool DataManager::validateTimerData(const TimerData& timer) const {
    if (timer.name.isEmpty()) {
        Serial.println("❌ Timer name cannot be empty");
        return false;
    }

    if (timer.signalId.isEmpty()) {
        Serial.println("❌ Timer signal ID cannot be empty");
        return false;
    }

    return true;
}

bool DataManager::validateTaskData(const TaskData& task) const {
    if (task.name.isEmpty()) {
        Serial.println("❌ Task name cannot be empty");
        return false;
    }

    if (task.type.isEmpty()) {
        Serial.println("❌ Task type cannot be empty");
        return false;
    }

    return true;
}

// ==================== File Operations ====================

bool DataManager::loadSignalsFromFile() {
    if (!SPIFFS.exists(SIGNALS_FILE)) {
        Serial.println("Signals file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(SIGNALS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open signals file");
        return false;
    }

    DynamicJsonDocument doc(8192);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse signals file: %s\n", error.c_str());
        return false;
    }

    if (m_signals) {
        m_signals->clear();

        if (doc.containsKey("data")) {
            JsonArrayConst signalsArray = doc["data"];
            for (JsonVariantConst signalVariant : signalsArray) {
                SignalData signal;
                DynamicJsonDocument signalDoc(1024);
                signalDoc.set(signalVariant);
                if (signal.fromJson(signalDoc)) {
                    m_signals->push_back(signal);
                }
            }
        }

        Serial.printf("✅ Loaded %d signals from file\n", m_signals->size());
    }

    return true;
}

bool DataManager::saveSignalsToFile() {
    if (!m_signals) {
        return false;
    }

    DynamicJsonDocument doc(8192);
    doc["success"] = true;
    JsonArray signalsArray = doc.createNestedArray("data");

    for (const auto& signal : *m_signals) {
        signalsArray.add(signal.toJson());
    }

    doc["count"] = m_signals->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(SIGNALS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open signals file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTimersFromFile() {
    if (!SPIFFS.exists(TIMERS_FILE)) {
        Serial.println("Timers file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TIMERS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open timers file");
        return false;
    }

    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse timers file: %s\n", error.c_str());
        return false;
    }

    if (m_timers) {
        m_timers->clear();

        if (doc.containsKey("data")) {
            JsonArrayConst timersArray = doc["data"];
            for (JsonVariantConst timerVariant : timersArray) {
                TimerData timer;
                DynamicJsonDocument timerDoc(512);
                timerDoc.set(timerVariant);
                if (timer.fromJson(timerDoc)) {
                    m_timers->push_back(timer);
                }
            }
        }

        Serial.printf("✅ Loaded %d timers from file\n", m_timers->size());
    }

    return true;
}

bool DataManager::saveTimersToFile() {
    if (!m_timers) {
        return false;
    }

    DynamicJsonDocument doc(4096);
    doc["success"] = true;
    JsonArray timersArray = doc.createNestedArray("data");

    for (const auto& timer : *m_timers) {
        timersArray.add(timer.toJson());
    }

    doc["count"] = m_timers->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TIMERS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open timers file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadTasksFromFile() {
    if (!SPIFFS.exists(TASKS_FILE)) {
        Serial.println("Tasks file not found, starting with empty collection");
        return true;
    }

    File file = SPIFFS.open(TASKS_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open tasks file");
        return false;
    }

    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse tasks file: %s\n", error.c_str());
        return false;
    }

    if (m_tasks) {
        m_tasks->clear();

        if (doc.containsKey("data")) {
            JsonArrayConst tasksArray = doc["data"];
            for (JsonVariantConst taskVariant : tasksArray) {
                TaskData task;
                DynamicJsonDocument taskDoc(512);
                taskDoc.set(taskVariant);
                if (task.fromJson(taskDoc)) {
                    m_tasks->push_back(task);
                }
            }
        }

        Serial.printf("✅ Loaded %d tasks from file\n", m_tasks->size());
    }

    return true;
}

bool DataManager::saveTasksToFile() {
    if (!m_tasks) {
        return false;
    }

    DynamicJsonDocument doc(4096);
    doc["success"] = true;
    JsonArray tasksArray = doc.createNestedArray("data");

    for (const auto& task : *m_tasks) {
        tasksArray.add(task.toJson());
    }

    doc["count"] = m_tasks->size();
    doc["timestamp"] = millis();

    File file = SPIFFS.open(TASKS_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open tasks file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

bool DataManager::loadConfigFromFile() {
    if (!SPIFFS.exists(CONFIG_FILE)) {
        Serial.println("Config file not found, using default configuration");

        // Set default configuration
        if (m_config) {
            (*m_config)["system_name"] = "ESP32-S3 IR Control System";
            (*m_config)["version"] = "2.0.0";
            (*m_config)["ir_frequency"] = "38000";
            (*m_config)["learning_timeout"] = "30000";
            saveConfigToFile();
        }

        return true;
    }

    File file = SPIFFS.open(CONFIG_FILE, "r");
    if (!file) {
        Serial.println("❌ Failed to open config file");
        return false;
    }

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ Failed to parse config file: %s\n", error.c_str());
        return false;
    }

    if (m_config) {
        m_config->clear();

        if (doc.containsKey("data")) {
            JsonObjectConst data = doc["data"];
            for (JsonPairConst pair : data) {
                (*m_config)[pair.key().c_str()] = pair.value().as<String>();
            }
        }

        Serial.printf("✅ Loaded %d config items from file\n", m_config->size());
    }

    return true;
}

bool DataManager::saveConfigToFile() {
    if (!m_config) {
        return false;
    }

    DynamicJsonDocument doc(2048);
    doc["success"] = true;

    JsonObject data = doc.createNestedObject("data");
    for (const auto& pair : *m_config) {
        data[pair.first] = pair.second;
    }

    doc["timestamp"] = millis();

    File file = SPIFFS.open(CONFIG_FILE, "w");
    if (!file) {
        Serial.println("❌ Failed to open config file for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();

    return true;
}

// ==================== Data Structure Implementations ====================

DynamicJsonDocument SignalData::toJson() const {
    DynamicJsonDocument doc(1024);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["protocol"] = protocol;
    doc["signalCode"] = signalCode;  // 前端期望的字段名
    doc["rawData"] = rawData;        // 前端期望的字段名
    doc["frequency"] = frequency;    // 前端期望String类型
    doc["created"] = created;        // 前端期望的字段名
    doc["isActive"] = isActive;      // 前端期望的字段名
    doc["sentCount"] = sentCount;    // 前端期望的字段名
    doc["lastSent"] = lastSent;      // 前端期望的字段名
    doc["isLearned"] = isLearned;    // 前端期望的字段名
    doc["description"] = description;

    return doc;
}

bool SignalData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "other";
    protocol = doc["protocol"] | "";
    signalCode = doc["signalCode"] | "";  // 前端期望的字段名
    rawData = doc["rawData"] | "";        // 前端期望的字段名
    frequency = doc["frequency"] | "38000"; // 前端期望String类型
    created = doc["created"] | 0;         // 前端期望的字段名
    isActive = doc["isActive"] | true;    // 前端期望的字段名
    sentCount = doc["sentCount"] | 0;     // 前端期望的字段名
    lastSent = doc["lastSent"] | 0;       // 前端期望的字段名
    isLearned = doc["isLearned"] | false; // 前端期望的字段名
    description = doc["description"] | "";

    return true;
}

DynamicJsonDocument TimerData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["signal_id"] = signalId;        // 原字段名，使用下划线
    doc["schedule"] = schedule;
    doc["is_active"] = isActive;        // 原字段名，使用下划线
    doc["timestamp"] = timestamp;
    doc["last_executed"] = lastExecuted; // 原字段名，使用下划线
    doc["execution_count"] = executionCount; // 原字段名，使用下划线

    return doc;
}

bool TimerData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    signalId = doc["signal_id"] | "";        // 原字段名，使用下划线
    schedule = doc["schedule"] | "";
    isActive = doc["is_active"] | false;     // 原字段名，使用下划线，原默认值false
    timestamp = doc["timestamp"] | 0;
    lastExecuted = doc["last_executed"] | 0; // 原字段名，使用下划线
    executionCount = doc["execution_count"] | 0; // 原字段名，使用下划线

    return true;
}

DynamicJsonDocument TaskData::toJson() const {
    DynamicJsonDocument doc(512);

    doc["id"] = id;
    doc["name"] = name;
    doc["type"] = type;
    doc["status"] = status;
    doc["parameters"] = parameters;     // 原字段名
    doc["timestamp"] = timestamp;
    doc["start_time"] = startTime;      // 原字段名，使用下划线
    doc["end_time"] = endTime;          // 原字段名，使用下划线
    doc["result"] = result;
    doc["error"] = error;

    return doc;
}

bool TaskData::fromJson(const DynamicJsonDocument& doc) {
    if (!doc.containsKey("name")) {
        return false;
    }

    id = doc["id"] | "";
    name = doc["name"] | "";
    type = doc["type"] | "";
    status = doc["status"] | "pending";
    parameters = doc["parameters"] | "";  // 原字段名
    timestamp = doc["timestamp"] | 0;
    startTime = doc["start_time"] | 0;    // 原字段名，使用下划线
    endTime = doc["end_time"] | 0;        // 原字段名，使用下划线
    result = doc["result"] | "";
    error = doc["error"] | "";

    return true;
}

// TaskData参数管理方法
DynamicJsonDocument TaskData::getParameters() const {
    DynamicJsonDocument doc(512);
    if (!parameters.isEmpty()) {
        deserializeJson(doc, parameters);
    }
    return doc;
}

void TaskData::setParameters(const DynamicJsonDocument& params) {
    serializeJson(params, parameters);
}

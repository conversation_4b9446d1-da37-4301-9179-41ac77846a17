/**
 * R1 Signal Management Module - Complete Rewrite Version
 * Strictly follows R1 system architecture documentation and core standards
 *
 * <AUTHOR> System
 * @version 3.0.0
 */

class SignalManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'SignalManager');

    // 核心数据 - 使用优化的存储系统
    this.signalStorage = new OptimizedSignalStorage();
    this.selectedSignals = new Set();

    // 兼容性属性 - 保持现有代码工作
    const storage = this.signalStorage;
    this.signals = {
      set: (id, signal) => storage.addSignal(signal),
      get: (id) => storage.getSignal(id),
      has: (id) => storage.getSignal(id) !== null,
      delete: (id) => storage.removeSignal(id),
      clear: () => storage.clear(),
      values: () => storage.getAllSignals(),
      keys: () => storage.getAllSignals().map(s => s.id),
      entries: () => storage.getAllSignals().map(s => [s.id, s]),
      forEach: (callback) => storage.getAllSignals().forEach(s => callback(s, s.id)),
      get size() { return storage.getStats().totalSignals; }
    };

    // 状态管理
    this.isLearning = false;
    this.currentView = 'grid'; // 'grid' | 'list'
    this.isMultiSelectMode = false;

    // 信号学习状态管理
    this.learningState = {
      isLearning: false,        // 是否在学习模式
      hasUnsavedSignal: false,  // 是否有未保存的信号
      pausedTasks: [],          // 暂停的任务列表
      currentSignalData: null,  // 当前检测到的信号数据
      learningStartTime: 0,     // 学习开始时间
      lastActivityTime: 0       // 最后活动时间
    };

    // 超时控制
    this.learningTimeout = null;      // 学习超时定时器
    this.autoSaveTimeout = null;      // 自动保存定时器
    this.activityCheckInterval = null; // 活动检查定时器

    // 过滤和搜索
    this.searchKeyword = '';
    this.filterType = '';
    this.sortBy = 'name';

    // 性能优化组件
    this.domUpdater = window.DOMUpdateManager;
    this.virtualList = null;

    // 统计数据
    this.stats = {
      totalSignals: 0,
      totalSent: 0,
      lastLearned: null
    };

    // 初始化页面生命周期监听
    this.initPageLifecycleListeners();
  }

  /**
   * 设置事件监听器 - 继承自BaseModule
   */
  async setupEventListeners() {
    // 监听控制模块的信号请求
    this.eventBus.on('signal.request.all', (data) => {
      this.handleSignalRequest(data);
    });

    this.eventBus.on('signal.request.by-ids', (data) => {
      this.handleSignalRequestByIds(data);
    });

    this.eventBus.on('signal.request.selected', (data) => {
      this.handleSelectedSignalRequest(data);
    });

    this.eventBus.on('signal.request.count', (data) => {
      this.handleSignalCountRequest(data);
    });

    this.eventBus.on('signal.request.send', (data) => {
      this.handleSendSignalRequest(data);
    });

    // 监听批量发射响应
    this.eventBus.on('signal.batch.emit.response', (data) => {
      this.handleBatchEmitResponse(data);
    });

    // 学习状态查询事件响应 - 符合架构标准
    this.eventBus.on('signal.learning.status.request', (data) => {
      const isLearning = this.isLearning || false;
      if (data.callback) {
        data.callback({ isLearning });
      }
    });

    // ✅ 监听控制模块的信号发射统计更新事件
    this.eventBus.on('signal.emit.success', (data) => {
      this.handleSignalEmitSuccess(data);
    });

    this.eventBus.on('signal.emit.failed', (data) => {
      this.handleSignalEmitFailed(data);
    });

    // ✅ 监听追加信号事件 - 同步选中状态
    this.eventBus.on('control.signals.appended', (data) => {
      this.handleSignalsAppended(data);
    });


  }

  /**
   * 设置UI - 继承自BaseModule
   */
  async setupUI() {
    this.initEventDelegation();
    this.initInputElements();
    this.renderInitialUI();
    this.checkForUnsavedSignalOnLoad();
    this.updateLearningUI(); // 初始化按钮状态
  }

  /**
   * 加载模块数据 - 继承自BaseModule
   */
  async loadModuleData() {
    try {
      // 前端职责：只从后端ESP32加载数据
      await this.loadSignalsFromESP32();

      console.log('📡 ESP32-S3硬件模式：系统已启动');
      console.log('🎯 请使用"学习信号"功能添加真实红外信号');

      this.updateStats();
      this.renderSignals();
    } catch (error) {
      this.handleError(error, '数据加载');
      console.log('⚠️ 数据加载失败，请检查ESP32连接');
    }
  }

  /**
   * 初始化事件委托 - 使用标准addEventListener
   */
  initEventDelegation() {
    const container = $('#signal-manager');
    if (!container) {
      console.error('SignalManager: 找不到容器元素 #signal-manager');
      return;
    }

    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (e) => {
      this.handleClick(e);
    });


  }

  /**
   * 初始化页面生命周期监听器
   */
  initPageLifecycleListeners() {
    // 页面卸载时自动停止学习
    window.addEventListener('beforeunload', () => {
      if (this.learningState.isLearning) {
        this.stopLearningAndCleanup();
      }
    });

    // 页面隐藏时保存状态
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.learningState.hasUnsavedSignal) {
        this.saveUnsavedSignalToLocal();
      }
    });

    // 页面获得焦点时更新活动时间
    window.addEventListener('focus', () => {
      this.updateActivityTime();
    });


  }

  /**
   * 检查页面加载时是否有未保存的信号
   */
  checkForUnsavedSignalOnLoad() {
    try {
      const unsavedSignal = localStorage.getItem('signalManager_unsavedSignal');
      if (unsavedSignal) {
        const signalData = JSON.parse(unsavedSignal);
        this.showSignalRecoveryDialog(signalData);
      }
    } catch (error) {
      console.error('SignalManager: 检查未保存信号失败:', error);
      localStorage.removeItem('signalManager_unsavedSignal');
    }
  }

  /**
   * 显示信号恢复对话框
   */
  showSignalRecoveryDialog(signalData) {
    const modalContent = `
      <div class="modal-header">
        <h3>恢复未保存的信号</h3>
      </div>
      <div class="modal-body">
        <div class="recovery-dialog">
          <p>检测到一个未完成保存的信号，是否恢复？</p>
          <div class="signal-preview">
            <strong>信号数据:</strong> ${signalData.rawData?.substring(0, 50)}...
            <br>
            <strong>检测时间:</strong> ${new Date(signalData.detectedAt).toLocaleString()}
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="recovery-actions">
          <button class="btn secondary" id="discardRecoveryBtn">丢弃</button>
          <button class="btn primary" id="recoverSignalBtn">恢复编辑</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);

    // 添加事件监听器
    const discardBtn = $('#discardRecoveryBtn');
    if (discardBtn) {
      discardBtn.addEventListener('click', () => {
        this.discardRecoverySignal();
      });
    }

    const recoverBtn = $('#recoverSignalBtn');
    if (recoverBtn) {
      recoverBtn.addEventListener('click', () => {
        this.recoverUnsavedSignal();
      });
    }
  }

  /**
   * 处理点击事件 - 使用closest解决事件冒泡问题
   */
  handleClick(e) {
    try {
      const actionElement = e.target.closest('[data-action]');
      if (!actionElement) return;

      const action = actionElement.getAttribute('data-action');
      if (!action) return;

      // 获取信号ID
      let signalId = actionElement.getAttribute('data-signal-id');
      if (!signalId) {
        const signalElement = actionElement.closest('[data-signal-id]');
        signalId = signalElement?.getAttribute('data-signal-id');
      }

      // 路由到具体处理方法
      this.routeAction(action, signalId, e);

    } catch (error) {
      console.error('❌ SignalManager: 事件处理错误', error);
      this.handleError(error, '事件处理');
    }
  }

  /**
   * 动作路由 - 符合架构标准
   */
  routeAction(action, signalId, event) {
    switch (action) {
      case 'toggle-learning':
        if (this.learningState.isLearning) {
          this.stopLearning();
        } else {
          this.startLearning();
        }
        break;
      case 'toggle-view':
        this.toggleView();
        break;
      case 'toggle-multiselect':
        this.toggleMultiSelectMode();
        break;
      case 'toggle-search':
        this.toggleSearchArea();
        break;
      case 'import-signals':
        this.showImportDialog();
        break;
      case 'export-all-signals':
        this.exportAllSignals();
        break;
      case 'select-all':
        this.selectAllSignals();
        break;
      case 'select-none':
        this.selectNoneSignals();
        break;
      case 'batch-send':
        this.batchSendSignals();
        break;
      case 'export-selected':
        this.exportSelectedSignals();
        break;
      case 'delete-selected':
        this.batchDeleteSignals();
        break;
      case 'send-signal':
        if (signalId) this.sendSignal(signalId);
        break;
      case 'edit-signal':
        if (signalId) this.showEditDialog(signalId);
        break;
      case 'delete-signal':
        if (signalId) this.deleteSignal(signalId);
        break;
      case 'show-details':
        if (signalId) this.showSignalDetails(signalId);
        break;
      case 'toggle-selection':
        if (signalId) this.toggleSignalSelection(signalId);
        break;
      case 'toggle-selection-checkbox':
        event.stopPropagation();
        if (signalId) this.handleCheckboxToggle(signalId, event);
        break;

      // 增强版学习相关动作
      case 'save-learned-signal':
        this.saveLearnedSignal();
        break;

      case 'cancel-learned-signal':
        this.cancelLearnedSignal();
        break;

      case 'continue-learning':
        this.saveLearnedSignal();
        break;

      case 'recover-signal':
        this.recoverUnsavedSignal();
        break;

      case 'discard-recovery':
        this.discardRecoverySignal();
        break;

      case 'cancel-learning':
        this.stopLearning();
        break;

      // 表单提交相关动作
      case 'submit-learn-form':
        this.handleLearnFormSubmit();
        break;

      case 'submit-edit-form':
        if (signalId) this.handleEditFormSubmit(signalId);
        break;

      // 导入相关动作
      case 'import-from-file':
        this.importFromFile();
        break;

      case 'import-from-text':
        this.importFromText();
        break;

      // 模态框相关动作
      case 'close-modal':
        if (window.R1System && window.R1System.closeModal) {
          window.R1System.closeModal();
        }
        break;

      default:
        console.log(`未处理的动作: ${action}`);
    }
  }

  /**
   * 初始化输入元素 - 符合架构标准
   */
  initInputElements() {
    // 搜索输入框
    const searchInput = $('#signalSearchInput');
    if (searchInput) {
      searchInput.oninput = R1Utils.debounce((e) => {
        this.searchKeyword = e.target.value;
        this.renderSignals();
      }, 300);
    }

    // 类型过滤
    const typeFilter = $('#signalTypeFilter');
    if (typeFilter) {
      typeFilter.onchange = (e) => {
        this.filterType = e.target.value;
        this.renderSignals();
      };
    }

    // 排序选择
    const sortSelect = $('#signalSortBy');
    if (sortSelect) {
      sortSelect.onchange = (e) => {
        this.sortBy = e.target.value;
        this.renderSignals();
      };
    }
  }

  /**
   * 渲染初始UI - 符合架构标准
   */
  renderInitialUI() {
    // 🔧 移除所有调试代码，符合架构文档的性能优化标准
    this.updateStats();

    // 只在首次渲染时生成HTML，避免重复渲染
    if (!this._isInitialRendered) {
      this.renderSignals();
      this._isInitialRendered = true;
    }

    this.updateViewToggleButton();
    this.updateMultiSelectButton();
  }

  /**
   * 自动清理功能已移除 - 保护用户数据安全
   *
   * 移除原因：
   * 1. 模拟信号与真实信号格式完全相同，无法可靠区分
   * 2. 用户可能起相同的信号名称（如"客厅电视开关"）
   * 3. 真实设备可能有相同的信号码（如0x20DF10EF）
   * 4. 协议类型完全一样（NEC、SONY等）
   * 5. isLearned标志也相同
   *
   * 替代方案：
   * - 开发调试时：手动清理localStorage（浏览器开发者工具）
   * - 用户使用时：手动删除不需要的信号（批量删除功能）
   * - 系统保证：不会自动删除任何用户数据
   */

  // 已删除测试信号功能 - 纯前端版本只从ESP32后端加载真实数据

  // 已删除本地存储加载方法 - 纯前端版本只从ESP32后端加载

  // 已删除数据验证方法 - 纯前端版本信任ESP32后端返回的数据格式

  // 已删除数据标准化方法 - 纯前端版本信任ESP32后端返回的标准格式数据

  // 已删除本地存储保存方法 - 纯前端版本通过ESP32后端API保存

  // 已删除智能数据转换方法 - 纯前端版本期望ESP32后端返回标准格式数据

  // 已删除所有数据处理辅助方法 - 纯前端版本信任ESP32后端返回的标准格式数据

  /**
   * 从ESP32加载信号
   */
  async loadSignalsFromESP32() {
    try {
      const response = await this.esp32.get('/api/signals');
      if (response.success && response.data) {
        // 清空现有缓存，重新加载
        this.signalStorage.clear();

        // 修复：后端返回的数据结构是 {signals: [...], stats: {...}}
        const signals = response.data.signals || response.data;
        if (Array.isArray(signals)) {
          signals.forEach(signal => {
            this.signalStorage.addSignal(signal);
          });
          console.log(`✅ SignalManager: 从ESP32加载了 ${signals.length} 个信号`);
        } else {
          console.warn('⚠️ SignalManager: ESP32返回的数据格式不正确');
        }
      }
    } catch (error) {
      console.warn('SignalManager: ESP32连接失败，使用本地数据');
    }
  }

  /**
   * 更新统计数据
   */
  updateStats() {
    this.stats.totalSignals = this.signals.size;

    // 更新UI中的统计显示
    const totalEl = $('#totalSignalsCount');
    if (totalEl) {
      totalEl.textContent = this.stats.totalSignals;
    }
  }

  /**
   * 渲染信号列表/网格 - 符合架构标准
   */
  renderSignals() {
    const gridContainer = $('#signalsGrid');
    const listContainer = $('#signalsList');

    if (!gridContainer || !listContainer) {
      console.error('SignalManager: 找不到信号容器元素');
      return;
    }

    // 性能优化：检查是否需要重新渲染
    const filteredSignals = this.getFilteredSignals();
    const currentSignalsHash = this.getSignalsHash(filteredSignals);

    console.log(`🔍 渲染信号 - 总数: ${this.signals.size}, 过滤后: ${filteredSignals.length}`);
    console.log(`🔍 搜索关键词: "${this.searchKeyword}", 类型过滤: "${this.filterType}", 排序: "${this.sortBy}"`);

    // 详细显示过滤过程
    if (this.signals.size !== filteredSignals.length) {
      console.log(`🔍 过滤详情:`);
      console.log(`  - 原始信号: ${this.signals.size} 个`);

      let afterSearch = Array.from(this.signals.values());
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        afterSearch = afterSearch.filter(signal =>
          signal.name.toLowerCase().includes(keyword) ||
          signal.description?.toLowerCase().includes(keyword)
        );
        console.log(`  - 搜索过滤后: ${afterSearch.length} 个 (关键词: "${this.searchKeyword}")`);
      }

      if (this.filterType) {
        afterSearch = afterSearch.filter(signal => signal.type === this.filterType);
        console.log(`  - 类型过滤后: ${afterSearch.length} 个 (类型: "${this.filterType}")`);
      }

      console.log(`  - 最终显示: ${filteredSignals.length} 个`);
    }

    if (this._lastSignalsHash === currentSignalsHash &&
        this._lastView === this.currentView &&
        this._lastMultiSelectMode === this.isMultiSelectMode) {
      console.log('🔍 使用缓存，跳过渲染');
      return;
    }

    // 使用DocumentFragment减少重排重绘
    if (this.currentView === 'grid') {
      gridContainer.style.display = 'grid';
      listContainer.style.display = 'none';
      this.renderSignalGridOptimized(gridContainer, filteredSignals);
    } else {
      gridContainer.style.display = 'none';
      listContainer.style.display = 'block';
      this.renderSignalListOptimized(listContainer, filteredSignals);
    }

    // 缓存渲染状态
    this._lastSignalsHash = currentSignalsHash;
    this._lastView = this.currentView;
    this._lastMultiSelectMode = this.isMultiSelectMode;
  }

  /**
   * 获取过滤后的信号
   */
  getFilteredSignals() {
    let signals = Array.from(this.signals.values());

    // 搜索过滤
    if (this.searchKeyword) {
      const keyword = this.searchKeyword.toLowerCase();
      signals = signals.filter(signal =>
        signal.name.toLowerCase().includes(keyword) ||
        signal.description?.toLowerCase().includes(keyword)
      );
    }

    // 类型过滤
    if (this.filterType) {
      signals = signals.filter(signal => signal.type === this.filterType);
    }

    // 排序
    signals.sort((a, b) => {
      switch (this.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return b.created - a.created;
        case 'used':
          return b.sentCount - a.sentCount;
        default:
          return 0;
      }
    });

    return signals;
  }

  /**
   * 计算信号数据哈希 - 用于检测变化
   */
  getSignalsHash(signals) {
    const hashData = signals.map(s => `${s.id}-${s.name}-${s.sentCount}-${this.selectedSignals.has(s.id)}`).join('|');
    return hashData.length + '-' + (hashData.charCodeAt(0) || 0);
  }

  /**
   * 渲染信号网格 - 性能优化版本
   */
  renderSignalGridOptimized(container, signals) {
    if (signals.length === 0) {
      container.innerHTML = '<div class="no-signals">暂无信号数据</div>';
      return;
    }

    console.log(`🔍 开始渲染 ${signals.length} 个信号到网格`);

    // 🔧 使用DocumentFragment减少DOM操作
    const fragment = document.createDocumentFragment();
    const tempDiv = R1Utils.dom.create('div');

    // 批量生成HTML - 增加错误处理
    try {
      const htmlParts = [];
      signals.forEach((signal, index) => {
        try {
          const html = this.createSignalCardHTML(signal);
          htmlParts.push(html);
          console.log(`✅ 信号 ${index + 1}: ${signal.name} HTML生成成功`);
        } catch (error) {
          console.error(`❌ 信号 ${index + 1}: ${signal.name} HTML生成失败:`, error, signal);
        }
      });

      const html = htmlParts.join('');
      tempDiv.innerHTML = html;

      console.log(`📊 HTML生成完成，共 ${htmlParts.length} 个信号，DOM子元素: ${tempDiv.children.length}`);

      // 移动所有子元素到fragment
      while (tempDiv.firstChild) {
        fragment.appendChild(tempDiv.firstChild);
      }

      // 一次性替换内容
      container.innerHTML = '';
      container.appendChild(fragment);

      console.log(`✅ 网格渲染完成，容器子元素数量: ${container.children.length}`);

    } catch (error) {
      console.error('❌ 网格渲染过程出错:', error);
      container.innerHTML = '<div class="error">渲染出错，请刷新页面</div>';
    }
  }

  /**
   * 渲染信号列表 - 性能优化版本
   */
  renderSignalListOptimized(container, signals) {
    if (signals.length === 0) {
      container.innerHTML = '<div class="no-signals">暂无信号数据</div>';
      return;
    }

    // 🔧 使用DocumentFragment减少DOM操作
    const fragment = document.createDocumentFragment();
    const tempDiv = document.createElement('div');

    // 批量生成HTML
    const html = signals.map(signal => this.createSignalListItemHTML(signal)).join('');
    tempDiv.innerHTML = html;

    // 移动所有子元素到fragment
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }

    // 一次性替换内容
    container.innerHTML = '';
    container.appendChild(fragment);
  }



  /**
   * 创建信号卡片HTML - 优化版，简洁清晰
   */
  createSignalCardHTML(signal) {
    try {
      // 数据安全检查
      if (!signal || !signal.id) {
        console.error('❌ 信号数据无效:', signal);
        return '<div class="signal-card error">信号数据错误</div>';
      }

      // 由于所有数据都已统一为标准格式，直接使用即可
      const isSelected = this.selectedSignals.has(signal.id);
      const typeIcon = this.getTypeIcon(signal.type);

      // 优化时间显示 - 添加年份，更清晰
      const createdText = signal.created ?
        R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm') : '未知时间';

      // 安全的字符串处理
      const safeName = (signal.name || '未命名').replace(/"/g, '&quot;');
      const safeSignalCode = (signal.signalCode || 'N/A').replace(/"/g, '&quot;');
      const safeProtocol = (signal.protocol || 'N/A').replace(/"/g, '&quot;');
      const safeId = (signal.id || '').replace(/"/g, '&quot;');

      return `
        <div class="signal-card ${isSelected ? 'selected' : ''} ${this.isMultiSelectMode ? 'multiselect-mode' : ''}"
             data-signal-id="${safeId}"
             ${this.isMultiSelectMode ? 'data-action="toggle-selection"' : ''}>
          ${this.isMultiSelectMode ? `
            <div class="signal-checkbox">
              <input type="checkbox" ${isSelected ? 'checked' : ''}
                     data-action="toggle-selection-checkbox" data-signal-id="${safeId}">
            </div>
          ` : ''}
          <div class="signal-header">
            <div class="signal-icon">${typeIcon}</div>
            <div class="signal-name">${safeName}</div>
            ${signal.isLearned ? '<div class="signal-badge learned">学习</div>' : '<div class="signal-badge manual">手动</div>'}
          </div>
          <div class="signal-code-info">
            <div class="signal-code">${safeSignalCode}</div>
            <div class="signal-protocol">${safeProtocol}</div>
          </div>
          <div class="signal-info">
            <div class="signal-type">${this.getTypeName(signal.type)}</div>
            <div class="signal-created">${createdText}</div>
          </div>
          <div class="signal-actions">
            <button class="btn small primary" data-action="send-signal" data-signal-id="${safeId}">发射</button>
            <button class="btn small secondary" data-action="edit-signal" data-signal-id="${safeId}">编辑</button>
            <button class="btn small secondary" data-action="show-details" data-signal-id="${safeId}">详情</button>
            <button class="btn small danger" data-action="delete-signal" data-signal-id="${safeId}">删除</button>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('❌ 创建信号卡片HTML失败:', error, signal);
      return '<div class="signal-card error">HTML生成错误</div>';
    }
  }

  /**
   * 创建信号列表项HTML - 优化版，简洁清晰
   */
  createSignalListItemHTML(signal) {
    const isSelected = this.selectedSignals.has(signal.id);
    const typeIcon = this.getTypeIcon(signal.type);

    // 优化时间显示 - 添加年份，更清晰
    const createdText = signal.created ?
      R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm') : '未知时间';

    return `
      <div class="signal-list-item ${isSelected ? 'selected' : ''} ${this.isMultiSelectMode ? 'multiselect-mode' : ''}"
           data-signal-id="${signal.id}"
           ${this.isMultiSelectMode ? 'data-action="toggle-selection"' : ''}>
        ${this.isMultiSelectMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${isSelected ? 'checked' : ''}
                   data-action="toggle-selection-checkbox" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        <div class="signal-icon">${typeIcon}</div>
        <div class="signal-name">${signal.name}</div>
        <div class="signal-code">${signal.signalCode || 'N/A'}</div>
        <div class="signal-protocol">${signal.protocol || 'N/A'}</div>
        <div class="signal-badge ${signal.isLearned ? 'learned' : 'manual'}">${signal.isLearned ? '学习' : '手动'}</div>
        <div class="signal-created">${createdText}</div>
        <div class="signal-actions">
          <button class="btn small primary" data-action="send-signal" data-signal-id="${signal.id}">发射</button>
          <button class="btn small secondary" data-action="edit-signal" data-signal-id="${signal.id}">编辑</button>
          <button class="btn small secondary" data-action="show-details" data-signal-id="${signal.id}">详情</button>
          <button class="btn small danger" data-action="delete-signal" data-signal-id="${signal.id}">删除</button>
        </div>
      </div>
    `;
  }

  /**
   * 获取类型图标
   */
  getTypeIcon(type) {
    const icons = {
      tv: '📺',
      ac: '❄️',
      light: '💡',
      fan: '🌀',
      other: '📱'
    };
    return icons[type] || icons.other;
  }

  /**
   * 获取类型名称
   */
  getTypeName(type) {
    const names = {
      tv: '电视',
      ac: '空调',
      light: '灯光',
      fan: '风扇',
      other: '其他'
    };
    return names[type] || names.other;
  }

  /**
   * 切换视图模式 - 性能优化版本
   */
  toggleView() {
    const oldView = this.currentView;
    this.currentView = this.currentView === 'grid' ? 'list' : 'grid';

    console.log(`🔧 SignalManager: 视图切换 ${oldView} -> ${this.currentView}`);

    // 🔧 强制重新渲染（因为视图变化）
    this._lastView = null;
    this.renderSignals();
    this.updateViewToggleButton();
  }

  /**
   * 更新视图切换按钮
   */
  updateViewToggleButton() {
    const btn = $('#signalViewToggle');
    if (btn) {
      const icon = btn.querySelector('.btn-icon');
      const text = btn.querySelector('.btn-text');

      if (this.currentView === 'grid') {
        if (icon) icon.textContent = '📋';
        if (text) text.textContent = '列表视图';
      } else {
        if (icon) icon.textContent = '⊞';
        if (text) text.textContent = '网格视图';
      }
    }
  }

  /**
   * 切换多选模式 - 性能优化版本
   */
  toggleMultiSelectMode() {
    const oldMode = this.isMultiSelectMode;
    this.isMultiSelectMode = !this.isMultiSelectMode;

    console.log(`🔧 SignalManager: 多选模式切换 ${oldMode} -> ${this.isMultiSelectMode}`);

    // 清除选择
    this.selectedSignals.clear();

    // 🔧 强制重新渲染（因为多选模式变化）
    this._lastMultiSelectMode = null;
    this.renderSignals();
    this.updateMultiSelectButton();
    this.updateBatchOperationsVisibility();
  }

  /**
   * 更新多选按钮
   */
  updateMultiSelectButton() {
    const btn = $('#multiSelectToggleBtn');
    if (btn) {
      const text = btn.querySelector('.btn-text');
      if (text) {
        text.textContent = this.isMultiSelectMode ? '退出多选' : '多选模式';
      }

      if (this.isMultiSelectMode) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    }
  }

  /**
   * 更新批量操作区域可见性
   */
  updateBatchOperationsVisibility() {
    const batchOps = $('#batchOperations');
    if (batchOps) {
      batchOps.style.display = this.isMultiSelectMode ? 'flex' : 'none';
    }
  }

  /**
   * 切换搜索区域
   */
  toggleSearchArea() {
    const searchArea = $('#searchFilterArea');
    if (searchArea) {
      const isVisible = searchArea.style.display !== 'none';
      searchArea.style.display = isVisible ? 'none' : 'block';
    }
  }

  /**
   * 清除所有过滤条件
   */
  clearAllFilters() {
    // 清除搜索关键词
    this.searchKeyword = '';
    const searchInput = $('#signalSearchInput');
    if (searchInput) {
      searchInput.value = '';
    }

    // 清除类型过滤
    this.filterType = '';
    const typeFilter = $('#signalTypeFilter');
    if (typeFilter) {
      typeFilter.value = '';
    }

    // 重置排序
    this.sortBy = 'name';
    const sortSelect = $('#signalSortBy');
    if (sortSelect) {
      sortSelect.value = 'name';
    }


  }



  /**
   * 全选信号
   */
  selectAllSignals() {
    const filteredSignals = this.getFilteredSignals();
    filteredSignals.forEach(signal => {
      this.selectedSignals.add(signal.id);
    });
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 全不选信号
   */
  selectNoneSignals() {
    this.selectedSignals.clear();
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 更新选中数量显示
   */
  updateSelectedCount() {
    const countEl = $('#selectedCount');
    if (countEl) {
      countEl.textContent = this.selectedSignals.size;
    }
  }

  /**
   * 切换信号选择状态
   */
  toggleSignalSelection(signalId) {
    if (this.selectedSignals.has(signalId)) {
      this.selectedSignals.delete(signalId);
    } else {
      this.selectedSignals.add(signalId);
    }
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 处理复选框切换
   */
  handleCheckboxToggle(signalId, event) {
    const checkbox = event.target.closest('input[type="checkbox"]') || event.target;
    if (checkbox.checked) {
      this.selectedSignals.add(signalId);
    } else {
      this.selectedSignals.delete(signalId);
    }
    this.updateSelectedCount();
  }

  /**
   * 显示学习对话框
   */
  showLearnDialog() {
    const modalContent = `
      <div class="modal-header">
        <h3>学习新信号</h3>
      </div>
      <div class="modal-body">
        <form id="learnSignalForm">
          <div class="form-group">
            <label for="signalName">信号名称</label>
            <input type="text" id="signalName" class="form-input" placeholder="请输入信号名称" required>
          </div>
          <div class="form-group">
            <label for="signalType">信号类型</label>
            <select id="signalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="signalDescription">描述 (可选)</label>
            <textarea id="signalDescription" class="form-input" placeholder="信号描述" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" data-action="submit-learn-form">开始学习</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);

    // 直接聚焦到名称输入框，移除不必要的定时器
    const nameInput = $('#signalName');
    if (nameInput) nameInput.focus();
  }

  /**
   * 发射信号 - 符合事件驱动架构，只负责管理不执行发射
   */
  async sendSignal(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号发射');
      return false;
    }

    try {
      // 通过控制模块发射单个信号
      // 信号管理模块只负责管理，不执行发射逻辑
      this.eventBus.emitSync('control.request.send-signal', {
        signalId: signal.id,
        source: 'SignalManager'
      });

      // 信号管理模块只负责发送请求，不执行发射
      // 发射逻辑、统计更新、UI更新都由控制模块的统一系统处理
      return true;

    } catch (error) {
      console.error(`❌ [DEBUG] SignalManager.sendSignal() 失败:`, error);
      this.handleError(error, '信号发射请求');
      return false;
    }
  }

  /**
   * 处理信号发射成功事件 - 由控制模块通知
   */
  handleSignalEmitSuccess(data) {
    try {
      const { signalId, signalName, timestamp } = data;
      const signal = this.signals.get(signalId);

      if (signal) {
        // 更新信号统计
        signal.lastSent = timestamp;
        signal.sentCount = (signal.sentCount || 0) + 1;
        this.stats.totalSent++;

        // 纯前端版本：数据通过ESP32 API同步

        // 更新UI
        this.renderSignals();
        this.updateStats();

        console.log(`✅ SignalManager: 信号发射成功统计已更新: ${signalName}`);
      }
    } catch (error) {
      console.error('SignalManager: 处理信号发射成功事件失败:', error);
    }
  }

  /**
   * 处理信号发射失败事件 - 由控制模块通知
   */
  handleSignalEmitFailed(data) {
    try {
      const { signalId, signalName, error } = data;
      console.warn(`⚠️ SignalManager: 信号发射失败: ${signalName}, 错误: ${error}`);

      // 可以在这里添加失败统计或其他处理逻辑
      // 目前只记录日志
    } catch (err) {
      console.error('SignalManager: 处理信号发射失败事件失败:', err);
    }
  }

  /**
   * 处理信号追加事件 - 同步选中状态
   */
  handleSignalsAppended(data) {
    try {


      // 将追加的信号添加到选中状态，保持UI与当前任务队列同步
      if (data.appendedSignals && Array.isArray(data.appendedSignals)) {
        data.appendedSignals.forEach(signal => {
          this.selectedSignals.add(signal.id);
        });

        // 更新UI显示 - 重新渲染信号列表以显示新的选中状态
        this.renderSignals();
        this.updateSelectedCount();


      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号追加事件';
    } catch (error) {
      console.error('SignalManager: 处理信号追加事件失败:', error);
    }
  }

  /**
   * 删除信号 - 纯前端版本通过ESP32 API删除
   */
  async deleteSignal(signalId, skipConfirm = false) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号删除');
      return false;
    }

    // 批量删除时跳过单个确认
    if (!skipConfirm && !confirm(`确定要删除信号 "${signal.name}" 吗？此操作不可恢复！`)) {
      return false;
    }

    try {
      // 纯前端版本：通过ESP32 API删除信号
      const response = await this.esp32.post('/api/signals/delete', {
        signalId: signalId
      });

      if (response.success) {
        // 删除成功，重新从ESP32加载数据
        await this.loadSignalsFromESP32();
        this.selectedSignals.delete(signalId);

        // 批量删除时不立即更新UI，由批量删除方法统一处理
        if (!skipConfirm) {
          // 单个删除时立即更新UI
          this.renderSignals();
          this.updateStats();
          this.updateSelectedCount();
          this.handleSuccess(`信号 "${signal.name}" 删除成功`, '信号删除');
        }

        return true;
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      this.handleError(error, '信号删除');
      return false;
    }
  }

  /**
   * 显示信号详情 - 支持新的信号格式
   */
  showSignalDetails(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号详情');
      return;
    }

    const modalContent = `
      <div class="modal-header">
        <h3>信号详情</h3>
      </div>
      <div class="modal-body">
        <div class="detail-grid">
          <div class="detail-item">
            <label>名称</label>
            <span>${signal.name}</span>
          </div>
          <div class="detail-item">
            <label>信号码</label>
            <span class="signal-code-display">${signal.signalCode || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <label>协议类型</label>
            <span class="signal-protocol-display">${signal.protocol || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <label>信号来源</label>
            <span class="signal-source">${signal.isLearned ? '学习获得' : '手动添加'}</span>
          </div>
          <div class="detail-item">
            <label>设备类型</label>
            <span>${this.getTypeName(signal.type)}</span>
          </div>
          <div class="detail-item">
            <label>描述</label>
            <span>${signal.description || '无'}</span>
          </div>
          <div class="detail-item">
            <label>载波频率</label>
            <span>${signal.frequency} Hz</span>
          </div>
          <div class="detail-item">
            <label>创建时间</label>
            <span>${R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm:ss')}</span>
          </div>
          <div class="detail-item">
            <label>发射次数</label>
            <span>${signal.sentCount}</span>
          </div>
          <div class="detail-item">
            <label>最后发射</label>
            <span>${signal.lastSent ? R1Utils.formatTime(signal.lastSent, 'YYYY-MM-DD HH:mm:ss') : '未发射'}</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">关闭</button>
          <button type="button" class="btn primary" data-action="send-signal" data-signal-id="${signal.id}">发射信号</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }

  /**
   * 批量发射信号 - 符合事件驱动架构，只负责管理不执行发射
   */
  async batchSendSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要发射的信号'), '批量发射');
      return;
    }

    const signalIds = Array.from(this.selectedSignals);
    console.log(`🚀 [DEBUG] SignalManager.batchSendSignals() 开始 - 信号数量: ${signalIds.length}, IDs: ${signalIds}`);

    try {
      // 🔥 统一事件：通过控制模块发射选中信号
      // 信号管理模块只负责管理，不执行发射逻辑
      console.log(`📤 [DEBUG] SignalManager: 即将发送 control.request.selected-signals 事件`);
      this.eventBus.emitSync('control.request.selected-signals', {
        signalIds: signalIds,
        source: 'SignalManager'
      });

      // ✅ 信号管理模块只负责发送请求，不执行发射
      // 发射逻辑、进度更新、统计更新都由控制模块的统一系统处理
      console.log(`✅ [DEBUG] SignalManager: 已向控制模块发送批量信号发射请求，信号数量: ${signalIds.length}`);

      this.handleSuccess(`已提交批量发射请求 (${signalIds.length} 个信号)`, '批量发射');

    } catch (error) {
      console.error(`❌ [DEBUG] SignalManager.batchSendSignals() 失败:`, error);
      this.handleError(error, '批量发射请求');
    }
  }

  /**
   * 批量删除信号 - 纯前端版本通过ESP32 API批量删除
   */
  async batchDeleteSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要删除的信号'), '批量删除');
      return;
    }

    const signalIds = Array.from(this.selectedSignals);
    const signalNames = signalIds.map(id => this.signals.get(id)?.name).filter(Boolean);

    // 只确认一次，显示所有要删除的信号
    if (!confirm(`确定要删除这 ${signalIds.length} 个信号吗？\n\n${signalNames.join('\n')}\n\n此操作不可恢复！`)) {
      return;
    }

    try {
      // 纯前端版本：通过ESP32 API批量删除信号
      // 修复：将数组转换为JSON字符串
      const response = await this.esp32.post('/api/signals/batch-delete', {
        signalIds: JSON.stringify(signalIds)
      });

      if (response.success) {
        // 删除成功，重新从ESP32加载数据
        await this.loadSignalsFromESP32();
        signalIds.forEach(id => {
          this.selectedSignals.delete(id);
        });

        // 更新UI
        this.renderSignals();
        this.updateStats();
        this.updateSelectedCount();

        const message = `批量删除完成: 成功 ${response.data.successCount || signalIds.length} 个，失败 ${response.data.errorCount || 0} 个`;
        this.handleSuccess(message, '批量删除');
      } else {
        throw new Error(response.error || '批量删除失败');
      }
    } catch (error) {
      this.handleError(error, '批量删除');
    }

    // 清除选择
    this.selectedSignals.clear();
    this.updateSelectedCount();
  }

  /**
   * 导出所有信号
   */
  exportAllSignals() {
    if (this.signals.size === 0) {
      this.handleError(new Error('没有信号可以导出'), '信号导出');
      return;
    }

    const allSignals = Array.from(this.signals.values());
    const exportData = {
      version: '3.0.0',
      exported: Date.now(),
      exportType: 'all',
      totalSignals: allSignals.length,
      signals: allSignals
    };

    this.downloadJSON(exportData, `r1-all-signals-${R1Utils.formatTime(Date.now(), 'YYYY-MM-DD-HH-mm-ss')}.json`);
    this.handleSuccess(`已导出全部 ${allSignals.length} 个信号`, '信号导出');
  }

  /**
   * 导出选中信号
   */
  exportSelectedSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要导出的信号'), '信号导出');
      return;
    }

    const selectedSignals = Array.from(this.selectedSignals)
      .map(id => this.signals.get(id))
      .filter(Boolean);

    const exportData = {
      version: '3.0.0',
      exported: Date.now(),
      exportType: 'selected',
      totalSignals: selectedSignals.length,
      signals: selectedSignals
    };

    this.downloadJSON(exportData, `r1-selected-signals-${R1Utils.formatTime(Date.now(), 'YYYY-MM-DD-HH-mm-ss')}.json`);
    this.handleSuccess(`已导出选中的 ${selectedSignals.length} 个信号`, '信号导出');
  }

  /**
   * 下载JSON文件
   */
  downloadJSON(data, filename) {
    const dataStr = JSON.stringify(data, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = R1Utils.dom.create('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
  }

  /**
   * 显示导入对话框
   */
  showImportDialog() {
    const modalContent = `
      <div class="modal-header">
        <h3>导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-options">
          <button type="button" class="btn primary" data-action="import-from-file">从文件导入</button>
          <button type="button" class="btn secondary" data-action="import-from-text">从文本导入</button>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }

  /**
   * 从文件导入信号
   */
  importFromFile() {
    const modalContent = `
      <div class="modal-header">
        <h3>从文件导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-file-area">
          <div class="form-group">
            <label for="importFileInput">选择信号文件</label>
            <input type="file" id="importFileInput" class="form-input" accept=".json,.txt,.xml,.md" required>
            <div class="form-help">
              支持格式：JSON、TXT、XML、MD
            </div>
          </div>
          <div class="import-preview" id="importPreview" style="display: none;">
            <h4>预览导入数据</h4>
            <div class="preview-content" id="previewContent"></div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" id="confirmFileImport" disabled>确认导入</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
    this.initFileImportHandlers();
  }

  /**
   * 从文本导入信号
   */
  importFromText() {
    const modalContent = `
      <div class="modal-header">
        <h3>从文本导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-text-area">
          <div class="form-group">
            <label for="importTextInput">信号数据</label>
            <textarea id="importTextInput" class="form-input" rows="8" placeholder="请输入信号数据，格式：&#10;信号码,协议类型,信号名称,描述&#10;&#10;示例：&#10;0x20DF10EF,NEC,客厅电视开关,客厅电视的开关信号&#10;0x30EF20DF,NEC,空调制冷,空调制冷模式&#10;0x1234,RC5,卧室灯开关,卧室主灯开关"></textarea>
            <div class="form-help">
              格式：信号码,协议类型,信号名称,描述（每行一个信号）
            </div>
          </div>
          <div class="form-group">
            <label for="defaultSignalType">默认信号类型</label>
            <select id="defaultSignalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="import-preview" id="textImportPreview" style="display: none;">
            <h4>预览导入数据</h4>
            <div class="preview-content" id="textPreviewContent"></div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn secondary" id="previewTextImport">预览</button>
          <button type="button" class="btn primary" id="confirmTextImport" disabled>确认导入</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
    this.initTextImportHandlers();
  }

  /**
   * 显示编辑对话框 - 更新为新的信号格式
   */
  showEditDialog(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号编辑');
      return;
    }

    const modalContent = `
      <div class="modal-header">
        <h3>编辑信号</h3>
      </div>
      <div class="modal-body">
        <form id="editSignalForm">
          <div class="form-group">
            <label for="editSignalCode">信号码</label>
            <input type="text" id="editSignalCode" class="form-input" value="${signal.signalCode || ''}" ${signal.isLearned ? 'readonly' : ''} placeholder="0x20DF10EF">
            ${signal.isLearned ? '<div class="form-help">学习获得的信号码不可修改</div>' : ''}
          </div>
          <div class="form-group">
            <label for="editSignalProtocol">协议类型</label>
            <select id="editSignalProtocol" class="form-input" ${signal.isLearned ? 'disabled' : ''}>
              <option value="NEC" ${signal.protocol === 'NEC' ? 'selected' : ''}>NEC</option>
              <option value="RC5" ${signal.protocol === 'RC5' ? 'selected' : ''}>RC5</option>
              <option value="SONY" ${signal.protocol === 'SONY' ? 'selected' : ''}>SONY</option>
              <option value="RAW" ${signal.protocol === 'RAW' ? 'selected' : ''}>RAW</option>
              <option value="OTHER" ${signal.protocol === 'OTHER' ? 'selected' : ''}>其他</option>
            </select>
            ${signal.isLearned ? '<div class="form-help">学习获得的协议类型不可修改</div>' : ''}
          </div>
          <div class="form-group">
            <label for="editSignalName">信号名称</label>
            <input type="text" id="editSignalName" class="form-input" value="${signal.name}" required>
          </div>
          <div class="form-group">
            <label for="editSignalType">信号类型</label>
            <select id="editSignalType" class="form-input">
              <option value="tv" ${signal.type === 'tv' ? 'selected' : ''}>电视</option>
              <option value="ac" ${signal.type === 'ac' ? 'selected' : ''}>空调</option>
              <option value="fan" ${signal.type === 'fan' ? 'selected' : ''}>风扇</option>
              <option value="light" ${signal.type === 'light' ? 'selected' : ''}>灯光</option>
              <option value="other" ${signal.type === 'other' ? 'selected' : ''}>其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editSignalDescription">描述</label>
            <textarea id="editSignalDescription" class="form-input" rows="3">${signal.description || ''}</textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" data-action="submit-edit-form" data-signal-id="${signal.id}">保存</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }
  /**
   * 处理编辑表单提交 - 纯前端版本通过ESP32 API更新
   */
  async handleEditFormSubmit(signalId) {
    try {
      const signal = this.signals.get(signalId);
      if (!signal) {
        this.handleError(new Error('信号不存在'), '信号编辑');
        return;
      }

      // 获取表单数据
      const signalCode = $('#editSignalCode')?.value?.trim();
      const protocol = $('#editSignalProtocol')?.value;
      const name = $('#editSignalName')?.value?.trim();
      const type = $('#editSignalType')?.value;
      const description = $('#editSignalDescription')?.value?.trim();

      // 验证必填字段
      if (!name) {
        this.handleError(new Error('信号名称不能为空'), '信号编辑');
        return;
      }

      // 构建更新数据
      const updateData = {
        signalId: signalId,
        name: name,
        type: type,
        description: description
      };

      // 如果不是学习获得的信号，包含信号码和协议
      if (!signal.isLearned) {
        if (!signalCode) {
          this.handleError(new Error('信号码不能为空'), '信号编辑');
          return;
        }
        updateData.signalCode = signalCode;
        updateData.protocol = protocol;
      }

      // 纯前端版本：通过ESP32 API更新信号
      // 修复：将updateData结构调整为后端期望的格式
      const response = await this.esp32.post('/api/signals/update', {
        signalId: updateData.signalId,
        signalData: JSON.stringify({
          name: updateData.name,
          type: updateData.type,
          description: updateData.description,
          signalCode: updateData.signalCode,
          protocol: updateData.protocol
        })
      });

      if (response.success) {
        // 更新成功，重新从ESP32加载数据
        await this.loadSignalsFromESP32();

        // 更新UI
        this.renderSignals();

        // 关闭模态框
        window.R1System.hideModal();

        this.handleSuccess(`信号 "${name}" 编辑成功`, '信号编辑');
      } else {
        throw new Error(response.error || '更新失败');
      }
    } catch (error) {
      this.handleError(error, '信号编辑');
    }
  }

  /**
   * 处理学习表单提交 - 直接开始学习
   */
  handleLearnFormSubmit() {
    try {
      // 关闭模态框
      window.R1System.hideModal();

      // 直接开始学习（新的学习流程不需要预设名称）
      this.startLearning();

    } catch (error) {
      this.handleError(error, '信号学习');
    }
  }

  // ==================== 增强版信号学习功能 ====================

  /**
   * 开始学习信号 - 纯前端版本只负责UI状态和ESP32 API调用
   */
  async startLearning() {
    try {
      // 检查是否已在学习状态
      if (this.learningState.isLearning) {
        this.handleError(new Error('已在学习模式中'), '开始学习');
        return;
      }

      console.log('SignalManager: 开始学习信号');

      // 纯前端版本：通过ESP32 API启动学习
      // 修复：发送后端期望的timeout参数
      const response = await this.esp32.post('/api/signals/learn/start', {
        timeout: 10000  // 10秒超时
      });

      if (response.success) {
        // 更新前端学习状态
        this.learningState.isLearning = true;
        this.learningState.hasUnsavedSignal = false;
        this.learningState.currentSignalData = null;
        this.learningState.learningStartTime = Date.now();

        // 显示学习指示器
        this.showLearningIndicator();
        this.updateLearningUI();

        this.handleSuccess('学习模式已启动，请对准设备按下遥控器', '开始学习');

        // 启动前端状态监听（通过WebSocket接收学习结果）
        this.startLearningStatusMonitor();
      } else {
        throw new Error(response.error || '启动学习失败');
      }
    } catch (error) {
      console.error('SignalManager: 开始学习失败:', error);
      this.handleError(error, '开始学习');
      this.resetLearningState();
    }
  }

  /**
   * 停止学习信号 - 纯前端版本通过ESP32 API停止
   */
  async stopLearning() {
    try {
      console.log('SignalManager: 用户主动停止学习信号');

      // 检查是否有未保存的信号
      if (this.learningState.hasUnsavedSignal) {
        const confirmed = confirm('有未保存的信号，确定要停止学习吗？未保存的信号将丢失。');
        if (!confirmed) {
          console.log('SignalManager: 用户取消停止学习');
          return;
        }
      }

      // 纯前端版本：通过ESP32 API停止学习
      // 修复：停止学习不需要参数
      const response = await this.esp32.post('/api/signals/learn/stop', {});

      if (response.success) {
        // 重置前端学习状态
        this.resetLearningState();
        this.hideLearningIndicator();
        this.updateLearningUI();

        this.handleSuccess('学习模式已停止', '停止学习');
      } else {
        throw new Error(response.error || '停止学习失败');
      }
    } catch (error) {
      console.error('SignalManager: 停止学习失败:', error);
      this.handleError(error, '停止学习');
    }
  }

  // 已删除自动停止学习方法 - 纯前端版本通过ESP32后端管理学习超时

  // 已删除学习状态清理方法 - 纯前端版本通过ESP32 API管理学习状态

  // 已删除任务暂停和恢复方法 - 纯前端版本不需要管理其他模块的任务状态

  /**
   * 启动学习状态监听 - 纯前端版本通过WebSocket监听ESP32学习结果
   */
  startLearningStatusMonitor() {
    console.log('SignalManager: 启动学习状态监听');

    // 纯前端版本：通过WebSocket监听ESP32学习结果
    // 实际的学习检测由ESP32后端处理
    if (this.esp32 && this.esp32.onMessage) {
      this.esp32.onMessage('signal.learned', (data) => {
        this.handleLearningResult(data);
      });

      this.esp32.onMessage('learning.error', (data) => {
        this.handleLearningError(data.error || '学习失败');
      });

      // 添加缺失的WebSocket事件监听
      this.esp32.onMessage('signal.sent', (data) => {
        this.handleSignalSent(data);
      });

      this.esp32.onMessage('signal.failed', (data) => {
        this.handleSignalFailed(data);
      });

      this.esp32.onMessage('system.status', (data) => {
        this.handleSystemStatus(data);
      });

      this.esp32.onMessage('esp32.connected', (data) => {
        this.handleESP32Connected(data);
      });
    }
  }

  /**
   * 处理学习结果 - 纯前端版本只处理UI更新
   */
  handleLearningResult(data) {
    console.log('SignalManager: 收到学习结果:', data);

    if (data.success && data.signal) {
      // 更新学习状态
      this.learningState.hasUnsavedSignal = true;
      this.learningState.currentSignalData = data.signal;

      // 显示保存对话框
      this.showSaveSignalDialog(data.signal);
    } else {
      this.handleLearningError(data.error || '学习失败');
    }
  }

  /**
   * 处理学习错误 - 纯前端版本只处理UI状态
   */
  handleLearningError(errorMessage) {
    console.error('SignalManager: 学习失败:', errorMessage);

    // 重置学习状态
    this.resetLearningState();
    this.hideLearningIndicator();
    this.updateLearningUI();

    // 显示错误信息
    this.handleError(new Error(errorMessage), '信号学习');
  }

  // 已删除所有定时器和活动检查逻辑 - 纯前端版本通过ESP32后端管理学习超时和状态

  /**
   * 处理信号发射成功事件
   */
  handleSignalSent(data) {
    console.log('SignalManager: 信号发射成功:', data);

    if (data.signalId) {
      // 更新信号统计
      const signal = this.signalStorage.getSignal(data.signalId);
      if (signal) {
        signal.lastSent = Date.now();
        signal.sentCount = (signal.sentCount || 0) + 1;
        this.signalStorage.updateSignal(signal);

        // 更新UI显示
        this.renderSignals();

        this.handleSuccess(`信号 "${signal.name}" 发射成功`, '信号发射');
      }
    }
  }

  /**
   * 处理信号发射失败事件
   */
  handleSignalFailed(data) {
    console.error('SignalManager: 信号发射失败:', data);

    const errorMsg = data.error || '信号发射失败';
    this.handleError(new Error(errorMsg), '信号发射');
  }

  /**
   * 处理系统状态更新事件
   */
  handleSystemStatus(data) {
    console.log('SignalManager: 系统状态更新:', data);

    // 可以在这里更新系统状态显示
    // 例如：连接状态、内存使用情况等
  }

  /**
   * 处理ESP32连接事件
   */
  handleESP32Connected(data) {
    console.log('SignalManager: ESP32已连接:', data);

    // ESP32连接后重新加载信号数据
    this.loadSignalsFromESP32();

    this.handleSuccess('ESP32设备已连接', '设备连接');
  }

  /**
   * 重置学习状态 - 纯前端版本只重置UI状态
   */
  resetLearningState() {
    this.learningState.isLearning = false;
    this.learningState.hasUnsavedSignal = false;
    this.learningState.currentSignalData = null;
    this.learningState.learningStartTime = 0;

    console.log('SignalManager: 学习状态已重置');
  }

  // 已删除ESP32学习指令发送方法 - 纯前端版本通过标准API接口与ESP32通信



  // 已删除所有模拟数据生成方法 - 纯前端版本只处理ESP32后端返回的真实数据

  // 已删除模拟学习过程 - 纯前端版本通过ESP32 WebSocket接收真实学习结果

  /**
   * 显示学习指示器
   */
  showLearningIndicator() {
    let indicator = $('#learningIndicator');
    if (!indicator) {
      // 创建学习指示器
      indicator = document.createElement('div');
      indicator.id = 'learningIndicator';
      indicator.className = 'learning-indicator';
      indicator.innerHTML = `
        <div class="learning-content">
          <h3>🔍 正在学习信号</h3>
          <p>请对准设备按下遥控器按钮</p>
          <div class="learning-spinner">
            <div class="spinner"></div>
          </div>
          <button class="cancel-learning-btn" data-action="cancel-learning">
            停止学习
          </button>
        </div>
      `;
      document.body.appendChild(indicator);

      // 添加事件监听器
      const cancelBtn = indicator.querySelector('.cancel-learning-btn');
      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          this.stopLearning();
        });
      }
    }
    indicator.style.display = 'flex';
  }

  /**
   * 隐藏学习指示器
   */
  hideLearningIndicator() {
    const indicator = $('#learningIndicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  /**
   * 更新学习UI状态
   */
  updateLearningUI() {
    const learnBtn = $('#learnSignalBtn');
    const learnBtnIcon = $('#learnBtnIcon');
    const learnBtnText = $('#learnBtnText');

    if (learnBtn && learnBtnIcon && learnBtnText) {
      if (this.learningState.isLearning) {
        // 学习中状态 - 显示停止按钮
        learnBtnIcon.textContent = '⏹️';
        learnBtnText.textContent = '停止学习';
        learnBtn.classList.remove('primary');
        learnBtn.classList.add('danger');
        learnBtn.title = '点击停止学习信号';
      } else {
        // 未学习状态 - 显示学习按钮
        learnBtnIcon.textContent = '🔍';
        learnBtnText.textContent = '学习信号';
        learnBtn.classList.remove('danger');
        learnBtn.classList.add('primary');
        learnBtn.title = '点击开始学习信号';
      }
    }
  }

  // 已删除本地存储保存方法 - 纯前端版本通过ESP32 API管理信号数据

  // 已删除自动保存方法 - 纯前端版本通过ESP32 API管理信号保存

  // 已删除模拟原始数据生成方法 - 纯前端版本只处理ESP32后端返回的真实数据

  /**
   * 保存学习到的信号 - 纯前端版本通过ESP32 API保存
   */
  async saveLearnedSignal() {
    try {
      // 获取表单数据
      const name = $('#learnedSignalName')?.value?.trim();
      const type = $('#learnedSignalType')?.value || 'other';
      const description = $('#learnedSignalDescription')?.value?.trim();

      if (!name) {
        this.handleError(new Error('信号名称不能为空'), '保存信号');
        return;
      }

      if (!this.learningState.currentSignalData) {
        this.handleError(new Error('没有可保存的信号数据'), '保存信号');
        window.R1System.hideModal();
        return;
      }

      // 纯前端版本：通过ESP32 API保存学习到的信号
      // 修复：将signalData转换为JSON字符串
      const response = await this.esp32.post('/api/signals/learn/save', {
        signalData: JSON.stringify(this.learningState.currentSignalData),
        name: name,
        type: type,
        description: description
      });

      if (response.success) {
        // 清理状态
        this.learningState.hasUnsavedSignal = false;
        this.learningState.currentSignalData = null;

        // 关闭模态框
        window.R1System.hideModal();

        // 重新加载信号列表
        await this.loadSignalsFromESP32();
        this.renderSignals();
        this.updateStats();

        this.handleSuccess(`信号 "${name}" 保存成功`, '信号保存');
      } else {
        throw new Error(response.error || '保存失败');
      }
    } catch (error) {
      console.error('SignalManager: 保存信号失败:', error);
      this.handleError(error, '信号保存');
    }
  }

  /**
   * 取消学习到的信号 - 纯前端版本简化处理
   */
  cancelLearnedSignal() {
    try {
      console.log('SignalManager: 用户取消信号保存');

      // 清理状态
      this.learningState.hasUnsavedSignal = false;
      this.learningState.currentSignalData = null;

      // 关闭模态框
      window.R1System.hideModal();

      this.handleSuccess('已取消信号保存', '取消保存');
    } catch (error) {
      console.error('SignalManager: 取消信号保存失败:', error);
      this.handleError(error, '取消保存');
    }
  }



  /**
   * 恢复未保存的信号
   */
  recoverUnsavedSignal() {
    try {
      const unsavedSignal = localStorage.getItem('signalManager_unsavedSignal');
      if (!unsavedSignal) {
        this.handleError(new Error('没有找到未保存的信号数据'), '信号恢复');
        return;
      }

      const signalData = JSON.parse(unsavedSignal);

      console.log('SignalManager: 开始恢复未保存信号，触发学习暂停:', signalData);

      // 关闭恢复对话框
      window.R1System.hideModal();

      // 纯前端版本：恢复信号时只设置UI状态

      // 设置学习状态（模拟学习模式）
      this.learningState.isLearning = true;
      this.learningState.currentSignalData = signalData;
      this.learningState.hasUnsavedSignal = true;
      this.learningState.learningStartTime = Date.now();
      this.learningState.lastActivityTime = Date.now();

      // 更新学习按钮状态
      this.updateLearningUI();

      // 显示编辑对话框
      this.showSignalEditDialog(signalData);

      console.log('SignalManager: 未保存信号已恢复，学习暂停已触发');

    } catch (error) {
      console.error('SignalManager: 恢复信号失败:', error);
      this.handleError(error, '信号恢复');
      localStorage.removeItem('signalManager_unsavedSignal');
    }
  }

  /**
   * 丢弃恢复的信号
   */
  discardRecoverySignal() {
    try {
      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');

      // 关闭对话框
      window.R1System.hideModal();

      this.handleSuccess('未保存的信号已丢弃', '丢弃信号');

      console.log('SignalManager: 未保存信号已丢弃');

    } catch (error) {
      console.error('SignalManager: 丢弃信号失败:', error);
      this.handleError(error, '丢弃信号');
    }
  }

  // 已删除模拟信号检测方法 - 纯前端版本通过ESP32 WebSocket接收真实信号数据

  // 已删除所有模拟频率和原始数据生成方法 - 纯前端版本只处理ESP32后端返回的真实数据

  // 已删除信号检测处理方法 - 纯前端版本通过WebSocket接收ESP32后端的学习结果

  /**
   * 显示保存信号对话框 - 纯前端版本简化UI
   */
  showSaveSignalDialog(signalData) {
    const modalContent = `
      <div class="modal-header">
        <h3>保存学习到的信号</h3>
      </div>
      <div class="modal-body">
        <div class="signal-preview">
          <h4>检测到的信号信息</h4>
          <div class="signal-data">
            <div class="data-item">
              <label>信号码:</label>
              <span class="data-value">${signalData.signalCode || 'N/A'}</span>
            </div>
            <div class="data-item">
              <label>协议:</label>
              <span class="data-value">${signalData.protocol || 'NEC'}</span>
            </div>
            <div class="data-item">
              <label>频率:</label>
              <span class="data-value">${signalData.frequency || '38000'} Hz</span>
            </div>
          </div>
        </div>

        <form id="learnedSignalForm">
          <div class="form-group">
            <label for="learnedSignalName">信号名称 *</label>
            <input type="text" id="learnedSignalName" class="form-input"
                   placeholder="请输入信号名称" required>
          </div>
          <div class="form-group">
            <label for="learnedSignalType">信号类型</label>
            <select id="learnedSignalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other" selected>其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="learnedSignalDescription">描述</label>
            <textarea id="learnedSignalDescription" class="form-input" rows="3"
                      placeholder="请输入信号描述（可选）"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="cancel-learning">取消</button>
          <button type="button" class="btn primary" data-action="save-learned-signal">保存信号</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }

  // 已删除模态框事件监听器方法 - 纯前端版本使用统一的事件路由系统

  // 已删除自动保存定时器和倒计时显示 - 纯前端版本通过ESP32后端管理保存逻辑

  /**
   * 初始化文件导入处理器
   */
  initFileImportHandlers() {
    const fileInput = $('#importFileInput');
    const confirmBtn = $('#confirmFileImport');
    const previewArea = $('#importPreview');
    const previewContent = $('#previewContent');

    if (!fileInput || !confirmBtn) return;

    fileInput.addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (!file) {
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
        return;
      }

      try {
        const content = await this.readFileContent(file);
        const parsedData = await this.parseImportFile(content, file.type);

        if (parsedData && parsedData.length > 0) {
          this.displayImportPreview(parsedData, previewContent);
          previewArea.style.display = 'block';
          confirmBtn.disabled = false;

          // 绑定确认导入事件
          confirmBtn.onclick = () => this.executeFileImport(parsedData);
        } else {
          throw new Error('文件中没有找到有效的信号数据');
        }
      } catch (error) {
        this.handleError(error, '文件解析');
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
      }
    });
  }

  /**
   * 初始化文本导入处理器
   */
  initTextImportHandlers() {
    const textInput = $('#importTextInput');
    const previewBtn = $('#previewTextImport');
    const confirmBtn = $('#confirmTextImport');
    const previewArea = $('#textImportPreview');
    const previewContent = $('#textPreviewContent');

    if (!textInput || !previewBtn || !confirmBtn) return;

    previewBtn.addEventListener('click', () => {
      const textContent = textInput.value.trim();
      if (!textContent) {
        this.handleError(new Error('请输入信号数据'), '文本解析');
        return;
      }

      try {
        const parsedData = this.parseTextImport(textContent);
        if (parsedData && parsedData.length > 0) {
          this.displayImportPreview(parsedData, previewContent);
          previewArea.style.display = 'block';
          confirmBtn.disabled = false;

          // 绑定确认导入事件
          confirmBtn.onclick = () => this.executeTextImport(parsedData);
        } else {
          throw new Error('没有找到有效的信号数据');
        }
      } catch (error) {
        this.handleError(error, '文本解析');
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
      }
    });
  }

  /**
   * 读取文件内容
   */
  readFileContent(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'UTF-8');
    });
  }

  /**
   * 上传文件到ESP32后端进行解析和导入
   */
  async parseImportFile(content, fileType) {
    try {
      // 纯前端版本：将文件内容上传到ESP32后端进行解析
      const response = await this.esp32.post('/api/signals/import', {
        content: content,
        fileType: fileType
      });

      if (response.success && response.data) {
        // 修复：后端返回的数据结构可能是 {signals: [...]} 或直接是数组
        const signals = response.data.signals || response.data;
        console.log(`✅ ESP32后端解析成功，返回 ${signals.length} 个信号`);
        return signals;
      } else {
        throw new Error(response.error || '后端解析失败');
      }
    } catch (error) {
      console.error('❌ 文件上传解析错误:', error);
      throw new Error(`文件解析失败: ${error.message}`);
    }
  }

  /**
   * 上传文本到ESP32后端进行解析
   */
  async parseTextImport(textContent) {
    try {
      // 纯前端版本：将文本内容上传到ESP32后端进行解析
      const response = await this.esp32.post('/api/signals/import/text', {
        content: textContent,
        defaultType: $('#defaultSignalType')?.value || 'other'
      });

      if (response.success && response.data) {
        // 修复：后端返回的数据结构可能是 {signals: [...]} 或直接是数组
        const signals = response.data.signals || response.data;
        console.log(`✅ ESP32后端文本解析成功，返回 ${signals.length} 个信号`);
        return signals;
      } else {
        throw new Error(response.error || '后端文本解析失败');
      }
    } catch (error) {
      console.error('❌ 文本上传解析错误:', error);
      throw new Error(`文本解析失败: ${error.message}`);
    }
  }

  // 已删除信号验证方法 - 纯前端版本信任ESP32后端返回的验证结果

  /**
   * 显示导入预览 - 纯前端UI功能
   */
  displayImportPreview(signals, container) {
    if (!container) return;

    const previewHTML = `
      <div class="import-summary">
        <p>将导入 <strong>${signals.length}</strong> 个信号</p>
      </div>
      <div class="import-signals-list">
        ${signals.map((signal) => `
          <div class="import-signal-item">
            <div class="signal-info">
              <div class="signal-code">${signal.signalCode || 'N/A'}</div>
              <div class="signal-details">
                <span class="signal-name">${signal.name || '未命名'}</span>
                <span class="signal-protocol">${signal.protocol || 'NEC'}</span>
              </div>
            </div>
            <div class="signal-description">${signal.description || '无描述'}</div>
          </div>
        `).join('')}
      </div>
    `;

    container.innerHTML = previewHTML;
  }

  /**
   * 执行文件导入 - 纯前端版本通过ESP32 API导入
   */
  async executeFileImport(signals) {
    try {
      // 纯前端版本：将解析后的信号数据发送到ESP32后端进行导入
      // 修复：将signals数组转换为JSON字符串
      const response = await this.esp32.post('/api/signals/import/execute', {
        signals: JSON.stringify(signals)
      });

      if (response.success) {
        // 导入成功，重新加载信号列表
        await this.loadSignalsFromESP32();

        // 更新UI
        this.renderSignals();
        this.updateStats();

        // 关闭模态框
        window.R1System.hideModal();

        // 显示结果
        const message = `文件导入完成: 成功 ${response.data.successCount || 0} 个，跳过 ${response.data.skipCount || 0} 个`;
        this.handleSuccess(message, '文件导入');
      } else {
        throw new Error(response.error || '导入失败');
      }
    } catch (error) {
      this.handleError(error, '文件导入');
    }
  }

  /**
   * 执行文本导入 - 纯前端版本通过ESP32 API导入
   */
  async executeTextImport(signals) {
    try {
      // 纯前端版本：将解析后的信号数据发送到ESP32后端进行导入
      // 修复：将signals数组转换为JSON字符串
      const response = await this.esp32.post('/api/signals/import/text/execute', {
        signals: JSON.stringify(signals)
      });

      if (response.success) {
        // 导入成功，重新加载信号列表
        await this.loadSignalsFromESP32();

        // 更新UI
        this.renderSignals();
        this.updateStats();

        // 关闭模态框
        window.R1System.hideModal();

        // 显示结果
        const message = `文本导入完成: 成功 ${response.data.successCount || 0} 个，跳过 ${response.data.skipCount || 0} 个`;
        this.handleSuccess(message, '文本导入');
      } else {
        throw new Error(response.error || '文本导入失败');
      }
    } catch (error) {
      this.handleError(error, '文本导入');
    }
  }

  // ==================== 事件处理方法 ====================

  /**
   * 处理信号请求（来自控制模块）
   */
  handleSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到信号请求 - 来源: ${data.source}, 目的: ${data.purpose}`);

      // 获取所有信号
      const allSignals = Array.from(this.signals.values());

      console.log(`📤 SignalManager: 返回 ${allSignals.length} 个信号`);

      // 调用回调函数返回信号
      if (data.callback && typeof data.callback === 'function') {
        console.log(`✅ SignalManager: 调用回调函数`);
        data.callback(allSignals);
      } else {
        console.error(`❌ SignalManager: 回调函数无效`, {
          hasCallback: !!data.callback,
          callbackType: typeof data.callback,
          dataKeys: Object.keys(data)
        });
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理信号请求失败:', error);
      this.performance.errorCount++;

      // 即使出错也要调用回调，避免控制模块无限等待
      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理选中信号请求（来自控制模块）
   */
  handleSelectedSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到选中信号请求 - 来源: ${data.source}`);

      // 获取选中的信号
      const selectedSignals = Array.from(this.selectedSignals)
        .map(id => this.signals.get(id))
        .filter(signal => signal !== undefined);

      console.log(`📤 SignalManager: 返回 ${selectedSignals.length} 个选中信号`);

      // 调用回调函数返回信号
      if (data.callback && typeof data.callback === 'function') {
        data.callback(selectedSignals);
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理选中信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理选中信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理信号计数请求（来自状态显示模块）
   */
  handleSignalCountRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到信号计数请求 - 来源: ${data.source || 'StatusDisplay'}`);

      const signalCount = this.signals.size;
      console.log(`📤 SignalManager: 返回信号数量: ${signalCount}`);

      // 通过回调返回结果
      if (data.callback && typeof data.callback === 'function') {
        data.callback(signalCount);
      }

      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号计数请求';
    } catch (error) {
      this.performance.errorCount++;
      console.error('SignalManager: 处理信号计数请求失败:', error);
      if (data.callback && typeof data.callback === 'function') {
        data.callback(0);
      }
    }
  }

  /**
   * 处理按ID查询信号请求（来自控制模块）
   */
  handleSignalRequestByIds(data) {
    try {
      const { signalIds, source, callback } = data;
      console.log(`📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ${source}, IDs: ${signalIds}`);

      // 根据ID获取信号
      const requestedSignals = signalIds
        .map(id => {
          const signal = this.signals.get(id);
          console.log(`🔍 [DEBUG] 查找信号 ID: ${id}, 找到: ${signal ? signal.name : '未找到'}`);
          return signal;
        })
        .filter(signal => signal !== undefined);

      console.log(`📤 [DEBUG] SignalManager: 返回 ${requestedSignals.length} 个指定信号，信号名称: ${requestedSignals.map(s => s.name)}`);

      // 调用回调函数返回信号
      if (callback && typeof callback === 'function') {
        console.log(`🔄 [DEBUG] SignalManager: 调用回调函数，传递 ${requestedSignals.length} 个信号`);
        callback(requestedSignals);
      } else {
        console.warn(`⚠️ [DEBUG] SignalManager: 没有回调函数或回调函数无效`);
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理按ID查询信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理按ID查询信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理发送信号请求（来自控制模块）
   */
  handleSendSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到发送信号请求 - 信号ID: ${data.signalId}`);

      // 发送信号
      this.sendSignal(data.signalId).then(success => {
        console.log(`📤 SignalManager: 信号发送结果: ${success}`);

        // 调用回调函数返回结果
        if (data.callback && typeof data.callback === 'function') {
          data.callback(success);
        }
      }).catch(error => {
        console.error('❌ SignalManager: 发送信号失败:', error);

        if (data.callback && typeof data.callback === 'function') {
          data.callback(false);
        }
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理发送信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理发送信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback(false);
      }
    }
  }

  /**
   * 处理批量发射响应（来自控制模块）
   */
  handleBatchEmitResponse(data) {
    try {
      console.log(`📨 SignalManager: 收到批量发射响应:`, data);

      if (data.success) {
        if (data.queued) {
          this.handleSuccess('批量发射已加入队列', '批量发射');
        } else {
          this.handleSuccess('批量发射已开始', '批量发射');
        }
      } else {
        this.handleError(new Error(data.error || '批量发射失败'), '批量发射');
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理批量发射响应';

    } catch (error) {
      console.error('SignalManager: 处理批量发射响应失败:', error);
      this.performance.errorCount++;
    }
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      // 停止学习
      this.stopLearning();

      // 清理定时器
      if (this.autoSaveTimeout) {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = null;
      }
      if (this.countdownInterval) {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }

      // 清理事件监听器
      this.eventBus.off('signal.request.all');
      this.eventBus.off('signal.request.by-ids');
      this.eventBus.off('signal.send.request');
      this.eventBus.off('signal.edit.request');
      this.eventBus.off('signal.import.file.request');
      this.eventBus.off('signal.import.text.request');
      this.eventBus.off('signal.learn.form.submit');
      this.eventBus.off('signal.edit.form.submit');
      this.eventBus.off('signal.validation.request');

      // 清理数据
      this.signals.clear();
      this.selectedSignals.clear();

      console.log('SignalManager: 模块已销毁');
    } catch (error) {
      console.error('SignalManager: 销毁模块失败:', error);
    }
  }
}

// 导出到全局作用域 - 符合R1架构标准
if (typeof window !== 'undefined') {
  window.SignalManager = SignalManager;
  console.log('✅ SignalManager 类已导出到全局作用域 (仅支持标准格式)');
}
# ESP_Async_WebServer MD5.H 兼容性自动修复脚本
# 解决 ESP32 Arduino Core 2.0.17 兼容性问题

Write-Host "=== ESP_Async_WebServer MD5.H 兼容性修复工具 ===" -ForegroundColor Green
Write-Host ""

# 检测Arduino库目录
$path1 = "$env:USERPROFILE\Documents\Arduino\libraries\ESP_Async_WebServer\src\WebAuthentication.cpp"
$path2 = "$env:USERPROFILE\Documents\Arduino\libraries\ESPAsyncWebServer\src\WebAuthentication.cpp"
$path3 = "$env:USERPROFILE\Documents\Arduino\libraries\ESP32AsyncWebServer\src\WebAuthentication.cpp"

$targetFile = $null
if (Test-Path $path1) {
    $targetFile = $path1
    Write-Host "✓ 找到目标文件: $path1" -ForegroundColor Green
} elseif (Test-Path $path2) {
    $targetFile = $path2
    Write-Host "✓ 找到目标文件: $path2" -ForegroundColor Green
} elseif (Test-Path $path3) {
    $targetFile = $path3
    Write-Host "✓ 找到目标文件: $path3" -ForegroundColor Green
}

if (-not $targetFile) {
    Write-Host "❌ 错误: 未找到 WebAuthentication.cpp 文件" -ForegroundColor Red
    Write-Host "请确保已安装 ESP_Async_WebServer 库" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "可能的安装位置:" -ForegroundColor Yellow
    Write-Host "  - $path1" -ForegroundColor Gray
    Write-Host "  - $path2" -ForegroundColor Gray
    Write-Host "  - $path3" -ForegroundColor Gray
    exit 1
}

# 备份原文件
$backupFile = $targetFile + ".backup"
if (-not (Test-Path $backupFile)) {
    Copy-Item $targetFile $backupFile
    Write-Host "✓ 已创建备份文件: $backupFile" -ForegroundColor Green
}

# 读取文件内容
$content = Get-Content $targetFile -Raw

# 检查是否已经修复
if ($content -match '#include "mbedtls/md5\.h"') {
    Write-Host "✓ 文件已经修复，无需重复操作" -ForegroundColor Green
    exit 0
}

# 执行修复
Write-Host "🔧 正在修复 md5.h 引用..." -ForegroundColor Yellow

# 替换 #include "md5.h" 为 mbedtls 版本
$newContent = $content -replace '#include "md5\.h"', '#include "mbedtls/md5.h"' + "`n" + '#include "mbedtls/compat-2.x.h"'

# 写入修复后的内容
Set-Content $targetFile $newContent -NoNewline

Write-Host "✓ 修复完成!" -ForegroundColor Green
Write-Host ""
Write-Host "修复详情:" -ForegroundColor Cyan
Write-Host "  - 文件: $targetFile" -ForegroundColor Gray
Write-Host "  - 替换: #include `"md5.h`" → #include `"mbedtls/md5.h`"" -ForegroundColor Gray
Write-Host "  - 添加: #include `"mbedtls/compat-2.x.h`"" -ForegroundColor Gray
Write-Host ""
Write-Host "现在可以重新编译您的 ESP32 项目了!" -ForegroundColor Green

# 验证修复
Write-Host ""
Write-Host "=== 验证修复结果 ===" -ForegroundColor Cyan
$verifyContent = Get-Content $targetFile | Select-String -Pattern "mbedtls"
if ($verifyContent) {
    Write-Host "✓ 验证成功: 已正确引用 mbedtls 库" -ForegroundColor Green
    foreach ($line in $verifyContent) {
        Write-Host "  $line" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ 验证失败: 修复可能未成功" -ForegroundColor Red
}

Write-Host ""
Write-Host "如果需要恢复原文件，请运行:" -ForegroundColor Yellow
Write-Host "  Copy-Item `"$backupFile`" `"$targetFile`"" -ForegroundColor Gray

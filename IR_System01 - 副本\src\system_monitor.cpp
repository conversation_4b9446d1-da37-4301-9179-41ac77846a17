/*
 * System Monitor Implementation - ESP32-S3 IR Control System
 * Monitors system health, performance, and resource usage
 * Provides comprehensive system diagnostics and alerts
 */

#include "system_monitor.h"
#include "data_manager.h"
#include "ir_controller.h"
#include "task_manager.h"
#include "websocket_manager.h"
#include <WiFi.h>
#include <algorithm>

SystemMonitor::SystemMonitor() {
    dataManager = nullptr;
    irController = nullptr;
    taskManager = nullptr;
    wsManager = nullptr;
    
    m_isMonitoring = false;
    lastUpdate = 0;
    updateInterval = DEFAULT_UPDATE_INTERVAL;
    
    lowMemoryThreshold = DEFAULT_LOW_MEMORY_THRESHOLD;
    highCpuThreshold = DEFAULT_HIGH_CPU_THRESHOLD;
    lowWifiThreshold = DEFAULT_LOW_WIFI_THRESHOLD;
    
    // Clear callbacks
    onHealthChanged = nullptr;
    onAlert = nullptr;
    onMetricsUpdated = nullptr;
}

SystemMonitor::~SystemMonitor() {
    stopMonitoring();
}

bool SystemMonitor::initialize() {
    Serial.println("Initializing System Monitor...");
    
    Serial.printf("✅ System Monitor initialized - Update interval: %lu ms\n", updateInterval);
    Serial.printf("🔧 Thresholds - Memory: %u bytes, CPU: %.1f%%, WiFi: %d dBm\n", 
                 lowMemoryThreshold, highCpuThreshold, lowWifiThreshold);
    
    return true;
}

// ==================== Module Reference Setters ====================

void SystemMonitor::setDataManager(DataManager* dm) {
    dataManager = dm;
    Serial.println("🔗 SystemMonitor: DataManager reference set");
}

void SystemMonitor::setIRController(IRController* ir) {
    irController = ir;
    Serial.println("🔗 SystemMonitor: IRController reference set");
}

void SystemMonitor::setTaskManager(TaskManager* tm) {
    taskManager = tm;
    Serial.println("🔗 SystemMonitor: TaskManager reference set");
}

void SystemMonitor::setWebSocketManager(WebSocketManager* wsm) {
    wsManager = wsm;
    Serial.println("🔗 SystemMonitor: WebSocketManager reference set");
}

void SystemMonitor::handleLoop() {
    if (!m_isMonitoring) {
        return;
    }
    
    unsigned long currentTime = millis();
    if (currentTime - lastUpdate >= updateInterval) {
        // Collect current metrics
        SystemMetrics metrics = collectMetrics();
        
        // Check thresholds and trigger alerts
        checkThresholds(metrics);
        
        // Add to history
        addToHistory(metrics);
        
        // Trigger callback
        if (onMetricsUpdated) {
            onMetricsUpdated(metrics);
        }

        // 发送系统状态到WebSocket客户端 (前端需要)
        if (wsManager) {
            DynamicJsonDocument statusUpdate = getSystemStatus();
            wsManager->broadcastMessage("system_status_update", statusUpdate);
        }

        lastUpdate = currentTime;
    }
}

void SystemMonitor::startMonitoring() {
    if (!m_isMonitoring) {
        m_isMonitoring = true;
        lastUpdate = 0; // Force immediate update
        
        Serial.println("📊 System monitoring started");
        
        // Perform initial system check
        performSystemCheck();
    }
}

void SystemMonitor::stopMonitoring() {
    if (m_isMonitoring) {
        m_isMonitoring = false;
        Serial.println("📊 System monitoring stopped");
    }
}

// ==================== Metrics Collection ====================

SystemMetrics SystemMonitor::getCurrentMetrics() {
    return collectMetrics();
}

std::vector<SystemMetrics> SystemMonitor::getMetricsHistory() {
    return metricsHistory;
}

DynamicJsonDocument SystemMonitor::getSystemStatus() {
    SystemMetrics metrics = collectMetrics();
    
    DynamicJsonDocument status(1024);
    
    // Basic system info
    status["uptime"] = metrics.uptime;
    status["timestamp"] = metrics.timestamp;
    status["health"] = static_cast<int>(metrics.health);
    status["health_text"] = getHealthString(metrics.health);
    
    // Memory info
    status["memory"]["free"] = metrics.freeHeap;
    status["memory"]["total"] = metrics.totalHeap;
    status["memory"]["usage_percent"] = metrics.memoryUsage;
    status["memory"]["min_free"] = metrics.minFreeHeap;
    
    // Performance
    status["performance"]["cpu_usage"] = metrics.cpuUsage;
    
    // Network
    status["network"]["wifi_connected"] = metrics.wifiConnected;
    status["network"]["wifi_rssi"] = metrics.wifiRSSI;
    
    // Hardware
    status["hardware"]["ir_ready"] = metrics.irHardwareReady;
    
    // Application
    status["application"]["active_clients"] = metrics.activeClients;
    status["application"]["total_signals"] = metrics.totalSignals;
    status["application"]["active_tasks"] = metrics.activeTasks;
    
    return status;
}

DynamicJsonDocument SystemMonitor::getDetailedStatus() {
    DynamicJsonDocument detailed(2048);
    
    // Include basic status
    detailed = getSystemStatus();
    
    // Add detailed system information
    detailed["system_info"] = getSystemInfo();
    detailed["hardware_info"] = getHardwareInfo();
    detailed["network_info"] = getNetworkInfo();
    detailed["performance_report"] = getPerformanceReport();
    
    return detailed;
}

// ==================== Health Status ====================

SystemHealth SystemMonitor::getCurrentHealth() {
    SystemMetrics metrics = collectMetrics();
    return calculateHealth(metrics);
}

String SystemMonitor::getHealthString(SystemHealth health) {
    switch (health) {
        case SystemHealth::EXCELLENT: return "Excellent";
        case SystemHealth::GOOD: return "Good";
        case SystemHealth::WARNING: return "Warning";
        case SystemHealth::CRITICAL: return "Critical";
        case SystemHealth::ERROR: return "Error";
        default: return "Unknown";
    }
}

// ==================== Configuration ====================

void SystemMonitor::setUpdateInterval(unsigned long intervalMs) {
    updateInterval = intervalMs;
    Serial.printf("🔧 Monitor update interval set to: %lu ms\n", intervalMs);
}

void SystemMonitor::setLowMemoryThreshold(uint32_t threshold) {
    lowMemoryThreshold = threshold;
    Serial.printf("🔧 Low memory threshold set to: %u bytes\n", threshold);
}

void SystemMonitor::setHighCpuThreshold(float threshold) {
    highCpuThreshold = threshold;
    Serial.printf("🔧 High CPU threshold set to: %.1f%%\n", threshold);
}

void SystemMonitor::setLowWifiThreshold(int threshold) {
    lowWifiThreshold = threshold;
    Serial.printf("🔧 Low WiFi threshold set to: %d dBm\n", threshold);
}

// ==================== Event Callback Setters ====================

void SystemMonitor::setOnHealthChanged(std::function<void(SystemHealth, const String&)> callback) {
    onHealthChanged = callback;
    Serial.println("📊 Health changed callback set");
}

void SystemMonitor::setOnAlert(std::function<void(const String&)> callback) {
    onAlert = callback;
    Serial.println("📊 Alert callback set");
}

// ==================== System Information ====================

DynamicJsonDocument SystemMonitor::getSystemInfo() {
    DynamicJsonDocument info(512);
    
    info["chip_model"] = ESP.getChipModel();
    info["chip_revision"] = ESP.getChipRevision();
    info["chip_cores"] = ESP.getChipCores();
    info["cpu_freq_mhz"] = ESP.getCpuFreqMHz();
    info["flash_size"] = ESP.getFlashChipSize();
    info["flash_speed"] = ESP.getFlashChipSpeed();
    info["sdk_version"] = ESP.getSdkVersion();
    info["arduino_version"] = ARDUINO;
    
    return info;
}

DynamicJsonDocument SystemMonitor::getHardwareInfo() {
    DynamicJsonDocument hardware(256);
    
    hardware["ir_transmitter_pin"] = 21;
    hardware["ir_receiver_pin"] = 14;
    hardware["ir_hardware_ready"] = checkIRHardware();
    hardware["psram_size"] = ESP.getPsramSize();
    hardware["psram_free"] = ESP.getFreePsram();
    
    return hardware;
}

DynamicJsonDocument SystemMonitor::getNetworkInfo() {
    DynamicJsonDocument network(256);
    
    network["wifi_connected"] = checkWiFiConnection();
    
    if (WiFi.status() == WL_CONNECTED) {
        network["wifi_ssid"] = WiFi.SSID();
        network["wifi_ip"] = WiFi.localIP().toString();
        network["wifi_rssi"] = WiFi.RSSI();
        network["wifi_channel"] = WiFi.channel();
        network["wifi_mac"] = WiFi.macAddress();
    }
    
    if (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA) {
        network["ap_ip"] = WiFi.softAPIP().toString();
        network["ap_clients"] = WiFi.softAPgetStationNum();
    }
    
    return network;
}

// ==================== Performance Analysis ====================

float SystemMonitor::getAverageMemoryUsage(int samples) {
    if (metricsHistory.empty()) {
        return 0.0;
    }
    
    int count = min(samples, static_cast<int>(metricsHistory.size()));
    float total = 0.0;
    
    for (int i = metricsHistory.size() - count; i < metricsHistory.size(); i++) {
        total += metricsHistory[i].memoryUsage;
    }
    
    return total / count;
}

float SystemMonitor::getAverageCpuUsage(int samples) {
    if (metricsHistory.empty()) {
        return 0.0;
    }
    
    int count = min(samples, static_cast<int>(metricsHistory.size()));
    float total = 0.0;
    
    for (int i = metricsHistory.size() - count; i < metricsHistory.size(); i++) {
        total += metricsHistory[i].cpuUsage;
    }
    
    return total / count;
}

DynamicJsonDocument SystemMonitor::getPerformanceReport() {
    DynamicJsonDocument report(512);
    
    report["avg_memory_usage_10"] = getAverageMemoryUsage(10);
    report["avg_memory_usage_60"] = getAverageMemoryUsage(60);
    report["avg_cpu_usage_10"] = getAverageCpuUsage(10);
    report["avg_cpu_usage_60"] = getAverageCpuUsage(60);
    report["history_size"] = metricsHistory.size();
    report["monitoring_duration"] = m_isMonitoring ? (millis() - lastUpdate) : 0;
    
    return report;
}

// ==================== Maintenance ====================

void SystemMonitor::clearHistory() {
    metricsHistory.clear();
    Serial.println("📊 Metrics history cleared");
}

void SystemMonitor::resetCounters() {
    clearHistory();
    Serial.println("📊 Monitor counters reset");
}

bool SystemMonitor::performSystemCheck() {
    Serial.println("🔍 Performing system check...");
    
    bool allGood = true;
    
    // Check memory
    uint32_t freeHeap = getFreeHeap();
    if (freeHeap < lowMemoryThreshold) {
        triggerAlert("Low memory warning: " + formatMemorySize(freeHeap) + " free");
        allGood = false;
    }
    
    // Check WiFi
    if (checkWiFiConnection()) {
        int rssi = getWiFiRSSI();
        if (rssi < lowWifiThreshold) {
            triggerAlert("Weak WiFi signal: " + String(rssi) + " dBm");
            allGood = false;
        }
    } else {
        triggerAlert("WiFi not connected");
        allGood = false;
    }
    
    // Check IR hardware
    if (!checkIRHardware()) {
        triggerAlert("IR hardware not ready");
        allGood = false;
    }
    
    if (allGood) {
        Serial.println("✅ System check passed");
    } else {
        Serial.println("⚠️  System check found issues");
    }
    
    return allGood;
}

// ==================== Private Methods ====================

SystemMetrics SystemMonitor::collectMetrics() {
    SystemMetrics metrics;

    // Time and uptime
    metrics.timestamp = millis();
    metrics.uptime = metrics.timestamp;

    // Memory information
    metrics.freeHeap = getFreeHeap();
    metrics.totalHeap = getTotalHeap();
    metrics.minFreeHeap = getMinFreeHeap();
    metrics.memoryUsage = calculateMemoryUsage();

    // Performance
    metrics.cpuUsage = calculateCpuUsage();

    // Network status
    metrics.wifiConnected = checkWiFiConnection();
    metrics.wifiRSSI = getWiFiRSSI();

    // Hardware status
    metrics.irHardwareReady = checkIRHardware();

    // Application status
    metrics.activeClients = getActiveClientCount();
    metrics.totalSignals = getTotalSignalCount();
    metrics.activeTasks = getActiveTaskCount();

    // Calculate overall health
    metrics.health = calculateHealth(metrics);

    return metrics;
}

SystemHealth SystemMonitor::calculateHealth(const SystemMetrics& metrics) {
    int healthScore = 5; // Start with excellent

    // Check memory usage
    if (metrics.memoryUsage > 90.0) {
        healthScore = min(healthScore, 1); // Error
    } else if (metrics.memoryUsage > 80.0) {
        healthScore = min(healthScore, 2); // Critical
    } else if (metrics.memoryUsage > 70.0) {
        healthScore = min(healthScore, 3); // Warning
    }

    // Check CPU usage
    if (metrics.cpuUsage > 95.0) {
        healthScore = min(healthScore, 1); // Error
    } else if (metrics.cpuUsage > 85.0) {
        healthScore = min(healthScore, 2); // Critical
    } else if (metrics.cpuUsage > 75.0) {
        healthScore = min(healthScore, 3); // Warning
    }

    // Check WiFi signal
    if (metrics.wifiConnected) {
        if (metrics.wifiRSSI < -80) {
            healthScore = min(healthScore, 3); // Warning
        } else if (metrics.wifiRSSI < -90) {
            healthScore = min(healthScore, 2); // Critical
        }
    } else {
        healthScore = min(healthScore, 2); // Critical - no WiFi
    }

    // Check hardware
    if (!metrics.irHardwareReady) {
        healthScore = min(healthScore, 2); // Critical
    }

    return static_cast<SystemHealth>(healthScore);
}

void SystemMonitor::checkThresholds(const SystemMetrics& metrics) {
    static SystemHealth lastHealth = SystemHealth::GOOD;

    // Check if health changed
    if (metrics.health != lastHealth) {
        String reason = "Health changed from " + getHealthString(lastHealth) +
                       " to " + getHealthString(metrics.health);

        triggerHealthChange(metrics.health, reason);
        lastHealth = metrics.health;
    }

    // Check specific thresholds
    if (metrics.freeHeap < lowMemoryThreshold) {
        triggerAlert("Low memory: " + formatMemorySize(metrics.freeHeap) + " free");
    }

    if (metrics.cpuUsage > highCpuThreshold) {
        triggerAlert("High CPU usage: " + formatPercentage(metrics.cpuUsage));
    }

    if (metrics.wifiConnected && metrics.wifiRSSI < lowWifiThreshold) {
        triggerAlert("Weak WiFi signal: " + String(metrics.wifiRSSI) + " dBm");
    }
}

void SystemMonitor::addToHistory(const SystemMetrics& metrics) {
    metricsHistory.push_back(metrics);

    // Limit history size
    if (metricsHistory.size() > MAX_HISTORY_SIZE) {
        metricsHistory.erase(metricsHistory.begin());
    }
}

// ==================== Hardware Monitoring ====================

uint32_t SystemMonitor::getFreeHeap() {
    return ESP.getFreeHeap();
}

uint32_t SystemMonitor::getTotalHeap() {
    return ESP.getHeapSize();
}

uint32_t SystemMonitor::getMinFreeHeap() {
    return ESP.getMinFreeHeap();
}

float SystemMonitor::calculateMemoryUsage() {
    uint32_t total = getTotalHeap();
    uint32_t free = getFreeHeap();

    if (total == 0) return 0.0;

    return ((float)(total - free) / total) * 100.0;
}

float SystemMonitor::calculateCpuUsage() {
    // Simple CPU usage estimation based on loop timing
    static unsigned long lastTime = 0;
    static unsigned long lastIdleTime = 0;

    unsigned long currentTime = millis();
    unsigned long idleTime = currentTime; // Simplified for now

    if (lastTime == 0) {
        lastTime = currentTime;
        lastIdleTime = idleTime;
        return 0.0;
    }

    unsigned long timeDiff = currentTime - lastTime;
    unsigned long idleDiff = idleTime - lastIdleTime;

    lastTime = currentTime;
    lastIdleTime = idleTime;

    if (timeDiff == 0) return 0.0;

    float usage = 100.0 - ((float)idleDiff / timeDiff * 100.0);
    return max(0.0f, min(100.0f, usage));
}

// ==================== Network Monitoring ====================

bool SystemMonitor::checkWiFiConnection() {
    return WiFi.status() == WL_CONNECTED;
}

int SystemMonitor::getWiFiRSSI() {
    if (checkWiFiConnection()) {
        return WiFi.RSSI();
    }
    return 0;
}

// ==================== Application Monitoring ====================

int SystemMonitor::getActiveClientCount() {
    if (wsManager) {
        return wsManager->getClientCount();
    }
    return 0;
}

int SystemMonitor::getTotalSignalCount() {
    if (dataManager) {
        return dataManager->getSignalCount();
    }
    return 0;
}

int SystemMonitor::getActiveTaskCount() {
    if (taskManager) {
        return taskManager->getActiveTaskCount();
    }
    return 0;
}

bool SystemMonitor::checkIRHardware() {
    if (irController) {
        return irController->isHardwareReady();
    }
    return false;
}

// ==================== Alert System ====================

void SystemMonitor::triggerAlert(const String& message) {
    Serial.printf("⚠️  ALERT: %s\n", message.c_str());

    if (onAlert) {
        onAlert(message);
    }
}

void SystemMonitor::triggerHealthChange(SystemHealth newHealth, const String& reason) {
    Serial.printf("🏥 Health changed to %s: %s\n", getHealthString(newHealth).c_str(), reason.c_str());

    if (onHealthChanged) {
        onHealthChanged(newHealth, reason);
    }
}

// ==================== Helper Methods ====================

String SystemMonitor::formatUptime(unsigned long uptime) {
    unsigned long seconds = uptime / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;
    unsigned long days = hours / 24;

    String result = "";
    if (days > 0) result += String(days) + "d ";
    if (hours % 24 > 0) result += String(hours % 24) + "h ";
    if (minutes % 60 > 0) result += String(minutes % 60) + "m ";
    result += String(seconds % 60) + "s";

    return result;
}

String SystemMonitor::formatMemorySize(uint32_t bytes) {
    if (bytes < 1024) {
        return String(bytes) + " B";
    } else if (bytes < 1024 * 1024) {
        return String(bytes / 1024.0, 1) + " KB";
    } else {
        return String(bytes / (1024.0 * 1024.0), 1) + " MB";
    }
}

String SystemMonitor::formatPercentage(float percentage) {
    return String(percentage, 1) + "%";
}

bool SystemMonitor::isThresholdExceeded(float value, float threshold) {
    return value > threshold;
}

float SystemMonitor::calculateMovingAverage(const std::vector<float>& values, int samples) {
    if (values.empty()) return 0.0;

    int count = min(samples, static_cast<int>(values.size()));
    float total = 0.0;

    for (int i = values.size() - count; i < values.size(); i++) {
        total += values[i];
    }

    return total / count;
}

void SystemMonitor::updatePerformanceMetrics(SystemMetrics& metrics) {
    // Additional performance metric calculations can be added here
}

// ==================== SystemMetrics Implementation ====================

DynamicJsonDocument SystemMetrics::toJson() const {
    DynamicJsonDocument doc(1024);

    doc["uptime"] = uptime;
    doc["timestamp"] = timestamp;
    doc["free_heap"] = freeHeap;
    doc["total_heap"] = totalHeap;
    doc["min_free_heap"] = minFreeHeap;
    doc["memory_usage"] = memoryUsage;
    doc["cpu_usage"] = cpuUsage;
    doc["wifi_connected"] = wifiConnected;
    doc["wifi_rssi"] = wifiRSSI;
    doc["ir_hardware_ready"] = irHardwareReady;
    doc["active_clients"] = activeClients;
    doc["total_signals"] = totalSignals;
    doc["active_tasks"] = activeTasks;
    doc["health"] = static_cast<int>(health);

    return doc;
}
